{"name": "react-native-tab-view", "description": "Tab view component for React Native", "version": "4.1.3", "keywords": ["react-native-component", "react-component", "react-native", "ios", "android", "tab", "swipe", "scrollable", "coverflow"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/react-native-tab-view"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/tab-view/", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"use-latest-callback": "^0.2.4"}, "devDependencies": {"@jest/globals": "^30.0.0", "@testing-library/react-native": "^13.2.0", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-builder-bob": "^0.40.12", "react-native-pager-view": "6.8.1", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">= 18.2.0", "react-native": "*", "react-native-pager-view": ">= 6.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "c8c001f3044013e16977fdff55d1a6aaa3c41d3f"}