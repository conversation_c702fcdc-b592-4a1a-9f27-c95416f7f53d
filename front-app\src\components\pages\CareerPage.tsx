import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { Download, Fish, Calendar, TrendingUp, MapPin, Award, Star, Plus } from 'lucide-react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  withSpring
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { TopNavigation } from '../layout/TopNavigation';
import type { PageType } from '../AppWrapper';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface CareerPageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  appState: any;
}

export function CareerPage({ navigateToPage }: CareerPageProps) {
  const overviewOpacity = useSharedValue(0);
  const monthlyOpacity = useSharedValue(0);
  const timelineOpacity = useSharedValue(0);
  const achievementOpacity = useSharedValue(0);
  const badgeScale = useSharedValue(0);
  const progressWidth = useSharedValue(0);

  React.useEffect(() => {
    overviewOpacity.value = withDelay(100, withTiming(1, { duration: 600 }));
    badgeScale.value = withDelay(200, withSpring(1));
    monthlyOpacity.value = withDelay(600, withTiming(1, { duration: 600 }));
    timelineOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
    achievementOpacity.value = withDelay(1000, withTiming(1, { duration: 600 }));
    progressWidth.value = withDelay(1000, withTiming(0.75, { duration: 1000 }));
  }, []);

  const overviewStyle = useAnimatedStyle(() => ({
    opacity: overviewOpacity.value,
    transform: [{ translateY: withTiming(overviewOpacity.value === 1 ? 0 : 20) }],
  }));

  const badgeStyle = useAnimatedStyle(() => ({
    transform: [{ scale: badgeScale.value }],
  }));

  const monthlyStyle = useAnimatedStyle(() => ({
    opacity: monthlyOpacity.value,
    transform: [{ translateY: withTiming(monthlyOpacity.value === 1 ? 0 : 20) }],
  }));

  const timelineStyle = useAnimatedStyle(() => ({
    opacity: timelineOpacity.value,
    transform: [{ translateY: withTiming(timelineOpacity.value === 1 ? 0 : 20) }],
  }));

  const achievementStyle = useAnimatedStyle(() => ({
    opacity: achievementOpacity.value,
    transform: [{ translateY: withTiming(achievementOpacity.value === 1 ? 0 : 20) }],
  }));

  const progressStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value * 100}%`,
  }));

  const careerStats = {
    level: 15,
    experience: 75,
    title: '钓鱼大师',
    fishingAge: '2年',
    totalCatch: 128,
    speciesCount: 32,
    fishingDays: 85,
    spotsCount: 25
  };

  const monthlyStats = {
    catchCount: 15,
    fishingDays: 8,
    successRate: 75
  };

  const weeklyTrend = [
    { week: '1周', count: 3 },
    { week: '2周', count: 6 },
    { week: '3周', count: 8 },
    { week: '4周', count: 4 }
  ];

  const timelineData = [
    {
      month: '2024年1月',
      recordCount: 15,
      days: [
        {
          date: '1月15日',
          label: '今天',
          records: [
            {
              id: 1,
              fish: '鲫鱼',
              image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=50&h=50&fit=crop&auto=format',
              location: '东湖公园钓点',
              time: '14:30',
              weight: '0.8kg',
              length: '25cm',
              weather: '多云 15°C',
              points: '+50',
              isNew: true
            },
            {
              id: 2,
              fish: '草鱼',
              image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=50&h=50&fit=crop&auto=format',
              location: '西湖钓点',
              time: '09:15',
              weight: '1.2kg',
              length: '35cm',
              weather: '晴 18°C',
              points: '+35',
              isNew: false
            }
          ]
        },
        {
          date: '1月14日',
          label: '',
          records: [
            {
              id: 3,
              fish: '鲤鱼',
              image: 'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=50&h=50&fit=crop&auto=format',
              location: '北河钓场',
              time: '16:20',
              weight: '2.8kg',
              length: '45cm',
              weather: '阴 12°C',
              points: '+80',
              isRecord: true
            }
          ],
          hasMore: 2
        }
      ]
    }
  ];

  const viewTimelineRecord = (recordId: number) => {
    Alert.alert('记录详情', `查看时间线记录详情 #${recordId}`);
  };

  const expandTimelineDay = (dayIndex: number) => {
    Alert.alert('展开记录', `展开第${dayIndex}天的更多记录`);
  };

  const exportCareerData = () => {
    Alert.alert('导出数据', '生涯数据导出功能：将生成PDF报告');
  };

  const maxWeeklyCount = Math.max(...weeklyTrend.map(w => w.count));

  return (
    <ScrollView 
      className="flex-1 bg-page-bg" 
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <View>
        <TopNavigation
          title="我的生涯"
          showBack
          onBack={() => navigateToPage('profile')}
          rightIcon="download"
          onRightClick={exportCareerData}
        />

        {/* 生涯概览 */}
        <AnimatedView style={overviewStyle} className="px-6 py-4">
          <View className="bg-white p-6 rounded-xl shadow-sm">
            <View className="items-center mb-6">
              <AnimatedView style={badgeStyle}>
                <LinearGradient
                  colors={['#00A79D', '#0EA5E9']}
                  className="w-20 h-20 rounded-full items-center justify-center mb-3"
                >
                  <Fish color="white" size={32} />
                </LinearGradient>
              </AnimatedView>
              <Text className="text-xl font-bold text-primary-text mb-1">
                {careerStats.title}
              </Text>
              <Text className="text-sm text-secondary-text">
                钓龄 {careerStats.fishingAge} • 等级 {careerStats.level}
              </Text>
            </View>

            {/* 生涯统计 */}
            <View className="flex-row flex-wrap mb-4">
              {[
                { value: careerStats.totalCatch, label: '总钓获', color: '#00A79D' },
                { value: careerStats.speciesCount, label: '鱼种数', color: '#FFC759' },
                { value: careerStats.fishingDays, label: '钓鱼天数', color: '#121212' },
                { value: careerStats.spotsCount, label: '钓点数', color: '#00A79D' }
              ].map((stat, index) => (
                <View key={stat.label} className="w-1/2 p-2">
                  <View className="items-center p-3 bg-light-slate rounded-lg">
                    <Text 
                      className="text-2xl font-bold"
                      style={{ color: stat.color }}
                    >
                      {stat.value}
                    </Text>
                    <Text className="text-xs text-secondary-text">{stat.label}</Text>
                  </View>
                </View>
              ))}
            </View>

            {/* 生涯进度 */}
            <View>
              <View className="flex-row items-center justify-between mb-2">
                <Text className="text-sm text-secondary-text">下一等级进度</Text>
                <Text className="text-sm font-medium text-primary-text">{careerStats.experience}%</Text>
              </View>
              <View className="w-full bg-light-slate rounded-full h-3">
                <AnimatedView 
                  style={[progressStyle, { backgroundColor: '#00A79D', height: 12, borderRadius: 6 }]}
                />
              </View>
              <Text className="text-xs text-secondary-text mt-1">
                还需 12 次钓获升级到 {careerStats.level + 1} 级
              </Text>
            </View>
          </View>
        </AnimatedView>

        {/* 月度回顾 */}
        <AnimatedView style={monthlyStyle} className="px-6 py-4">
          <View className="bg-white p-4 rounded-xl shadow-sm">
            <Text className="text-lg font-bold text-primary-text mb-4">📊 本月回顾</Text>
            <View className="flex-row justify-around mb-4">
              {[
                { value: monthlyStats.catchCount, label: '本月钓获', color: '#00A79D' },
                { value: monthlyStats.fishingDays, label: '出钓天数', color: '#FFC759' },
                { value: `${monthlyStats.successRate}%`, label: '成功率', color: '#121212' }
              ].map((stat) => (
                <View key={stat.label} className="items-center">
                  <Text 
                    className="text-xl font-bold"
                    style={{ color: stat.color }}
                  >
                    {stat.value}
                  </Text>
                  <Text className="text-xs text-secondary-text">{stat.label}</Text>
                </View>
              ))}
            </View>
            
            {/* 月度趋势图 */}
            <View className="h-16 bg-light-slate rounded-lg p-2">
              <View className="flex-row items-end justify-between h-full">
                {weeklyTrend.map((data, index) => {
                  const height = (data.count / maxWeeklyCount) * 100;
                  const isMax = data.count === maxWeeklyCount;
                  return (
                    <View key={index} className="items-center">
                      <View 
                        className={`w-4 rounded-t ${isMax ? 'bg-sunrise-gold' : 'bg-aqua-teal'}`}
                        style={{ height: `${height}%` }}
                      />
                      <Text className="text-xs text-secondary-text mt-1">{data.week}</Text>
                    </View>
                  );
                })}
              </View>
            </View>
          </View>
        </AnimatedView>

        {/* 时间线记录 */}
        <AnimatedView style={timelineStyle} className="px-6 py-4">
          <View className="bg-white rounded-xl shadow-sm overflow-hidden">
            <View className="p-4 border-b border-gray-100">
              <View className="flex-row items-center justify-between">
                <Text className="text-lg font-bold text-primary-text">🕒 时间线</Text>
                <View className="flex-row items-center gap-x-2">
                  <View className="bg-aqua-teal px-3 py-1 rounded-full">
                    <Text className="text-xs text-white">全部</Text>
                  </View>
                  <TouchableOpacity className="border border-gray-200 px-3 py-1 rounded-full">
                    <Text className="text-xs text-secondary-text">本月</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* 时间线内容 */}
            <View className="p-4">
              {timelineData.map((month, monthIndex) => (
                <View key={month.month} className="mb-6">
                  <View className="flex-row items-center mb-4">
                    <View className="w-3 h-3 bg-sunrise-gold rounded-full mr-3" />
                    <Text className="text-lg font-bold text-primary-text">{month.month}</Text>
                    <View className="flex-1 h-px bg-gray-200 mx-3" />
                    <Text className="text-sm text-secondary-text">{month.recordCount}条记录</Text>
                  </View>

                  {/* 时间线记录项 */}
                  <View className="ml-6 gap-y-4">
                    {month.days.map((day, dayIndex) => (
                      <View key={dayIndex}>
                        <View className="flex-row items-center mb-2">
                          <View className="w-2 h-2 bg-aqua-teal rounded-full mr-3 -ml-4" />
                          <Text className="text-sm font-medium text-primary-text">
                            {day.date} {day.label}
                          </Text>
                          <View className="flex-1" />
                          <Text className="text-xs text-secondary-text">{day.records.length}条记录</Text>
                        </View>
                        
                        {/* 记录卡片 */}
                        <View className="ml-2 gap-y-3">
                          {day.records.map((record) => (
                            <TouchableOpacity
                              key={record.id}
                              className="bg-light-slate p-3 rounded-lg"
                              onPress={() => viewTimelineRecord(record.id)}
                            >
                              <View className="flex-row items-start gap-x-3">
                                <Image
                                  source={{ uri: record.image }}
                                  className="w-10 h-10 rounded-lg"
                                  resizeMode="cover"
                                />
                                <View className="flex-1">
                                  <View className="flex-row items-center gap-x-2 mb-1">
                                    <Text className="text-sm font-medium text-primary-text">{record.fish}</Text>
                                    {record.isNew && (
                                      <View className="bg-aqua-teal px-2 py-1 rounded">
                                        <Text className="text-xs text-white">新解锁</Text>
                                      </View>
                                    )}
                                    {record.isRecord && (
                                      <View className="bg-sunrise-gold px-2 py-1 rounded">
                                        <Text className="text-xs text-white">最大</Text>
                                      </View>
                                    )}
                                  </View>
                                  <Text className="text-xs text-secondary-text mb-1">
                                    {record.location} • {record.time}
                                  </Text>
                                  <View className="flex-row gap-x-3">
                                    <Text className="text-xs text-secondary-text">{record.weight}</Text>
                                    <Text className="text-xs text-secondary-text">{record.length}</Text>
                                    <Text className="text-xs text-secondary-text">{record.weather}</Text>
                                  </View>
                                </View>
                                <Text className="text-xs text-sunrise-gold">{record.points}</Text>
                              </View>
                            </TouchableOpacity>
                          ))}

                          {/* 展开更多 */}
                          {day.hasMore && (
                            <TouchableOpacity 
                              className="items-center py-2"
                              onPress={() => expandTimelineDay(dayIndex)}
                            >
                              <View className="flex-row items-center gap-x-1">
                                <Text className="text-xs text-aqua-teal">
                                  展开其余 {day.hasMore} 条记录
                                </Text>
                                <Plus color="#00A79D" size={12} />
                              </View>
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              ))}

              {/* 加载更多 */}
              <TouchableOpacity className="items-center py-4">
                <View className="flex-row items-center gap-x-1">
                  <Text className="text-aqua-teal text-sm font-medium">加载更多记录</Text>
                  <TrendingUp color="#00A79D" size={14} />
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </AnimatedView>

        {/* 成就徽章 */}
        <AnimatedView style={achievementStyle} className="px-6 py-4">
          <LinearGradient
            colors={['rgba(0,167,157,0.1)', 'rgba(255,199,89,0.1)']}
            className="p-4 rounded-xl border border-aqua-teal/20"
          >
            <View className="flex-row items-center space-x-2 mb-3">
              <Award color="#00A79D" size={20} />
              <Text className="text-sm font-medium text-primary-text">本月成就</Text>
            </View>
            <View className="flex-row justify-around">
              {[
                { icon: Fish, label: '鱼种收集者', achieved: true },
                { icon: Calendar, label: '连续记录', achieved: true },
                { icon: Star, label: '完美一周', achieved: false }
              ].map((achievement) => {
                const Icon = achievement.icon;
                return (
                  <TouchableOpacity key={achievement.label} className="items-center">
                    <LinearGradient
                      colors={achievement.achieved ? ['#FFC759', '#F59E0B'] : ['#E5E7EB', '#E5E7EB']}
                      className="w-8 h-8 rounded-full items-center justify-center mb-1"
                    >
                      <Icon 
                        color={achievement.achieved ? 'white' : '#9CA3AF'} 
                        size={14} 
                      />
                    </LinearGradient>
                    <Text className={`text-xs ${
                      achievement.achieved ? 'text-secondary-text' : 'text-gray-400'
                    }`}>
                      {achievement.label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </LinearGradient>
        </AnimatedView>
      </View>
    </ScrollView>
  );
}