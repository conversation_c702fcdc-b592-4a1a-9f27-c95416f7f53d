<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钓鱼生涯记录APP - UI原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ocean-blue': '#1A2E40',
                        'light-slate': '#F0F2F5',
                        'sunrise-gold': '#FFC759',
                        'aqua-teal': '#00A79D',
                        'primary-text': '#121212',
                        'secondary-text': '#6B7280',
                        'divider': '#E5E7EB'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1A2E40 0%, #203347 100%);
        }
        .fish-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
        }
        .fish-card {
            aspect-ratio: 1;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
        }
        .fish-unlocked {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #00A79D;
            transition: all 0.3s ease;
        }
        
        .fish-unlocked:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 24px rgba(0, 167, 157, 0.3);
            border-color: #00A79D;
        }
        
        .fish-locked {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            border: 2px solid #e5e7eb;
        }
        .stats-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .record-button {
            background: linear-gradient(135deg, #FFC759 0%, #FFB840 100%);
            box-shadow: 0 8px 24px rgba(255, 199, 89, 0.4);
        }
        .nav-active {
            color: #FFC759;
        }
        .nav-inactive {
            color: #6B7280;
        }
        
        /* 扫描动画样式 */
        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, #00A79D, transparent);
            animation: scan-move 2s linear infinite;
        }
        
        @keyframes scan-move {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(192px); opacity: 0.3; }
        }
        
        /* 雷达扫描效果 */
        .radar-pulse {
            width: 100px;
            height: 100px;
            border: 2px solid #FFC759;
            border-radius: 50%;
            opacity: 0.7;
            animation: radar-pulse 2s ease-in-out infinite;
        }
        
        @keyframes radar-pulse {
            0% { transform: scale(0.3); opacity: 1; }
            70% { transform: scale(1); opacity: 0.3; }
            100% { transform: scale(1.2); opacity: 0; }
        }
        
        /* 加载旋转器 */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #E5E7EB;
            border-left: 2px solid #00A79D;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 解锁动画 */
        .unlock-animation {
            animation: unlock-scale 1s ease-in-out;
        }
        
        @keyframes unlock-scale {
            0% { transform: scale(0) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
            100% { transform: scale(1) rotate(360deg); }
        }
        
        /* 照片预览样式 */
        .photo-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #E5E7EB;
        }
        
        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .photo-item .remove-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background: #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            cursor: pointer;
        }
        
        /* 步骤内容动画 */
        .step-content {
            animation: fadeInUp 0.5s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 成功庆祝动画 */
        .success-celebration {
            position: relative;
        }
        
        .confetti-container {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 200px;
            pointer-events: none;
        }
        
        .confetti {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #FFC759;
            border-radius: 50%;
            animation: confetti-fall 2s ease-out infinite;
        }
        
        .confetti:nth-child(1) {
            left: 10%;
            animation-delay: 0s;
            background: #FFC759;
        }
        
        .confetti:nth-child(2) {
            left: 30%;
            animation-delay: 0.3s;
            background: #00A79D;
        }
        
        .confetti:nth-child(3) {
            left: 50%;
            animation-delay: 0.6s;
            background: #FFC759;
        }
        
        .confetti:nth-child(4) {
            left: 70%;
            animation-delay: 0.9s;
            background: #00A79D;
        }
        
        .confetti:nth-child(5) {
            left: 90%;
            animation-delay: 1.2s;
            background: #FFC759;
        }
        
        @keyframes confetti-fall {
            0% {
                transform: translateY(-50px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(150px) rotate(360deg);
                opacity: 0;
            }
        }
        
        /* 成功页面进入动画 */
        .success-enter {
            animation: successSlideIn 0.8s ease-out;
        }
        
        @keyframes successSlideIn {
            0% {
                opacity: 0;
                transform: translateY(50px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 照片展示样式 */
        .photo-gallery {
            position: relative;
        }
        
        .main-photo {
            position: relative;
            overflow: hidden;
        }
        
        .main-photo img {
            transition: transform 0.3s ease;
        }
        
        .main-photo:hover img {
            transform: scale(1.02);
        }
        
        .thumbnail-item img {
            transition: all 0.3s ease;
        }
        
        .thumbnail-item img:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .thumbnail-item img.active {
            border-color: #00A79D !important;
            box-shadow: 0 0 0 2px rgba(0, 167, 157, 0.3);
        }
        
        /* 照片模态框样式 */
        #photo-modal {
            backdrop-filter: blur(4px);
            animation: modalFadeIn 0.3s ease-out;
        }
        
        @keyframes modalFadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        #photo-modal img {
            max-height: 70vh;
            animation: photoZoomIn 0.3s ease-out;
        }
        
        @keyframes photoZoomIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .modal-thumbnail {
            width: 50px;
            height: 50px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .modal-thumbnail:hover {
            transform: scale(1.1);
        }
        
        .modal-thumbnail.active {
            border-color: #FFC759;
            box-shadow: 0 0 0 2px rgba(255, 199, 89, 0.3);
        }
        
        /* 时间线样式 */
        .timeline-month {
            position: relative;
        }
        
        .timeline-day {
            position: relative;
        }
        
        .timeline-day::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 24px;
            bottom: -16px;
            width: 1px;
            background: linear-gradient(to bottom, #00A79D, transparent);
        }
        
        .timeline-day:last-child::before {
            display: none;
        }
        
        .timeline-record {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .timeline-record:hover {
            border-left-color: #00A79D;
            transform: translateX(4px);
        }
        
        /* 生涯页面特殊样式 */
        .career-badge {
            background: linear-gradient(135deg, #00A79D 0%, #FFC759 100%);
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulse-glow {
            from {
                box-shadow: 0 0 20px rgba(0, 167, 157, 0.3);
            }
            to {
                box-shadow: 0 0 30px rgba(255, 199, 89, 0.4);
            }
        }
        
        /* 地图标记脉冲动画 */
        .pulse-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid #00A79D;
            border-radius: 50%;
            opacity: 0.6;
            animation: pulse-ring 2s ease-out infinite;
        }
        
        @keyframes pulse-ring {
            0% {
                transform: translate(-50%, -50%) scale(0.5);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(2.5);
                opacity: 0;
            }
        }
        
        /* 钓点列表项悬停效果 */
        .fishing-spot-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-light-slate min-h-screen">
    <!-- 容器 -->
    <div class="max-w-md mx-auto bg-white shadow-2xl min-h-screen relative">
        
        <!-- 首页 -->
        <div id="homepage" class="page-content">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format" 
                             alt="头像" class="w-10 h-10 rounded-full border-2 border-sunrise-gold">
                        <div>
                            <h1 class="text-lg font-bold">渔友小明</h1>
                            <p class="text-sm opacity-80">钓龄 2年</p>
                        </div>
                    </div>
                    <i class="fas fa-bell text-xl"></i>
                </div>
            </div>

            <!-- 快速统计 -->
            <div class="px-6 py-4">
                <div class="grid grid-cols-3 gap-3">
                    <div class="stats-card p-4 rounded-xl text-center">
                        <div class="text-2xl font-bold text-primary-text">128</div>
                        <div class="text-sm text-secondary-text">总渔获</div>
                    </div>
                    <div class="stats-card p-4 rounded-xl text-center">
                        <div class="text-2xl font-bold text-aqua-teal">32</div>
                        <div class="text-sm text-secondary-text">鱼种数</div>
                    </div>
                    <div class="stats-card p-4 rounded-xl text-center">
                        <div class="text-2xl font-bold text-sunrise-gold">15</div>
                        <div class="text-sm text-secondary-text">本月记录</div>
                    </div>
                </div>
            </div>

            <!-- 记录渔获按钮 -->
            <div class="px-6 py-4">
                <button onclick="showPage('record')" class="record-button w-full py-4 rounded-2xl text-ocean-blue font-bold text-lg flex items-center justify-center space-x-2 hover:scale-105 transition-all">
                    <i class="fas fa-camera text-2xl"></i>
                    <span>记录渔获</span>
                </button>
            </div>

            <!-- 本周排行 -->
            <div class="px-6 py-4">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-primary-text">本周排行榜</h3>
                        <button onclick="showPage('leaderboard')" class="text-aqua-teal text-sm">查看全部</button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-sunrise-gold rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format" 
                                 alt="用户" class="w-8 h-8 rounded-full">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-primary-text">钓鱼大师</div>
                                <div class="text-xs text-secondary-text">草鱼 2.8kg</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-secondary-text rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                            <img src="https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=32&h=32&fit=crop&crop=face&auto=format" 
                                 alt="用户" class="w-8 h-8 rounded-full">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-primary-text">江边老钓</div>
                                <div class="text-xs text-secondary-text">鲤鱼 2.1kg</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近记录 -->
            <div class="px-6 py-4 pb-24">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <h3 class="text-lg font-bold text-primary-text mb-4">最近记录</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop&auto=format" 
                                 alt="鱼" class="w-15 h-15 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-primary-text">鲫鱼</span>
                                    <span class="text-xs bg-aqua-teal text-white px-2 py-1 rounded">新解锁</span>
                                </div>
                                <div class="text-xs text-secondary-text">重量: 0.8kg | 长度: 25cm</div>
                                <div class="text-xs text-secondary-text">今天 14:30 | 东湖钓点</div>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=60&h=60&fit=crop&auto=format" 
                                 alt="鱼" class="w-15 h-15 rounded-lg object-cover">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-primary-text">草鱼</div>
                                <div class="text-xs text-secondary-text">重量: 1.2kg | 长度: 35cm</div>
                                <div class="text-xs text-secondary-text">昨天 09:15 | 西湖钓点</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 记录渔获页面 -->
        <div id="record" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <button onclick="showPage('homepage')" class="text-xl">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-lg font-bold">记录渔获</h1>
                    <div class="text-sm">
                        <span id="step-indicator">步骤 1/4</span>
                    </div>
                </div>
            </div>

            <!-- 进度条 -->
            <div class="px-6 py-2">
                <div class="w-full bg-light-slate rounded-full h-2">
                    <div id="progress-bar" class="bg-aqua-teal h-2 rounded-full transition-all duration-500" style="width: 25%"></div>
                </div>
            </div>

            <!-- 第一步：拍照上传 -->
            <div id="step-1" class="step-content">
                <div class="px-6 py-6">
                    <div class="bg-white p-6 rounded-xl shadow-sm text-center">
                        <h3 class="text-lg font-bold text-primary-text mb-2">拍摄或上传渔获照片</h3>
                        <p class="text-sm text-secondary-text mb-6">可以上传多张照片，AI将自动选择最佳角度识别</p>
                        
                        <!-- 照片预览区域 -->
                        <div id="photo-preview" class="mb-6 hidden">
                            <div class="grid grid-cols-2 gap-3" id="photo-grid">
                                <!-- 照片缩略图将在这里显示 -->
                            </div>
                            <button onclick="clearPhotos()" class="mt-3 text-sm text-secondary-text underline">清除所有照片</button>
                        </div>

                        <!-- 默认上传区域 -->
                        <div id="upload-area" class="mb-6">
                            <div class="w-32 h-32 mx-auto bg-light-slate rounded-xl flex items-center justify-center border-2 border-dashed border-divider mb-4">
                                <i class="fas fa-camera text-4xl text-secondary-text"></i>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="simulatePhotoCapture()" class="bg-sunrise-gold text-white py-3 rounded-lg font-medium flex items-center justify-center space-x-2 hover:scale-105 transition-all">
                                <i class="fas fa-camera"></i>
                                <span>立即拍照</span>
                            </button>
                            <button onclick="simulatePhotoUpload()" class="bg-aqua-teal text-white py-3 rounded-lg font-medium flex items-center justify-center space-x-2 hover:scale-105 transition-all">
                                <i class="fas fa-image"></i>
                                <span>从相册选择</span>
                            </button>
                        </div>

                        <button id="next-step-1" onclick="goToStep(2)" class="w-full mt-4 py-3 bg-gray-300 text-gray-500 rounded-lg font-medium cursor-not-allowed" disabled>
                            下一步：AI识别
                        </button>
                    </div>
                </div>
            </div>

            <!-- 第二步：AI扫描 -->
            <div id="step-2" class="step-content hidden">
                <div class="px-6 py-6">
                    <div class="bg-white p-6 rounded-xl shadow-sm text-center">
                        <h3 class="text-lg font-bold text-primary-text mb-6">AI正在分析您的渔获...</h3>
                        
                        <!-- 扫描动画区域 -->
                        <div class="relative mb-6">
                            <div class="w-48 h-48 mx-auto rounded-xl overflow-hidden border-2 border-aqua-teal bg-light-slate">
                                <img id="scanning-image" src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=200&h=200&fit=crop&auto=format" 
                                     alt="扫描中的鱼" class="w-full h-full object-cover">
                                <!-- 扫描线条动画 -->
                                <div class="scan-line"></div>
                            </div>
                            
                            <!-- 雷达扫描效果 -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="radar-pulse"></div>
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center justify-center space-x-2">
                                <div class="loading-spinner"></div>
                                <span class="text-secondary-text">正在识别鱼种特征...</span>
                            </div>
                            <div class="text-xs text-secondary-text">
                                <div id="scan-progress">🔍 分析图像质量... <span class="text-aqua-teal">完成</span></div>
                                <div id="scan-progress-2" class="opacity-50">🧠 AI模型识别中...</div>
                                <div id="scan-progress-3" class="opacity-50">🎯 匹配数据库...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三步：识别结果与解锁特效 -->
            <div id="step-3" class="step-content hidden">
                <!-- 解锁特效覆盖层 -->
                <div id="unlock-overlay" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 hidden">
                    <div class="text-center text-white">
                        <div class="unlock-animation mb-4">
                            <div class="w-32 h-32 mx-auto bg-gradient-to-r from-sunrise-gold to-yellow-400 rounded-full flex items-center justify-center">
                                <i class="fas fa-star text-4xl animate-pulse"></i>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold mb-2 animate-bounce">🎉 新鱼种解锁！</h2>
                        <p class="text-lg mb-2">恭喜您发现了</p>
                        <p class="text-xl font-bold text-sunrise-gold mb-4">鲫鱼 (Crucian Carp)</p>
                        <p class="text-sm opacity-80">图鉴完成度：18% → 19%</p>
                        <button onclick="hideUnlockEffect()" class="mt-6 px-6 py-2 bg-sunrise-gold text-ocean-blue rounded-lg font-bold">
                            继续记录
                        </button>
                    </div>
                </div>

                <div class="px-6 py-6">
                    <div class="bg-white p-4 rounded-xl shadow-sm">
                        <h3 class="text-lg font-bold text-primary-text mb-4">识别完成！</h3>
                        
                        <!-- 主要识别结果 -->
                        <div class="mb-6">
                            <div class="flex items-center space-x-4 p-4 bg-gradient-to-r from-aqua-teal from-opacity-10 to-sunrise-gold to-opacity-10 rounded-xl border-2 border-aqua-teal">
                                <div class="relative">
                                    <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=80&h=80&fit=crop&auto=format" 
                                         alt="识别的鱼" class="w-16 h-16 rounded-lg object-cover">
                                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center">
                                        <i class="fas fa-star text-white text-xs"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="text-lg font-bold text-primary-text">鲫鱼</div>
                                    <div class="text-sm text-secondary-text">Crucian Carp</div>
                                    <div class="text-xs text-aqua-teal font-medium">置信度: 96%</div>
                                </div>
                                <div class="text-center">
                                    <i class="fas fa-check-circle text-aqua-teal text-2xl"></i>
                                    <div class="text-xs text-secondary-text mt-1">已确认</div>
                                </div>
                            </div>
                        </div>

                        <!-- 其他可能选项 -->
                        <div class="mb-6">
                            <div class="text-sm text-secondary-text mb-3">其他可能选项:</div>
                            <div class="space-y-2">
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="selectAlternative('鲤鱼')">
                                    <img src="https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=40&h=40&fit=crop&auto=format" 
                                         alt="鲤鱼" class="w-10 h-10 rounded-lg object-cover">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-primary-text">鲤鱼</div>
                                        <div class="text-xs text-secondary-text">置信度: 78%</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="selectAlternative('草鱼')">
                                    <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=40&h=40&fit=crop&auto=format" 
                                         alt="草鱼" class="w-10 h-10 rounded-lg object-cover">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-primary-text">草鱼</div>
                                        <div class="text-xs text-secondary-text">置信度: 65%</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button onclick="goToStep(4)" class="w-full py-3 bg-sunrise-gold text-white rounded-lg font-bold hover:scale-105 transition-all">
                            确认识别结果，继续填写信息
                        </button>
                    </div>
                </div>
            </div>

            <!-- 第四步：补充信息 -->
            <div id="step-4" class="step-content hidden">
                <div class="px-6 py-6 pb-24">
                    <div class="bg-white p-4 rounded-xl shadow-sm">
                        <h3 class="text-lg font-bold text-primary-text mb-4">补充详细信息</h3>
                        
                        <!-- 已识别的鱼种信息 -->
                        <div class="mb-6 p-3 bg-light-slate rounded-lg">
                            <div class="flex items-center space-x-3">
                                <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=50&h=50&fit=crop&auto=format" 
                                     alt="鲫鱼" class="w-12 h-12 rounded-lg object-cover">
                                <div>
                                    <div class="font-medium text-primary-text">鲫鱼 (Crucian Carp)</div>
                                    <div class="text-sm text-secondary-text">AI识别 - 96% 置信度</div>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm text-secondary-text mb-1">重量 (kg) *</label>
                                    <input type="number" step="0.1" placeholder="0.8" 
                                           class="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none">
                                </div>
                                <div>
                                    <label class="block text-sm text-secondary-text mb-1">长度 (cm)</label>
                                    <input type="number" placeholder="25" 
                                           class="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none">
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm text-secondary-text mb-1">钓组配置</label>
                                <input type="text" placeholder="3号主线 + 2号子线 + 4号钩" 
                                       class="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none">
                            </div>
                            
                            <div>
                                <label class="block text-sm text-secondary-text mb-1">使用饵料</label>
                                <input type="text" placeholder="玉米粒、面包虫" 
                                       class="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none">
                            </div>
                            
                            <div>
                                <label class="block text-sm text-secondary-text mb-1">钓鱼心得</label>
                                <textarea placeholder="今天天气不错，鱼情活跃，在水草边上钓获..." rows="3"
                                          class="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none resize-none"></textarea>
                            </div>
                            
                            <!-- 自动记录信息 -->
                            <div class="p-4 bg-gradient-to-r from-light-slate to-white rounded-lg border border-divider">
                                <div class="text-sm font-medium text-primary-text mb-3">📍 自动记录信息</div>
                                <div class="space-y-2 text-sm text-secondary-text">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-calendar-alt text-aqua-teal"></i>
                                        <span>2024年1月15日 14:30</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-map-marker-alt text-aqua-teal"></i>
                                        <span>东湖公园钓点 (精确位置已加密)</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-cloud-sun text-aqua-teal"></i>
                                        <span>多云转晴 15°C，微风2级</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-water text-aqua-teal"></i>
                                        <span>水温约12°C，适宜垂钓</span>
                                    </div>
                                </div>
                            </div>
                            
                            <button onclick="saveRecord()" class="record-button w-full py-4 rounded-xl text-ocean-blue font-bold text-lg hover:scale-105 transition-all">
                                <i class="fas fa-save mr-2"></i>
                                保存到渔获日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保存成功页面 -->
        <div id="success" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <button onclick="showPage('homepage')" class="text-xl">
                        <i class="fas fa-home"></i>
                    </button>
                    <h1 class="text-lg font-bold">记录完成</h1>
                    <button onclick="shareRecord()" class="text-xl">
                        <i class="fas fa-share-alt"></i>
                    </button>
                </div>
            </div>

            <!-- 成功反馈动画 -->
            <div class="px-6 py-6">
                <div class="bg-white p-6 rounded-xl shadow-sm text-center">
                    <div class="success-celebration mb-4">
                        <div class="w-24 h-24 mx-auto bg-gradient-to-r from-sunrise-gold to-yellow-400 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-check text-white text-4xl animate-bounce"></i>
                        </div>
                        <div class="confetti-container">
                            <div class="confetti"></div>
                            <div class="confetti"></div>
                            <div class="confetti"></div>
                            <div class="confetti"></div>
                            <div class="confetti"></div>
                        </div>
                    </div>
                    <h2 class="text-2xl font-bold text-primary-text mb-2">🎉 记录成功！</h2>
                    <p class="text-lg text-secondary-text mb-4">您的渔获已成功添加到日志</p>
                    <div class="flex items-center justify-center space-x-2 text-sm text-aqua-teal">
                        <i class="fas fa-trophy"></i>
                        <span>获得经验值 +50</span>
                        <span>•</span>
                        <span>连续记录 +1</span>
                    </div>
                </div>
            </div>

            <!-- 生成的完整日志 -->
            <div class="px-6 py-4">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="p-4 border-b border-divider">
                        <h3 class="text-lg font-bold text-primary-text mb-2">您的渔获日志</h3>
                        <div class="text-sm text-secondary-text">
                            <i class="fas fa-calendar-alt mr-1"></i>
                            2024年1月15日 14:30
                        </div>
                    </div>

                    <!-- 主要内容区域 -->
                    <div class="p-4">
                        <!-- 原始照片展示 -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-medium text-primary-text">📸 原始照片</h4>
                                <span class="text-xs text-secondary-text" id="photo-count">3张照片</span>
                            </div>
                            <div class="photo-gallery" id="success-photo-gallery">
                                <!-- 主要照片 -->
                                <div class="main-photo mb-3 relative">
                                    <img id="main-photo" src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop&auto=format" 
                                         alt="主要照片" class="w-full h-48 rounded-xl object-cover border-2 border-aqua-teal cursor-pointer" 
                                         onclick="openPhotoModal(0)">
                                    <div class="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                                        主要照片
                                    </div>
                                    <div class="absolute top-2 right-2 bg-white bg-opacity-20 text-white p-2 rounded-full">
                                        <i class="fas fa-expand text-sm"></i>
                                    </div>
                                </div>
                                
                                <!-- 缩略图列表 -->
                                <div class="grid grid-cols-4 gap-2" id="thumbnail-grid">
                                    <div class="thumbnail-item relative">
                                        <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=150&h=150&fit=crop&auto=format" 
                                             alt="照片1" class="w-full aspect-square rounded-lg object-cover border-2 border-aqua-teal cursor-pointer"
                                             onclick="switchMainPhoto(0, this.src)">
                                        <div class="absolute top-1 right-1 w-3 h-3 bg-aqua-teal rounded-full"></div>
                                    </div>
                                    <div class="thumbnail-item relative">
                                        <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=150&h=150&fit=crop&auto=format" 
                                             alt="照片2" class="w-full aspect-square rounded-lg object-cover border border-divider cursor-pointer"
                                             onclick="switchMainPhoto(1, this.src)">
                                    </div>
                                    <div class="thumbnail-item relative">
                                        <img src="https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=150&h=150&fit=crop&auto=format" 
                                             alt="照片3" class="w-full aspect-square rounded-lg object-cover border border-divider cursor-pointer"
                                             onclick="switchMainPhoto(2, this.src)">
                                    </div>
                                    <div class="add-more-placeholder">
                                        <div class="w-full aspect-square rounded-lg border-2 border-dashed border-divider bg-light-slate flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-colors"
                                             onclick="showPage('record')">
                                            <i class="fas fa-plus text-secondary-text"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 鱼种信息卡片 -->
                        <div class="flex items-start space-x-4 mb-6">
                            <div class="relative">
                                <img id="log-fish-image" src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format" 
                                     alt="鲫鱼" class="w-24 h-24 rounded-xl object-cover border-2 border-aqua-teal">
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center">
                                    <i class="fas fa-star text-white text-xs"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-1">
                                    <h4 class="text-xl font-bold text-primary-text">鲫鱼</h4>
                                    <span class="text-xs bg-aqua-teal text-white px-2 py-1 rounded-full">新解锁</span>
                                </div>
                                <div class="text-sm text-secondary-text mb-2">Crucian Carp</div>
                                <div class="grid grid-cols-2 gap-2 text-sm">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-weight-hanging text-aqua-teal"></i>
                                        <span>0.8 kg</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-ruler text-aqua-teal"></i>
                                        <span>25 cm</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 钓鱼详情 -->
                        <div class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-light-slate p-3 rounded-lg">
                                    <div class="text-xs text-secondary-text mb-1">钓组配置</div>
                                    <div class="text-sm font-medium text-primary-text">3号主线 + 2号子线 + 4号钩</div>
                                </div>
                                <div class="bg-light-slate p-3 rounded-lg">
                                    <div class="text-xs text-secondary-text mb-1">使用饵料</div>
                                    <div class="text-sm font-medium text-primary-text">玉米粒、面包虫</div>
                                </div>
                            </div>

                            <!-- 环境信息 -->
                            <div class="bg-gradient-to-r from-light-slate to-white p-4 rounded-lg border border-divider">
                                <div class="text-sm font-medium text-primary-text mb-3">📍 钓鱼环境</div>
                                <div class="grid grid-cols-2 gap-3 text-xs text-secondary-text">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-map-marker-alt text-aqua-teal"></i>
                                        <span>东湖公园钓点</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-cloud-sun text-aqua-teal"></i>
                                        <span>多云转晴 15°C</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-wind text-aqua-teal"></i>
                                        <span>微风2级</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-water text-aqua-teal"></i>
                                        <span>水温约12°C</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 钓鱼心得 -->
                            <div class="bg-white border border-divider p-4 rounded-lg">
                                <div class="text-sm font-medium text-primary-text mb-2">💭 钓鱼心得</div>
                                <div class="text-sm text-secondary-text leading-relaxed">
                                    "今天天气不错，鱼情活跃，在水草边上钓获了这条漂亮的鲫鱼。使用玉米粒作饵效果很好，建议其他钓友也可以尝试这个钓点。"
                                </div>
                            </div>

                            <!-- AI分析摘要 -->
                            <div class="bg-gradient-to-r from-aqua-teal from-opacity-10 to-sunrise-gold to-opacity-10 p-4 rounded-lg border border-aqua-teal">
                                <div class="flex items-center space-x-2 mb-2">
                                    <i class="fas fa-robot text-aqua-teal"></i>
                                    <span class="text-sm font-medium text-primary-text">AI分析摘要</span>
                                </div>
                                <div class="text-xs text-secondary-text leading-relaxed">
                                    本次渔获的鲫鱼体型适中，在当前季节和水温条件下属于正常表现。建议继续在相似环境下使用相同钓组和饵料配置，成功率较高。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部操作区 -->
                    <div class="p-4 bg-light-slate border-t border-divider">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4 text-sm text-secondary-text">
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-eye"></i>
                                    <span>仅自己可见</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-bookmark"></i>
                                    <span>已保存</span>
                                </div>
                            </div>
                            <div class="text-xs text-secondary-text">
                                日志编号: #2024011501
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="showPage('record')" class="bg-aqua-teal text-white py-3 rounded-lg font-medium flex items-center justify-center space-x-2 hover:scale-105 transition-all">
                                <i class="fas fa-plus"></i>
                                <span>继续记录</span>
                            </button>
                            <button onclick="shareRecord()" class="bg-sunrise-gold text-white py-3 rounded-lg font-medium flex items-center justify-center space-x-2 hover:scale-105 transition-all">
                                <i class="fas fa-share-alt"></i>
                                <span>分享日志</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐下一步 -->
            <div class="px-6 py-4 pb-24">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <h3 class="text-lg font-bold text-primary-text mb-4">🎯 推荐下一步</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 p-3 bg-light-slate rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="showPage('fishdex')">
                            <div class="w-10 h-10 bg-aqua-teal bg-opacity-20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book text-aqua-teal"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-primary-text">查看鱼种图鉴</div>
                                <div class="text-xs text-secondary-text">探索更多鱼种，完成收集</div>
                            </div>
                            <i class="fas fa-chevron-right text-secondary-text"></i>
                        </div>
                        <div class="flex items-center space-x-3 p-3 bg-light-slate rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="showPage('leaderboard')">
                            <div class="w-10 h-10 bg-sunrise-gold bg-opacity-20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-trophy text-sunrise-gold"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-primary-text">查看排行榜</div>
                                <div class="text-xs text-secondary-text">看看您在地区的排名</div>
                            </div>
                            <i class="fas fa-chevron-right text-secondary-text"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 鱼种图鉴页面 -->
        <div id="fishdex" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <h1 class="text-lg font-bold">鱼种图鉴</h1>
                    <div class="text-sm">
                        <span class="text-sunrise-gold">32</span> / 180
                    </div>
                </div>
            </div>

            <!-- 进度条 -->
            <div class="px-6 py-4">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-secondary-text">收集进度</span>
                        <span class="text-sm font-medium text-primary-text">17.8%</span>
                    </div>
                    <div class="w-full bg-light-slate rounded-full h-3">
                        <div class="bg-aqua-teal h-3 rounded-full" style="width: 17.8%"></div>
                    </div>
                </div>
            </div>

            <!-- 分类筛选 -->
            <div class="px-6 py-2">
                <div class="flex space-x-2 overflow-x-auto">
                    <button class="px-4 py-2 bg-aqua-teal text-white rounded-full text-sm whitespace-nowrap">全部</button>
                    <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm whitespace-nowrap">淡水鱼</button>
                    <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm whitespace-nowrap">海水鱼</button>
                    <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm whitespace-nowrap">已解锁</button>
                </div>
            </div>

            <!-- 鱼种网格 -->
            <div class="px-6 py-4 pb-24">
                <div class="fish-grid">
                    <!-- 已解锁的鱼种 -->
                    <div class="fish-card fish-unlocked cursor-pointer" onclick="showFishDetail('crucian', '鲫鱼', 'Crucian Carp')">
                        <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=80&h=80&fit=crop&auto=format" 
                             alt="鲫鱼" class="w-16 h-16 object-cover rounded-lg">
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-white text-xs"></i>
                        </div>
                        <div class="absolute bottom-1 left-1 right-1 bg-black bg-opacity-70 text-white text-xs text-center py-1 rounded">
                            鲫鱼
                        </div>
                    </div>
                    <div class="fish-card fish-unlocked cursor-pointer" onclick="showFishDetail('grass-carp', '草鱼', 'Grass Carp')">
                        <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=80&h=80&fit=crop&auto=format" 
                             alt="草鱼" class="w-16 h-16 object-cover rounded-lg">
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-white text-xs"></i>
                        </div>
                        <div class="absolute bottom-1 left-1 right-1 bg-black bg-opacity-70 text-white text-xs text-center py-1 rounded">
                            草鱼
                        </div>
                    </div>
                    <div class="fish-card fish-unlocked cursor-pointer" onclick="showFishDetail('carp', '鲤鱼', 'Common Carp')">
                        <img src="https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=80&h=80&fit=crop&auto=format" 
                             alt="鲤鱼" class="w-16 h-16 object-cover rounded-lg">
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-white text-xs"></i>
                        </div>
                        <div class="absolute bottom-1 left-1 right-1 bg-black bg-opacity-70 text-white text-xs text-center py-1 rounded">
                            鲤鱼
                        </div>
                    </div>
                    
                    <!-- 未解锁的鱼种 -->
                    <div class="fish-card fish-locked">
                        <div class="w-16 h-16 bg-secondary-text bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-question text-secondary-text text-2xl"></i>
                        </div>
                    </div>
                    <div class="fish-card fish-locked">
                        <div class="w-16 h-16 bg-secondary-text bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-question text-secondary-text text-2xl"></i>
                        </div>
                    </div>
                    <div class="fish-card fish-locked">
                        <div class="w-16 h-16 bg-secondary-text bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-question text-secondary-text text-2xl"></i>
                        </div>
                    </div>
                    <div class="fish-card fish-locked">
                        <div class="w-16 h-16 bg-secondary-text bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-question text-secondary-text text-2xl"></i>
                        </div>
                    </div>
                    <div class="fish-card fish-locked">
                        <div class="w-16 h-16 bg-secondary-text bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-question text-secondary-text text-2xl"></i>
                        </div>
                    </div>
                    <div class="fish-card fish-locked">
                        <div class="w-16 h-16 bg-secondary-text bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-question text-secondary-text text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 排行榜页面 -->
        <div id="leaderboard" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <button onclick="showPage('homepage')" class="text-xl">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-lg font-bold">排行榜</h1>
                    <div></div>
                </div>
            </div>

            <!-- 地区选择 -->
            <div class="px-6 py-4">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="text-sm text-secondary-text">当前地区</div>
                            <div class="text-lg font-bold text-primary-text">广东省 深圳市</div>
                        </div>
                        <button class="text-aqua-teal">
                            <i class="fas fa-map-marker-alt text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 排行榜类型 -->
            <div class="px-6 py-2">
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-aqua-teal text-white rounded-full text-sm">月度最重</button>
                    <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm">总渔获数</button>
                    <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm">鱼种收集</button>
                </div>
            </div>

            <!-- 我的排名 -->
            <div class="px-6 py-4">
                <div class="bg-gradient-to-r from-sunrise-gold to-yellow-400 p-4 rounded-xl text-white">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-xl font-bold">
                            8
                        </div>
                        <div class="flex-1">
                            <div class="font-bold">你的排名</div>
                            <div class="text-sm opacity-90">草鱼 1.2kg | 本月最佳</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold">1.2kg</div>
                            <div class="text-xs opacity-90">距离第7名</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排行榜列表 -->
            <div class="px-6 py-4 pb-24">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <!-- 前三名 -->
                    <div class="p-4 border-b border-divider">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                1
                            </div>
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format" 
                                 alt="用户" class="w-10 h-10 rounded-full">
                            <div class="flex-1">
                                <div class="font-medium text-primary-text">钓鱼大师</div>
                                <div class="text-sm text-secondary-text">草鱼 2.8kg</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-primary-text">2.8kg</div>
                                <div class="text-xs text-secondary-text">1月12日</div>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border-b border-divider">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-r from-gray-300 to-gray-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                2
                            </div>
                            <img src="https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=40&h=40&fit=crop&crop=face&auto=format" 
                                 alt="用户" class="w-10 h-10 rounded-full">
                            <div class="flex-1">
                                <div class="font-medium text-primary-text">江边老钓</div>
                                <div class="text-sm text-secondary-text">鲤鱼 2.1kg</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-primary-text">2.1kg</div>
                                <div class="text-xs text-secondary-text">1月10日</div>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 border-b border-divider">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                3
                            </div>
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format" 
                                 alt="用户" class="w-10 h-10 rounded-full">
                            <div class="flex-1">
                                <div class="font-medium text-primary-text">钓鱼新手</div>
                                <div class="text-sm text-secondary-text">鲫鱼 1.8kg</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-primary-text">1.8kg</div>
                                <div class="text-xs text-secondary-text">1月8日</div>
                            </div>
                        </div>
                    </div>

                    <!-- 其他排名 -->
                    <div class="space-y-0">
                        <div class="p-4 border-b border-divider">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-light-slate rounded-full flex items-center justify-center text-secondary-text font-medium">4</div>
                                <img src="https://images.unsplash.com/photo-1517841905240-472988babdf9?w=32&h=32&fit=crop&crop=face&auto=format" 
                                     alt="用户" class="w-8 h-8 rounded-full">
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-primary-text">湖边渔者</div>
                                    <div class="text-xs text-secondary-text">鲤鱼 1.5kg</div>
                                </div>
                                <div class="text-sm font-medium text-primary-text">1.5kg</div>
                            </div>
                        </div>
                        <div class="p-4 border-b border-divider">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-light-slate rounded-full flex items-center justify-center text-secondary-text font-medium">5</div>
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face&auto=format" 
                                     alt="用户" class="w-8 h-8 rounded-full">
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-primary-text">钓鱼小白</div>
                                    <div class="text-xs text-secondary-text">草鱼 1.4kg</div>
                                </div>
                                <div class="text-sm font-medium text-primary-text">1.4kg</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人档案页面 -->
        <div id="profile" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <h1 class="text-lg font-bold">个人档案</h1>
                    <button class="text-xl">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>

            <!-- 用户信息 -->
            <div class="px-6 py-6">
                <div class="bg-white p-6 rounded-xl shadow-sm text-center">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face&auto=format" 
                         alt="头像" class="w-20 h-20 rounded-full mx-auto mb-4 border-4 border-sunrise-gold">
                    <h2 class="text-xl font-bold text-primary-text mb-1">渔友小明</h2>
                    <p class="text-sm text-secondary-text mb-4">钓龄 2年 | ID: 1001</p>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-aqua-teal">128</div>
                            <div class="text-xs text-secondary-text">总渔获</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-sunrise-gold">32</div>
                            <div class="text-xs text-secondary-text">鱼种数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-text">8</div>
                            <div class="text-xs text-secondary-text">本月排名</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成就展示 -->
            <div class="px-6 py-4">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <h3 class="text-lg font-bold text-primary-text mb-4">成就徽章</h3>
                    <div class="grid grid-cols-4 gap-3">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-sunrise-gold to-yellow-400 rounded-full flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-trophy text-white"></i>
                            </div>
                            <div class="text-xs text-secondary-text">初次记录</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-aqua-teal to-blue-400 rounded-full flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-fish text-white"></i>
                            </div>
                            <div class="text-xs text-secondary-text">鱼种收集者</div>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-calendar text-white"></i>
                            </div>
                            <div class="text-xs text-secondary-text">连续记录</div>
                        </div>
                        <div class="text-center opacity-50">
                            <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-crown text-gray-400"></i>
                            </div>
                            <div class="text-xs text-secondary-text">月度冠军</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="px-6 py-4 pb-24">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="p-4 border-b border-divider flex items-center justify-between cursor-pointer hover:bg-light-slate transition-colors" onclick="showPage('career')">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-timeline text-aqua-teal"></i>
                            <span class="text-primary-text">我的生涯</span>
                        </div>
                        <i class="fas fa-chevron-right text-secondary-text"></i>
                    </div>
                    <div class="p-4 border-b border-divider flex items-center justify-between cursor-pointer hover:bg-light-slate transition-colors" onclick="showPage('fishing-spots')">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-map-marked-alt text-aqua-teal"></i>
                            <span class="text-primary-text">钓点管理</span>
                        </div>
                        <i class="fas fa-chevron-right text-secondary-text"></i>
                    </div>
                    <div class="p-4 border-b border-divider flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-share-alt text-aqua-teal"></i>
                            <span class="text-primary-text">分享记录</span>
                        </div>
                        <i class="fas fa-chevron-right text-secondary-text"></i>
                    </div>
                    <div class="p-4 border-b border-divider flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-crown text-sunrise-gold"></i>
                            <span class="text-primary-text">升级专业版</span>
                            <span class="text-xs bg-sunrise-gold text-white px-2 py-1 rounded">Pro</span>
                        </div>
                        <i class="fas fa-chevron-right text-secondary-text"></i>
                    </div>
                    <div class="p-4 flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-cog text-secondary-text"></i>
                            <span class="text-primary-text">设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-secondary-text"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 鱼种详情页面 -->
        <div id="fish-detail" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <button onclick="showPage('fishdex')" class="text-xl">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-lg font-bold" id="fish-detail-title">鲫鱼详情</h1>
                    <button onclick="shareSpeciesData()" class="text-xl">
                        <i class="fas fa-share-alt"></i>
                    </button>
                </div>
            </div>

            <!-- 鱼种信息卡片 -->
            <div class="px-6 py-4">
                <div class="bg-white p-6 rounded-xl shadow-sm text-center">
                    <div class="relative inline-block mb-4">
                        <img id="species-main-image" src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format" 
                             alt="鲫鱼" class="w-24 h-24 rounded-xl object-cover border-2 border-aqua-teal mx-auto">
                        <div class="absolute -top-2 -right-2 w-8 h-8 bg-sunrise-gold rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-white"></i>
                        </div>
                    </div>
                    <h2 class="text-xl font-bold text-primary-text mb-1" id="species-name">鲫鱼</h2>
                    <p class="text-sm text-secondary-text mb-4" id="species-scientific">Crucian Carp</p>
                    
                    <!-- 统计数据 -->
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-aqua-teal" id="total-catches">8</div>
                            <div class="text-xs text-secondary-text">总钓获</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-sunrise-gold" id="max-weight">1.2kg</div>
                            <div class="text-xs text-secondary-text">最大重量</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-text" id="success-rate">75%</div>
                            <div class="text-xs text-secondary-text">成功率</div>
                        </div>
                    </div>

                    <!-- 解锁时间 -->
                    <div class="text-xs text-secondary-text">
                        <i class="fas fa-calendar-alt mr-1"></i>
                        首次解锁：2024年1月10日
                    </div>
                </div>
            </div>

            <!-- 数据分析 -->
            <div class="px-6 py-4">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <h3 class="text-lg font-bold text-primary-text mb-4">📊 数据分析</h3>
                    
                    <!-- 重量趋势 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-primary-text">重量趋势</span>
                            <span class="text-xs text-secondary-text">近7次记录</span>
                        </div>
                        <div class="relative h-20 bg-light-slate rounded-lg p-3">
                            <div class="flex items-end justify-between h-full">
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-aqua-teal rounded-t" style="height: 60%"></div>
                                    <span class="text-xs text-secondary-text mt-1">0.8</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-aqua-teal rounded-t" style="height: 40%"></div>
                                    <span class="text-xs text-secondary-text mt-1">0.5</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-aqua-teal rounded-t" style="height: 80%"></div>
                                    <span class="text-xs text-secondary-text mt-1">1.0</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-sunrise-gold rounded-t" style="height: 100%"></div>
                                    <span class="text-xs text-secondary-text mt-1">1.2</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-aqua-teal rounded-t" style="height: 70%"></div>
                                    <span class="text-xs text-secondary-text mt-1">0.9</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-aqua-teal rounded-t" style="height: 50%"></div>
                                    <span class="text-xs text-secondary-text mt-1">0.6</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-aqua-teal rounded-t" style="height: 65%"></div>
                                    <span class="text-xs text-secondary-text mt-1">0.8</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最佳钓组 -->
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-light-slate p-3 rounded-lg">
                            <div class="text-xs text-secondary-text mb-1">最佳钓组</div>
                            <div class="text-sm font-medium text-primary-text">3号主线+2号子线</div>
                        </div>
                        <div class="bg-light-slate p-3 rounded-lg">
                            <div class="text-xs text-secondary-text mb-1">最佳饵料</div>
                            <div class="text-sm font-medium text-primary-text">玉米粒</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 历史记录列表 -->
            <div class="px-6 py-4 pb-24">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="p-4 border-b border-divider">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-bold text-primary-text">🎣 钓获记录</h3>
                            <div class="flex items-center space-x-2 text-sm text-secondary-text">
                                <span>按时间排序</span>
                                <i class="fas fa-sort-amount-down"></i>
                            </div>
                        </div>
                    </div>

                    <!-- 记录列表 -->
                    <div class="divide-y divide-divider" id="catch-history">
                        <!-- 记录项 1 -->
                        <div class="p-4 hover:bg-light-slate transition-colors cursor-pointer" onclick="viewCatchDetail(1)">
                            <div class="flex items-start space-x-3">
                                <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop&auto=format" 
                                     alt="鲫鱼" class="w-12 h-12 rounded-lg object-cover">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-1">
                                        <div class="text-sm font-medium text-primary-text">东湖公园钓点</div>
                                        <div class="text-xs text-secondary-text">今天 14:30</div>
                                    </div>
                                    <div class="flex items-center space-x-4 text-xs text-secondary-text mb-2">
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-weight-hanging text-aqua-teal"></i>
                                            <span>0.8kg</span>
                                        </span>
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-ruler text-aqua-teal"></i>
                                            <span>25cm</span>
                                        </span>
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-cloud-sun text-aqua-teal"></i>
                                            <span>多云 15°C</span>
                                        </span>
                                    </div>
                                    <div class="text-xs text-secondary-text">
                                        钓组: 3号主线+2号子线 | 饵料: 玉米粒
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-secondary-text"></i>
                            </div>
                        </div>

                        <!-- 记录项 2 -->
                        <div class="p-4 hover:bg-light-slate transition-colors cursor-pointer" onclick="viewCatchDetail(2)">
                            <div class="flex items-start space-x-3">
                                <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop&auto=format" 
                                     alt="鲫鱼" class="w-12 h-12 rounded-lg object-cover">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-1">
                                        <div class="text-sm font-medium text-primary-text">西湖钓点</div>
                                        <div class="text-xs text-secondary-text">1月14日 09:15</div>
                                    </div>
                                    <div class="flex items-center space-x-4 text-xs text-secondary-text mb-2">
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-weight-hanging text-aqua-teal"></i>
                                            <span>1.2kg</span>
                                        </span>
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-ruler text-aqua-teal"></i>
                                            <span>32cm</span>
                                        </span>
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-cloud-sun text-aqua-teal"></i>
                                            <span>晴 18°C</span>
                                        </span>
                                    </div>
                                    <div class="text-xs text-secondary-text">
                                        钓组: 4号主线+3号子线 | 饵料: 面包虫
                                    </div>
                                </div>
                                <div class="flex flex-col items-end">
                                    <span class="text-xs bg-sunrise-gold text-white px-2 py-1 rounded mb-1">最大</span>
                                    <i class="fas fa-chevron-right text-secondary-text"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 记录项 3 -->
                        <div class="p-4 hover:bg-light-slate transition-colors cursor-pointer" onclick="viewCatchDetail(3)">
                            <div class="flex items-start space-x-3">
                                <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop&auto=format" 
                                     alt="鲫鱼" class="w-12 h-12 rounded-lg object-cover">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-1">
                                        <div class="text-sm font-medium text-primary-text">北河钓场</div>
                                        <div class="text-xs text-secondary-text">1月12日 16:20</div>
                                    </div>
                                    <div class="flex items-center space-x-4 text-xs text-secondary-text mb-2">
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-weight-hanging text-aqua-teal"></i>
                                            <span>0.6kg</span>
                                        </span>
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-ruler text-aqua-teal"></i>
                                            <span>22cm</span>
                                        </span>
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-cloud-rain text-aqua-teal"></i>
                                            <span>小雨 12°C</span>
                                        </span>
                                    </div>
                                    <div class="text-xs text-secondary-text">
                                        钓组: 2号主线+1号子线 | 饵料: 蚯蚓
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right text-secondary-text"></i>
                            </div>
                        </div>

                        <!-- 查看更多 -->
                        <div class="p-4 text-center">
                            <button class="text-aqua-teal text-sm font-medium">
                                查看全部 8 条记录
                                <i class="fas fa-chevron-down ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我的生涯页面 -->
        <div id="career" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <button onclick="showPage('profile')" class="text-xl">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-lg font-bold">我的生涯</h1>
                    <button onclick="exportCareerData()" class="text-xl">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>

            <!-- 生涯概览 -->
            <div class="px-6 py-4">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="text-center mb-6">
                        <div class="w-20 h-20 mx-auto career-badge rounded-full flex items-center justify-center mb-3">
                            <i class="fas fa-fish text-white text-2xl"></i>
                        </div>
                        <h2 class="text-xl font-bold text-primary-text mb-1">钓鱼大师</h2>
                        <p class="text-sm text-secondary-text">钓龄 2年 • 等级 15</p>
                    </div>

                    <!-- 生涯统计 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-3 bg-light-slate rounded-lg">
                            <div class="text-2xl font-bold text-aqua-teal">128</div>
                            <div class="text-xs text-secondary-text">总钓获</div>
                        </div>
                        <div class="text-center p-3 bg-light-slate rounded-lg">
                            <div class="text-2xl font-bold text-sunrise-gold">32</div>
                            <div class="text-xs text-secondary-text">鱼种数</div>
                        </div>
                        <div class="text-center p-3 bg-light-slate rounded-lg">
                            <div class="text-2xl font-bold text-primary-text">85</div>
                            <div class="text-xs text-secondary-text">钓鱼天数</div>
                        </div>
                        <div class="text-center p-3 bg-light-slate rounded-lg">
                            <div class="text-2xl font-bold text-aqua-teal">25</div>
                            <div class="text-xs text-secondary-text">钓点数</div>
                        </div>
                    </div>

                    <!-- 生涯进度 -->
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm text-secondary-text">下一等级进度</span>
                            <span class="text-sm font-medium text-primary-text">75%</span>
                        </div>
                        <div class="w-full bg-light-slate rounded-full h-3">
                            <div class="bg-aqua-teal h-3 rounded-full" style="width: 75%"></div>
                        </div>
                        <div class="text-xs text-secondary-text mt-1">还需 12 次钓获升级到 16 级</div>
                    </div>
                </div>
            </div>

            <!-- 月度回顾 -->
            <div class="px-6 py-4">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <h3 class="text-lg font-bold text-primary-text mb-4">📊 本月回顾</h3>
                    <div class="grid grid-cols-3 gap-3 mb-4">
                        <div class="text-center">
                            <div class="text-xl font-bold text-aqua-teal">15</div>
                            <div class="text-xs text-secondary-text">本月钓获</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xl font-bold text-sunrise-gold">8</div>
                            <div class="text-xs text-secondary-text">出钓天数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xl font-bold text-primary-text">75%</div>
                            <div class="text-xs text-secondary-text">成功率</div>
                        </div>
                    </div>
                    
                    <!-- 月度趋势图 -->
                    <div class="relative h-16 bg-light-slate rounded-lg p-2">
                        <div class="flex items-end justify-between h-full">
                            <div class="flex flex-col items-center">
                                <div class="w-4 bg-aqua-teal rounded-t" style="height: 30%"></div>
                                <span class="text-xs text-secondary-text mt-1">1周</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-4 bg-aqua-teal rounded-t" style="height: 60%"></div>
                                <span class="text-xs text-secondary-text mt-1">2周</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-4 bg-sunrise-gold rounded-t" style="height: 100%"></div>
                                <span class="text-xs text-secondary-text mt-1">3周</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-4 bg-aqua-teal rounded-t" style="height: 45%"></div>
                                <span class="text-xs text-secondary-text mt-1">4周</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 时间线记录 -->
            <div class="px-6 py-4 pb-24">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="p-4 border-b border-divider">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-bold text-primary-text">🕒 时间线</h3>
                            <div class="flex items-center space-x-2">
                                <button class="text-xs bg-aqua-teal text-white px-3 py-1 rounded-full">全部</button>
                                <button class="text-xs text-secondary-text px-3 py-1 rounded-full border border-divider">本月</button>
                            </div>
                        </div>
                    </div>

                    <!-- 时间线内容 -->
                    <div class="p-4">
                        <!-- 2024年1月 -->
                        <div class="timeline-month mb-6">
                            <div class="flex items-center mb-4">
                                <div class="w-3 h-3 bg-sunrise-gold rounded-full mr-3"></div>
                                <h4 class="text-lg font-bold text-primary-text">2024年1月</h4>
                                <div class="flex-1 h-px bg-divider mx-3"></div>
                                <span class="text-sm text-secondary-text">15条记录</span>
                            </div>

                            <!-- 时间线记录项 -->
                            <div class="ml-6 space-y-4">
                                <!-- 1月15日 - 今天 -->
                                <div class="timeline-day">
                                    <div class="flex items-center mb-2">
                                        <div class="w-2 h-2 bg-aqua-teal rounded-full mr-3 -ml-4"></div>
                                        <span class="text-sm font-medium text-primary-text">1月15日 今天</span>
                                        <div class="flex-1"></div>
                                        <span class="text-xs text-secondary-text">2条记录</span>
                                    </div>
                                    
                                    <!-- 记录卡片 -->
                                    <div class="ml-2 space-y-3">
                                        <div class="timeline-record bg-light-slate p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="viewTimelineRecord(1)">
                                            <div class="flex items-start space-x-3">
                                                <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=50&h=50&fit=crop&auto=format" 
                                                     alt="鲫鱼" class="w-10 h-10 rounded-lg object-cover">
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-2 mb-1">
                                                        <span class="text-sm font-medium text-primary-text">鲫鱼</span>
                                                        <span class="text-xs bg-aqua-teal text-white px-2 py-1 rounded">新解锁</span>
                                                    </div>
                                                    <div class="text-xs text-secondary-text mb-1">东湖公园钓点 • 14:30</div>
                                                    <div class="flex items-center space-x-3 text-xs text-secondary-text">
                                                        <span>0.8kg</span>
                                                        <span>25cm</span>
                                                        <span>多云 15°C</span>
                                                    </div>
                                                </div>
                                                <div class="text-xs text-sunrise-gold">+50</div>
                                            </div>
                                        </div>

                                        <div class="timeline-record bg-light-slate p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="viewTimelineRecord(2)">
                                            <div class="flex items-start space-x-3">
                                                <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=50&h=50&fit=crop&auto=format" 
                                                     alt="草鱼" class="w-10 h-10 rounded-lg object-cover">
                                                <div class="flex-1">
                                                    <div class="text-sm font-medium text-primary-text mb-1">草鱼</div>
                                                    <div class="text-xs text-secondary-text mb-1">西湖钓点 • 09:15</div>
                                                    <div class="flex items-center space-x-3 text-xs text-secondary-text">
                                                        <span>1.2kg</span>
                                                        <span>35cm</span>
                                                        <span>晴 18°C</span>
                                                    </div>
                                                </div>
                                                <div class="text-xs text-sunrise-gold">+35</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 1月14日 -->
                                <div class="timeline-day">
                                    <div class="flex items-center mb-2">
                                        <div class="w-2 h-2 bg-aqua-teal rounded-full mr-3 -ml-4"></div>
                                        <span class="text-sm font-medium text-primary-text">1月14日</span>
                                        <div class="flex-1"></div>
                                        <span class="text-xs text-secondary-text">3条记录</span>
                                    </div>
                                    
                                    <div class="ml-2 space-y-3">
                                        <div class="timeline-record bg-light-slate p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="viewTimelineRecord(3)">
                                            <div class="flex items-start space-x-3">
                                                <img src="https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=50&h=50&fit=crop&auto=format" 
                                                     alt="鲤鱼" class="w-10 h-10 rounded-lg object-cover">
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-2 mb-1">
                                                        <span class="text-sm font-medium text-primary-text">鲤鱼</span>
                                                        <span class="text-xs bg-sunrise-gold text-white px-2 py-1 rounded">最大</span>
                                                    </div>
                                                    <div class="text-xs text-secondary-text mb-1">北河钓场 • 16:20</div>
                                                    <div class="flex items-center space-x-3 text-xs text-secondary-text">
                                                        <span>2.8kg</span>
                                                        <span>45cm</span>
                                                        <span>阴 12°C</span>
                                                    </div>
                                                </div>
                                                <div class="text-xs text-sunrise-gold">+80</div>
                                            </div>
                                        </div>

                                        <!-- 收起的记录 -->
                                        <div class="text-center py-2">
                                            <button class="text-xs text-aqua-teal" onclick="expandTimelineDay(this)">
                                                展开其余 2 条记录
                                                <i class="fas fa-chevron-down ml-1"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 1月12日 -->
                                <div class="timeline-day">
                                    <div class="flex items-center mb-2">
                                        <div class="w-2 h-2 bg-secondary-text rounded-full mr-3 -ml-4"></div>
                                        <span class="text-sm font-medium text-primary-text">1月12日</span>
                                        <div class="flex-1"></div>
                                        <span class="text-xs text-secondary-text">1条记录</span>
                                    </div>
                                    
                                    <div class="ml-2">
                                        <div class="timeline-record bg-light-slate p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="viewTimelineRecord(4)">
                                            <div class="flex items-start space-x-3">
                                                <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=50&h=50&fit=crop&auto=format" 
                                                     alt="鲫鱼" class="w-10 h-10 rounded-lg object-cover">
                                                <div class="flex-1">
                                                    <div class="text-sm font-medium text-primary-text mb-1">鲫鱼</div>
                                                    <div class="text-xs text-secondary-text mb-1">南湖公园 • 08:45</div>
                                                    <div class="flex items-center space-x-3 text-xs text-secondary-text">
                                                        <span>0.6kg</span>
                                                        <span>22cm</span>
                                                        <span>小雨 10°C</span>
                                                    </div>
                                                </div>
                                                <div class="text-xs text-sunrise-gold">+25</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 加载更多 -->
                        <div class="text-center py-4">
                            <button class="text-aqua-teal text-sm font-medium">
                                加载更多记录
                                <i class="fas fa-chevron-down ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 钓点管理页面 -->
        <div id="fishing-spots" class="page-content hidden">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <button onclick="showPage('profile')" class="text-xl">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-lg font-bold">钓点管理</h1>
                    <button onclick="addNewSpot()" class="text-xl">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>

            <!-- 视图切换 -->
            <div class="px-6 py-4">
                <div class="flex items-center bg-white p-1 rounded-lg shadow-sm">
                    <button id="map-view-btn" onclick="switchView('map')" class="flex-1 py-2 px-4 text-sm font-medium text-white bg-aqua-teal rounded-md transition-all">
                        <i class="fas fa-map mr-2"></i>地图视图
                    </button>
                    <button id="list-view-btn" onclick="switchView('list')" class="flex-1 py-2 px-4 text-sm font-medium text-secondary-text hover:text-primary-text transition-all">
                        <i class="fas fa-list mr-2"></i>列表视图
                    </button>
                </div>
            </div>

            <!-- 地图视图 -->
            <div id="map-view" class="px-6 py-4">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <!-- 模拟地图区域 -->
                    <div class="relative h-64 bg-gradient-to-br from-blue-100 to-green-100">
                        <!-- 地图背景 -->
                        <div class="absolute inset-0 opacity-20">
                            <div class="w-full h-full bg-gradient-to-br from-blue-200 via-green-200 to-blue-300"></div>
                        </div>
                        
                        <!-- 钓点标记 -->
                        <div class="absolute top-16 left-20 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="relative">
                                <div class="w-8 h-8 bg-aqua-teal rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:scale-110 transition-all" onclick="showSpotDetail('donghu')">
                                    <i class="fas fa-map-pin text-white text-sm"></i>
                                </div>
                                <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-primary-text whitespace-nowrap">
                                    东湖公园
                                </div>
                                <div class="pulse-ring"></div>
                            </div>
                        </div>

                        <div class="absolute top-32 left-40 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="relative">
                                <div class="w-8 h-8 bg-sunrise-gold rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:scale-110 transition-all" onclick="showSpotDetail('xihu')">
                                    <i class="fas fa-star text-white text-sm"></i>
                                </div>
                                <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-primary-text whitespace-nowrap">
                                    西湖钓点
                                </div>
                                <div class="pulse-ring"></div>
                            </div>
                        </div>

                        <div class="absolute top-20 right-24 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="relative">
                                <div class="w-8 h-8 bg-aqua-teal rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:scale-110 transition-all" onclick="showSpotDetail('beihe')">
                                    <i class="fas fa-map-pin text-white text-sm"></i>
                                </div>
                                <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-primary-text whitespace-nowrap">
                                    北河钓场
                                </div>
                                <div class="pulse-ring"></div>
                            </div>
                        </div>

                        <!-- 地图控制按钮 -->
                        <div class="absolute top-4 right-4 flex flex-col space-y-2">
                            <button class="w-8 h-8 bg-white rounded shadow flex items-center justify-center hover:bg-gray-50 transition-colors">
                                <i class="fas fa-plus text-gray-600 text-sm"></i>
                            </button>
                            <button class="w-8 h-8 bg-white rounded shadow flex items-center justify-center hover:bg-gray-50 transition-colors">
                                <i class="fas fa-minus text-gray-600 text-sm"></i>
                            </button>
                            <button class="w-8 h-8 bg-white rounded shadow flex items-center justify-center hover:bg-gray-50 transition-colors">
                                <i class="fas fa-crosshairs text-gray-600 text-sm"></i>
                            </button>
                        </div>

                        <!-- 添加钓点提示 -->
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="bg-white bg-opacity-90 p-3 rounded-lg">
                                <div class="text-xs text-primary-text font-medium mb-1">💡 使用提示</div>
                                <div class="text-xs text-secondary-text">点击地图上的标记查看钓点详情，点击右上角+号添加新钓点</div>
                            </div>
                        </div>
                    </div>

                    <!-- 地图底部信息 -->
                    <div class="p-4 border-t border-divider">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-aqua-teal rounded-full"></div>
                                    <span class="text-xs text-secondary-text">常规钓点</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-sunrise-gold rounded-full"></div>
                                    <span class="text-xs text-secondary-text">收藏钓点</span>
                                </div>
                            </div>
                            <div class="text-xs text-secondary-text">共 25 个钓点</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 列表视图 -->
            <div id="list-view" class="hidden">
                <!-- 钓点统计 -->
                <div class="px-6 py-4">
                    <div class="bg-white p-4 rounded-xl shadow-sm">
                        <h3 class="text-lg font-bold text-primary-text mb-4">📊 钓点统计</h3>
                        <div class="grid grid-cols-3 gap-3">
                            <div class="text-center p-3 bg-light-slate rounded-lg">
                                <div class="text-xl font-bold text-aqua-teal">25</div>
                                <div class="text-xs text-secondary-text">总钓点</div>
                            </div>
                            <div class="text-center p-3 bg-light-slate rounded-lg">
                                <div class="text-xl font-bold text-sunrise-gold">8</div>
                                <div class="text-xs text-secondary-text">收藏点</div>
                            </div>
                            <div class="text-center p-3 bg-light-slate rounded-lg">
                                <div class="text-xl font-bold text-primary-text">15</div>
                                <div class="text-xs text-secondary-text">本月去过</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 钓点筛选 -->
                <div class="px-6 py-2">
                    <div class="flex space-x-2 overflow-x-auto">
                        <button class="px-4 py-2 bg-aqua-teal text-white rounded-full text-sm whitespace-nowrap">全部</button>
                        <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm whitespace-nowrap">收藏</button>
                        <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm whitespace-nowrap">最近访问</button>
                        <button class="px-4 py-2 bg-white text-secondary-text border border-divider rounded-full text-sm whitespace-nowrap">高产出</button>
                    </div>
                </div>

                <!-- 钓点列表 -->
                <div class="px-6 py-4 pb-24">
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="p-4 border-b border-divider">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-bold text-primary-text">钓点列表</h3>
                                <div class="flex items-center space-x-2 text-sm text-secondary-text">
                                    <span>按访问频次排序</span>
                                    <i class="fas fa-sort-amount-down"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 钓点项目 -->
                        <div class="divide-y divide-divider">
                            <!-- 钓点 1 - 收藏钓点 -->
                            <div class="p-4 hover:bg-light-slate transition-colors cursor-pointer" onclick="showSpotDetail('donghu')">
                                <div class="flex items-start space-x-3">
                                    <div class="relative">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-green-400 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-white"></i>
                                        </div>
                                        <div class="absolute -top-1 -right-1 w-5 h-5 bg-sunrise-gold rounded-full flex items-center justify-center">
                                            <i class="fas fa-star text-white text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <h4 class="text-sm font-bold text-primary-text">东湖公园钓点</h4>
                                            <span class="text-xs bg-sunrise-gold text-white px-2 py-1 rounded">收藏</span>
                                        </div>
                                        <div class="text-xs text-secondary-text mb-2">距离 2.3km • 公园湖泊 • 免费</div>
                                        <div class="grid grid-cols-3 gap-3 text-xs">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-fish text-aqua-teal"></i>
                                                <span class="text-secondary-text">28条记录</span>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-calendar text-aqua-teal"></i>
                                                <span class="text-secondary-text">最近：今天</span>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-chart-line text-aqua-teal"></i>
                                                <span class="text-secondary-text">成功率 85%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-bold text-primary-text">4.8</div>
                                        <div class="flex items-center text-xs text-sunrise-gold">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 钓点 2 -->
                            <div class="p-4 hover:bg-light-slate transition-colors cursor-pointer" onclick="showSpotDetail('xihu')">
                                <div class="flex items-start space-x-3">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-400 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-water text-white"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <h4 class="text-sm font-bold text-primary-text">西湖钓点</h4>
                                            <span class="text-xs bg-aqua-teal text-white px-2 py-1 rounded">热门</span>
                                        </div>
                                        <div class="text-xs text-secondary-text mb-2">距离 5.8km • 天然湖泊 • 收费</div>
                                        <div class="grid grid-cols-3 gap-3 text-xs">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-fish text-aqua-teal"></i>
                                                <span class="text-secondary-text">15条记录</span>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-calendar text-aqua-teal"></i>
                                                <span class="text-secondary-text">最近：昨天</span>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-chart-line text-aqua-teal"></i>
                                                <span class="text-secondary-text">成功率 72%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-bold text-primary-text">4.2</div>
                                        <div class="flex items-center text-xs text-sunrise-gold">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 钓点 3 -->
                            <div class="p-4 hover:bg-light-slate transition-colors cursor-pointer" onclick="showSpotDetail('beihe')">
                                <div class="flex items-start space-x-3">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-stream text-white"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <h4 class="text-sm font-bold text-primary-text">北河钓场</h4>
                                        </div>
                                        <div class="text-xs text-secondary-text mb-2">距离 12.5km • 人工鱼塘 • 收费</div>
                                        <div class="grid grid-cols-3 gap-3 text-xs">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-fish text-aqua-teal"></i>
                                                <span class="text-secondary-text">8条记录</span>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-calendar text-aqua-teal"></i>
                                                <span class="text-secondary-text">最近：3天前</span>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-chart-line text-aqua-teal"></i>
                                                <span class="text-secondary-text">成功率 60%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-bold text-primary-text">3.8</div>
                                        <div class="flex items-center text-xs text-sunrise-gold">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 查看更多 -->
                            <div class="p-4 text-center">
                                <button class="text-aqua-teal text-sm font-medium">
                                    查看全部 25 个钓点
                                    <i class="fas fa-chevron-down ml-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 照片查看模态框 -->
        <div id="photo-modal" class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 hidden">
            <div class="relative w-full h-full flex items-center justify-center p-4">
                <!-- 关闭按钮 -->
                <button onclick="closePhotoModal()" class="absolute top-4 right-4 w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all z-10">
                    <i class="fas fa-times"></i>
                </button>
                
                <!-- 照片导航 -->
                <button id="prev-photo" onclick="navigatePhoto(-1)" class="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all">
                    <i class="fas fa-chevron-left"></i>
                </button>
                
                <button id="next-photo" onclick="navigatePhoto(1)" class="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all">
                    <i class="fas fa-chevron-right"></i>
                </button>
                
                <!-- 主要照片显示 -->
                <div class="text-center">
                    <img id="modal-photo" src="" alt="照片预览" class="max-w-full max-h-full object-contain rounded-lg">
                    <div class="mt-4 text-white">
                        <div id="modal-photo-info" class="text-sm opacity-80">照片 1 / 3</div>
                    </div>
                </div>
                
                <!-- 底部缩略图导航 -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                    <div class="flex space-x-2" id="modal-thumbnails">
                        <!-- 缩略图将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md gradient-bg">
            <div class="flex items-center justify-around py-3 px-6">
                <button onclick="showPage('homepage')" class="nav-tab flex flex-col items-center space-y-1">
                    <i class="fas fa-home text-xl nav-active" id="nav-home"></i>
                    <span class="text-xs nav-active" id="nav-home-text">首页</span>
                </button>
                <button onclick="showPage('fishdex')" class="nav-tab flex flex-col items-center space-y-1">
                    <i class="fas fa-book text-xl nav-inactive" id="nav-fishdex"></i>
                    <span class="text-xs nav-inactive" id="nav-fishdex-text">图鉴</span>
                </button>
                <button onclick="showPage('record')" class="nav-tab relative">
                    <div class="w-14 h-14 bg-sunrise-gold rounded-full flex items-center justify-center -mt-6 shadow-lg">
                        <i class="fas fa-plus text-white text-xl"></i>
                    </div>
                </button>
                <button onclick="showPage('leaderboard')" class="nav-tab flex flex-col items-center space-y-1">
                    <i class="fas fa-trophy text-xl nav-inactive" id="nav-leaderboard"></i>
                    <span class="text-xs nav-inactive" id="nav-leaderboard-text">排行</span>
                </button>
                <button onclick="showPage('profile')" class="nav-tab flex flex-col items-center space-y-1">
                    <i class="fas fa-user text-xl nav-inactive" id="nav-profile"></i>
                    <span class="text-xs nav-inactive" id="nav-profile-text">我的</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let uploadedPhotos = [];
        let isNewSpecies = true; // 模拟是否为新鱼种

        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.add('hidden'));
            
            // 显示目标页面
            document.getElementById(pageId).classList.remove('hidden');
            
            // 如果是记录页面，重置到第一步
            if (pageId === 'record') {
                resetRecordPage();
            }
            
            // 更新导航状态
            updateNavigation(pageId);
        }

        function updateNavigation(activePageId) {
            // 重置所有导航项
            const navItems = ['home', 'fishdex', 'leaderboard', 'profile'];
            navItems.forEach(item => {
                const icon = document.getElementById(`nav-${item}`);
                const text = document.getElementById(`nav-${item}-text`);
                if (icon && text) {
                    icon.className = icon.className.replace('nav-active', 'nav-inactive');
                    text.className = text.className.replace('nav-active', 'nav-inactive');
                }
            });
            
            // 设置当前活跃项
            let navId = activePageId;
            if (activePageId === 'homepage') navId = 'home';
            
            const activeIcon = document.getElementById(`nav-${navId}`);
            const activeText = document.getElementById(`nav-${navId}-text`);
            if (activeIcon && activeText) {
                activeIcon.className = activeIcon.className.replace('nav-inactive', 'nav-active');
                activeText.className = activeText.className.replace('nav-inactive', 'nav-active');
            }
        }

        // 记录页面功能
        function resetRecordPage() {
            currentStep = 1;
            uploadedPhotos = [];
            updateStepIndicator();
            showStep(1);
            updateProgressBar(25);
            
            // 重置照片预览
            document.getElementById('photo-preview').classList.add('hidden');
            document.getElementById('upload-area').classList.remove('hidden');
            document.getElementById('next-step-1').disabled = true;
            document.getElementById('next-step-1').className = 'w-full mt-4 py-3 bg-gray-300 text-gray-500 rounded-lg font-medium cursor-not-allowed';
        }

        function goToStep(step) {
            if (step === 2 && uploadedPhotos.length === 0) {
                alert('请先上传至少一张照片');
                return;
            }
            
            currentStep = step;
            showStep(step);
            updateStepIndicator();
            updateProgressBar(step * 25);
            
            // 如果进入AI扫描步骤，开始扫描动画
            if (step === 2) {
                startAIScanning();
            }
        }

        function showStep(step) {
            // 隐藏所有步骤
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step-${i}`);
                if (stepElement) {
                    stepElement.classList.add('hidden');
                }
            }
            
            // 显示当前步骤
            const currentStepElement = document.getElementById(`step-${step}`);
            if (currentStepElement) {
                currentStepElement.classList.remove('hidden');
            }
        }

        function updateStepIndicator() {
            const indicator = document.getElementById('step-indicator');
            if (indicator) {
                indicator.textContent = `步骤 ${currentStep}/4`;
            }
        }

        function updateProgressBar(percentage) {
            const progressBar = document.getElementById('progress-bar');
            if (progressBar) {
                progressBar.style.width = percentage + '%';
            }
        }

        // 模拟照片拍摄
        function simulatePhotoCapture() {
            const photoUrl = 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=150&h=150&fit=crop&auto=format';
            addPhoto(photoUrl, '拍摄照片');
        }

        // 模拟照片上传
        function simulatePhotoUpload() {
            const photos = [
                'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=150&h=150&fit=crop&auto=format',
                'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=150&h=150&fit=crop&auto=format'
            ];
            const randomPhoto = photos[Math.floor(Math.random() * photos.length)];
            addPhoto(randomPhoto, '上传照片');
        }

        function addPhoto(url, type) {
            uploadedPhotos.push({url, type});
            updatePhotoPreview();
            enableNextStep();
        }

        function updatePhotoPreview() {
            const photoPreview = document.getElementById('photo-preview');
            const photoGrid = document.getElementById('photo-grid');
            const uploadArea = document.getElementById('upload-area');
            
            if (uploadedPhotos.length > 0) {
                photoPreview.classList.remove('hidden');
                uploadArea.classList.add('hidden');
                
                photoGrid.innerHTML = '';
                uploadedPhotos.forEach((photo, index) => {
                    const photoItem = document.createElement('div');
                    photoItem.className = 'photo-item';
                    photoItem.innerHTML = `
                        <img src="${photo.url}" alt="${photo.type}">
                        <div class="remove-btn" onclick="removePhoto(${index})">×</div>
                    `;
                    photoGrid.appendChild(photoItem);
                });
                
                // 添加更多照片的选项
                if (uploadedPhotos.length < 4) {
                    const addMore = document.createElement('div');
                    addMore.className = 'photo-item border-dashed bg-light-slate flex items-center justify-center cursor-pointer';
                    addMore.innerHTML = '<i class="fas fa-plus text-secondary-text text-2xl"></i>';
                    addMore.onclick = () => simulatePhotoUpload();
                    photoGrid.appendChild(addMore);
                }
            }
        }

        function removePhoto(index) {
            uploadedPhotos.splice(index, 1);
            if (uploadedPhotos.length === 0) {
                document.getElementById('photo-preview').classList.add('hidden');
                document.getElementById('upload-area').classList.remove('hidden');
                disableNextStep();
            } else {
                updatePhotoPreview();
            }
        }

        function clearPhotos() {
            uploadedPhotos = [];
            document.getElementById('photo-preview').classList.add('hidden');
            document.getElementById('upload-area').classList.remove('hidden');
            disableNextStep();
        }

        function enableNextStep() {
            const nextBtn = document.getElementById('next-step-1');
            nextBtn.disabled = false;
            nextBtn.className = 'w-full mt-4 py-3 bg-sunrise-gold text-white rounded-lg font-medium hover:scale-105 transition-all cursor-pointer';
        }

        function disableNextStep() {
            const nextBtn = document.getElementById('next-step-1');
            nextBtn.disabled = true;
            nextBtn.className = 'w-full mt-4 py-3 bg-gray-300 text-gray-500 rounded-lg font-medium cursor-not-allowed';
        }

        // AI扫描动画
        function startAIScanning() {
            // 设置扫描图片
            const scanningImage = document.getElementById('scanning-image');
            if (uploadedPhotos.length > 0) {
                scanningImage.src = uploadedPhotos[0].url;
            }
            
            // 扫描进度动画
            setTimeout(() => {
                document.getElementById('scan-progress-2').classList.remove('opacity-50');
                document.getElementById('scan-progress-2').innerHTML = '🧠 AI模型识别中... <span class="text-aqua-teal">完成</span>';
            }, 2000);
            
            setTimeout(() => {
                document.getElementById('scan-progress-3').classList.remove('opacity-50');
                document.getElementById('scan-progress-3').innerHTML = '🎯 匹配数据库... <span class="text-aqua-teal">完成</span>';
            }, 4000);
            
            // 5秒后跳转到结果页面
            setTimeout(() => {
                goToStep(3);
                // 如果是新鱼种，显示解锁特效
                if (isNewSpecies) {
                    setTimeout(showUnlockEffect, 500);
                }
            }, 5000);
        }

        // 解锁特效
        function showUnlockEffect() {
            document.getElementById('unlock-overlay').classList.remove('hidden');
        }

        function hideUnlockEffect() {
            document.getElementById('unlock-overlay').classList.add('hidden');
        }

        // 选择替代识别结果
        function selectAlternative(fishName) {
            // 这里可以更新显示的识别结果
            alert(`已选择: ${fishName}`);
        }

        // 保存记录
        function saveRecord() {
            // 模拟保存过程
            showSuccessPage();
        }

        // 显示成功页面
        function showSuccessPage() {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.add('hidden'));
            
            // 显示成功页面
            const successPage = document.getElementById('success');
            successPage.classList.remove('hidden');
            successPage.classList.add('success-enter');
            
            // 更新导航状态
            updateNavigation('success');
            
            // 启动庆祝动画
            setTimeout(() => {
                startCelebrationAnimation();
            }, 300);
        }

        // 庆祝动画
        function startCelebrationAnimation() {
            // 可以添加更多庆祝效果，比如音效等
            console.log('🎉 庆祝动画开始');
        }

        // 分享记录
        function shareRecord() {
            // 模拟分享功能
            if (navigator.share) {
                navigator.share({
                    title: '我的渔获日志',
                    text: '今天钓到了一条漂亮的鲫鱼！',
                    url: window.location.href
                });
            } else {
                // 降级方案
                alert('分享功能：已复制链接到剪贴板');
            }
        }

        // 照片相关功能
        let savedPhotos = [
            'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop&auto=format',
            'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop&auto=format',
            'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=400&h=300&fit=crop&auto=format'
        ];
        let currentPhotoIndex = 0;

        // 切换主要照片
        function switchMainPhoto(index, src) {
            currentPhotoIndex = index;
            const mainPhoto = document.getElementById('main-photo');
            mainPhoto.src = src.replace('150&h=150', '400&h=300');
            
            // 更新缩略图活跃状态
            const thumbnails = document.querySelectorAll('.thumbnail-item img');
            thumbnails.forEach((thumb, i) => {
                if (i === index) {
                    thumb.classList.add('active');
                    thumb.classList.remove('border-divider');
                    thumb.classList.add('border-aqua-teal');
                } else {
                    thumb.classList.remove('active');
                    thumb.classList.add('border-divider');
                    thumb.classList.remove('border-aqua-teal');
                }
            });
            
            // 更新选中指示器
            const indicators = document.querySelectorAll('.thumbnail-item .absolute');
            indicators.forEach((indicator, i) => {
                if (i === index) {
                    indicator.style.display = 'block';
                } else {
                    indicator.style.display = 'none';
                }
            });
        }

        // 打开照片模态框
        function openPhotoModal(index) {
            currentPhotoIndex = index;
            const modal = document.getElementById('photo-modal');
            const modalPhoto = document.getElementById('modal-photo');
            const photoInfo = document.getElementById('modal-photo-info');
            
            modalPhoto.src = savedPhotos[index];
            photoInfo.textContent = `照片 ${index + 1} / ${savedPhotos.length}`;
            
            // 生成底部缩略图
            updateModalThumbnails();
            
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 关闭照片模态框
        function closePhotoModal() {
            const modal = document.getElementById('photo-modal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 导航照片
        function navigatePhoto(direction) {
            currentPhotoIndex += direction;
            
            if (currentPhotoIndex < 0) {
                currentPhotoIndex = savedPhotos.length - 1;
            } else if (currentPhotoIndex >= savedPhotos.length) {
                currentPhotoIndex = 0;
            }
            
            const modalPhoto = document.getElementById('modal-photo');
            const photoInfo = document.getElementById('modal-photo-info');
            
            modalPhoto.src = savedPhotos[currentPhotoIndex];
            photoInfo.textContent = `照片 ${currentPhotoIndex + 1} / ${savedPhotos.length}`;
            
            updateModalThumbnails();
        }

        // 更新模态框缩略图
        function updateModalThumbnails() {
            const container = document.getElementById('modal-thumbnails');
            container.innerHTML = '';
            
            savedPhotos.forEach((photo, index) => {
                const thumb = document.createElement('img');
                thumb.src = photo.replace('400&h=300', '100&h=100');
                thumb.className = `modal-thumbnail object-cover ${index === currentPhotoIndex ? 'active' : ''}`;
                thumb.onclick = () => {
                    currentPhotoIndex = index;
                    document.getElementById('modal-photo').src = photo;
                    document.getElementById('modal-photo-info').textContent = `照片 ${index + 1} / ${savedPhotos.length}`;
                    updateModalThumbnails();
                };
                container.appendChild(thumb);
            });
        }

        // 更新成功页面的照片展示
        function updateSuccessPagePhotos() {
            // 从上传的照片数组中获取照片
            if (uploadedPhotos.length > 0) {
                savedPhotos = uploadedPhotos.map(photo => photo.url);
                
                // 更新照片计数
                document.getElementById('photo-count').textContent = `${savedPhotos.length}张照片`;
                
                // 更新主要照片
                document.getElementById('main-photo').src = savedPhotos[0];
                
                // 更新缩略图网格
                const thumbnailGrid = document.getElementById('thumbnail-grid');
                thumbnailGrid.innerHTML = '';
                
                savedPhotos.forEach((photo, index) => {
                    const thumbItem = document.createElement('div');
                    thumbItem.className = 'thumbnail-item relative';
                    thumbItem.innerHTML = `
                        <img src="${photo.replace('150&h=150', '150&h=150')}" 
                             alt="照片${index + 1}" 
                             class="w-full aspect-square rounded-lg object-cover border ${index === 0 ? 'border-aqua-teal' : 'border-divider'} cursor-pointer"
                             onclick="switchMainPhoto(${index}, '${photo}')">
                        ${index === 0 ? '<div class="absolute top-1 right-1 w-3 h-3 bg-aqua-teal rounded-full"></div>' : ''}
                    `;
                    thumbnailGrid.appendChild(thumbItem);
                });
                
                // 添加"添加更多"占位符
                if (savedPhotos.length < 4) {
                    const addMore = document.createElement('div');
                    addMore.className = 'add-more-placeholder';
                    addMore.innerHTML = `
                        <div class="w-full aspect-square rounded-lg border-2 border-dashed border-divider bg-light-slate flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-colors"
                             onclick="showPage('record')">
                            <i class="fas fa-plus text-secondary-text"></i>
                        </div>
                    `;
                    thumbnailGrid.appendChild(addMore);
                }
            }
        }

        // 修改显示成功页面函数
        function showSuccessPage() {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.add('hidden'));
            
            // 显示成功页面
            const successPage = document.getElementById('success');
            successPage.classList.remove('hidden');
            successPage.classList.add('success-enter');
            
            // 更新照片展示
            updateSuccessPagePhotos();
            
            // 更新导航状态
            updateNavigation('success');
            
            // 启动庆祝动画
            setTimeout(() => {
                startCelebrationAnimation();
            }, 300);
        }

        // 鱼种详情相关功能
        const fishSpeciesData = {
            'crucian': {
                name: '鲫鱼',
                scientific: 'Crucian Carp',
                image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format',
                totalCatches: 8,
                maxWeight: '1.2kg',
                successRate: '75%',
                unlockDate: '2024年1月10日',
                bestRig: '3号主线+2号子线',
                bestBait: '玉米粒'
            },
            'grass-carp': {
                name: '草鱼',
                scientific: 'Grass Carp',
                image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=120&h=120&fit=crop&auto=format',
                totalCatches: 5,
                maxWeight: '3.2kg',
                successRate: '60%',
                unlockDate: '2024年1月8日',
                bestRig: '5号主线+4号子线',
                bestBait: '玉米粒'
            },
            'carp': {
                name: '鲤鱼',
                scientific: 'Common Carp',
                image: 'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=120&h=120&fit=crop&auto=format',
                totalCatches: 12,
                maxWeight: '2.8kg',
                successRate: '85%',
                unlockDate: '2024年1月5日',
                bestRig: '4号主线+3号子线',
                bestBait: '面包虫'
            }
        };

        // 显示鱼种详情页面
        function showFishDetail(fishId, fishName, scientificName) {
            const fishData = fishSpeciesData[fishId];
            if (!fishData) return;

            // 更新页面标题
            document.getElementById('fish-detail-title').textContent = `${fishData.name}详情`;
            
            // 更新鱼种信息
            document.getElementById('species-main-image').src = fishData.image;
            document.getElementById('species-name').textContent = fishData.name;
            document.getElementById('species-scientific').textContent = fishData.scientific;
            
            // 更新统计数据
            document.getElementById('total-catches').textContent = fishData.totalCatches;
            document.getElementById('max-weight').textContent = fishData.maxWeight;
            document.getElementById('success-rate').textContent = fishData.successRate;
            
            // 更新解锁时间
            const unlockElement = document.querySelector('#fish-detail .text-xs.text-secondary-text');
            unlockElement.innerHTML = `<i class="fas fa-calendar-alt mr-1"></i>首次解锁：${fishData.unlockDate}`;
            
            // 更新最佳配置
            document.querySelector('#fish-detail .bg-light-slate:nth-child(1) .text-sm').textContent = fishData.bestRig;
            document.querySelector('#fish-detail .bg-light-slate:nth-child(2) .text-sm').textContent = fishData.bestBait;
            
            // 显示详情页面
            showPage('fish-detail');
        }

        // 查看单条钓获记录详情
        function viewCatchDetail(recordId) {
            // 这里可以跳转到具体的记录详情页面
            alert(`查看记录详情 #${recordId}`);
        }

        // 分享鱼种数据
        function shareSpeciesData() {
            const currentFish = document.getElementById('species-name').textContent;
            if (navigator.share) {
                navigator.share({
                    title: `我的${currentFish}钓获统计`,
                    text: `我已经钓获了${document.getElementById('total-catches').textContent}条${currentFish}，最大重量${document.getElementById('max-weight').textContent}！`,
                    url: window.location.href
                });
            } else {
                alert('分享功能：已复制数据到剪贴板');
            }
        }

        // 修改导航函数，支持鱼种详情页面
        function updateNavigation(activePageId) {
            // 重置所有导航项
            const navItems = ['home', 'fishdex', 'leaderboard', 'profile'];
            navItems.forEach(item => {
                const icon = document.getElementById(`nav-${item}`);
                const text = document.getElementById(`nav-${item}-text`);
                if (icon && text) {
                    icon.className = icon.className.replace('nav-active', 'nav-inactive');
                    text.className = text.className.replace('nav-active', 'nav-inactive');
                }
            });
            
            // 设置当前活跃项
            let navId = activePageId;
            if (activePageId === 'homepage') navId = 'home';
            if (activePageId === 'fish-detail') navId = 'fishdex'; // 鱼种详情归属于图鉴
            if (activePageId === 'success') return; // 成功页面不更新导航
            
            const activeIcon = document.getElementById(`nav-${navId}`);
            const activeText = document.getElementById(`nav-${navId}-text`);
            if (activeIcon && activeText) {
                activeIcon.className = activeIcon.className.replace('nav-inactive', 'nav-active');
                activeText.className = activeText.className.replace('nav-inactive', 'nav-active');
            }
        }

        // 生涯页面相关功能
        function viewTimelineRecord(recordId) {
            // 跳转到具体记录详情，可以重用保存成功页面的展示
            alert(`查看时间线记录详情 #${recordId}`);
        }

        function expandTimelineDay(button) {
            // 展开当天的其他记录
            const dayContainer = button.closest('.timeline-day');
            const hiddenRecords = [
                {
                    fish: '鲫鱼',
                    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=50&h=50&fit=crop&auto=format',
                    location: '公园池塘',
                    time: '12:30',
                    weight: '0.4kg',
                    length: '18cm',
                    weather: '多云 16°C',
                    points: '+20'
                },
                {
                    fish: '草鱼',
                    image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=50&h=50&fit=crop&auto=format',
                    location: '河边钓点',
                    time: '18:45',
                    weight: '1.8kg',
                    length: '38cm',
                    weather: '晴 14°C',
                    points: '+60'
                }
            ];

            // 创建新的记录元素
            hiddenRecords.forEach(record => {
                const recordElement = document.createElement('div');
                recordElement.className = 'timeline-record bg-light-slate p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors';
                recordElement.onclick = () => viewTimelineRecord(Math.random());
                recordElement.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <img src="${record.image}" alt="${record.fish}" class="w-10 h-10 rounded-lg object-cover">
                        <div class="flex-1">
                            <div class="text-sm font-medium text-primary-text mb-1">${record.fish}</div>
                            <div class="text-xs text-secondary-text mb-1">${record.location} • ${record.time}</div>
                            <div class="flex items-center space-x-3 text-xs text-secondary-text">
                                <span>${record.weight}</span>
                                <span>${record.length}</span>
                                <span>${record.weather}</span>
                            </div>
                        </div>
                        <div class="text-xs text-sunrise-gold">${record.points}</div>
                    </div>
                `;
                
                // 插入到按钮前面
                button.parentElement.parentElement.insertBefore(recordElement, button.parentElement);
            });

            // 移除展开按钮
            button.parentElement.remove();
        }

        function exportCareerData() {
            // 模拟导出生涯数据
            alert('生涯数据导出功能：将生成PDF报告');
        }

        // 修改导航函数，支持生涯页面
        function updateNavigation(activePageId) {
            // 重置所有导航项
            const navItems = ['home', 'fishdex', 'leaderboard', 'profile'];
            navItems.forEach(item => {
                const icon = document.getElementById(`nav-${item}`);
                const text = document.getElementById(`nav-${item}-text`);
                if (icon && text) {
                    icon.className = icon.className.replace('nav-active', 'nav-inactive');
                    text.className = text.className.replace('nav-active', 'nav-inactive');
                }
            });
            
            // 设置当前活跃项
            let navId = activePageId;
            if (activePageId === 'homepage') navId = 'home';
            if (activePageId === 'fish-detail') navId = 'fishdex'; // 鱼种详情归属于图鉴
            if (activePageId === 'career') navId = 'profile'; // 生涯页面归属于个人中心
            if (activePageId === 'success') return; // 成功页面不更新导航
            
            const activeIcon = document.getElementById(`nav-${navId}`);
            const activeText = document.getElementById(`nav-${navId}-text`);
            if (activeIcon && activeText) {
                activeIcon.className = activeIcon.className.replace('nav-inactive', 'nav-active');
                activeText.className = activeText.className.replace('nav-inactive', 'nav-active');
            }
        }

        // 钓点管理页面相关功能
        let currentView = 'map'; // map 或 list

        // 切换视图
        function switchView(view) {
            currentView = view;
            const mapView = document.getElementById('map-view');
            const listView = document.getElementById('list-view');
            const mapBtn = document.getElementById('map-view-btn');
            const listBtn = document.getElementById('list-view-btn');

            if (view === 'map') {
                mapView.classList.remove('hidden');
                listView.classList.add('hidden');
                
                // 更新按钮状态
                mapBtn.className = 'flex-1 py-2 px-4 text-sm font-medium text-white bg-aqua-teal rounded-md transition-all';
                listBtn.className = 'flex-1 py-2 px-4 text-sm font-medium text-secondary-text hover:text-primary-text transition-all';
            } else {
                mapView.classList.add('hidden');
                listView.classList.remove('hidden');
                
                // 更新按钮状态
                listBtn.className = 'flex-1 py-2 px-4 text-sm font-medium text-white bg-aqua-teal rounded-md transition-all';
                mapBtn.className = 'flex-1 py-2 px-4 text-sm font-medium text-secondary-text hover:text-primary-text transition-all';
            }
        }

        // 钓点数据
        const fishingSpotsData = {
            'donghu': {
                name: '东湖公园钓点',
                type: '公园湖泊',
                distance: '2.3km',
                cost: '免费',
                rating: 4.8,
                records: 28,
                lastVisit: '今天',
                successRate: '85%',
                isFavorite: true,
                description: '位于市区东湖公园内的天然湖泊，水质清澈，鱼类丰富。',
                bestTime: '早晨6-9点，傍晚5-7点',
                mainFish: ['鲫鱼', '草鱼', '鲤鱼'],
                facilities: ['停车场', '洗手间', '小卖部']
            },
            'xihu': {
                name: '西湖钓点',
                type: '天然湖泊',
                distance: '5.8km',
                cost: '收费',
                rating: 4.2,
                records: 15,
                lastVisit: '昨天',
                successRate: '72%',
                isFavorite: false,
                description: '风景优美的天然湖泊，适合休闲垂钓。',
                bestTime: '全天',
                mainFish: ['草鱼', '鲫鱼', '鲢鱼'],
                facilities: ['餐厅', '钓具租赁', '观景台']
            },
            'beihe': {
                name: '北河钓场',
                type: '人工鱼塘',
                distance: '12.5km',
                cost: '收费',
                rating: 3.8,
                records: 8,
                lastVisit: '3天前',
                successRate: '60%',
                isFavorite: false,
                description: '专业钓场，鱼种丰富，设施齐全。',
                bestTime: '上午8-12点',
                mainFish: ['鲤鱼', '草鱼', '黑鱼'],
                facilities: ['钓台', '遮阳棚', '鱼具店']
            }
        };

        // 显示钓点详情
        function showSpotDetail(spotId) {
            const spot = fishingSpotsData[spotId];
            if (!spot) return;

            // 这里可以展示钓点详情页面或模态框
            const detailInfo = `
钓点名称: ${spot.name}
类型: ${spot.type}
距离: ${spot.distance}
费用: ${spot.cost}
评分: ${spot.rating}分
记录: ${spot.records}条
最近访问: ${spot.lastVisit}
成功率: ${spot.successRate}

描述: ${spot.description}
最佳时间: ${spot.bestTime}
主要鱼种: ${spot.mainFish.join('、')}
设施: ${spot.facilities.join('、')}
            `;
            
            alert(detailInfo);
        }

        // 添加新钓点
        function addNewSpot() {
            // 模拟添加新钓点的功能
            alert('添加新钓点功能：\n1. 打开地图选择位置\n2. 填写钓点信息\n3. 上传照片\n4. 保存钓点');
        }

        // 收藏/取消收藏钓点
        function toggleFavorite(spotId) {
            if (fishingSpotsData[spotId]) {
                fishingSpotsData[spotId].isFavorite = !fishingSpotsData[spotId].isFavorite;
                // 更新UI显示
                updateSpotDisplay(spotId);
            }
        }

        // 更新钓点显示
        function updateSpotDisplay(spotId) {
            // 这里可以更新钓点在列表和地图中的显示状态
            console.log(`更新钓点 ${spotId} 的显示状态`);
        }

        // 修改导航函数，支持钓点管理页面
        function updateNavigation(activePageId) {
            // 重置所有导航项
            const navItems = ['home', 'fishdex', 'leaderboard', 'profile'];
            navItems.forEach(item => {
                const icon = document.getElementById(`nav-${item}`);
                const text = document.getElementById(`nav-${item}-text`);
                if (icon && text) {
                    icon.className = icon.className.replace('nav-active', 'nav-inactive');
                    text.className = text.className.replace('nav-active', 'nav-inactive');
                }
            });
            
            // 设置当前活跃项
            let navId = activePageId;
            if (activePageId === 'homepage') navId = 'home';
            if (activePageId === 'fish-detail') navId = 'fishdex'; // 鱼种详情归属于图鉴
            if (activePageId === 'career' || activePageId === 'fishing-spots') navId = 'profile'; // 生涯和钓点管理归属于个人中心
            if (activePageId === 'success') return; // 成功页面不更新导航
            
            const activeIcon = document.getElementById(`nav-${navId}`);
            const activeText = document.getElementById(`nav-${navId}-text`);
            if (activeIcon && activeText) {
                activeIcon.className = activeIcon.className.replace('nav-inactive', 'nav-active');
                activeText.className = activeText.className.replace('nav-inactive', 'nav-active');
            }
        }

        // 初始化页面
        showPage('homepage');
    </script>
</body>
</html>
