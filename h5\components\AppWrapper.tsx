'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { HomePage } from './pages/HomePage'
import { RecordPage } from './pages/RecordPage'
import { FishdexPage } from './pages/FishdexPage'
import { LeaderboardPage } from './pages/LeaderboardPage'
import { ProfilePage } from './pages/ProfilePage'
import { FishDetailPage } from './pages/FishDetailPage'
import { CareerPage } from './pages/CareerPage'
import { FishingSpotsPage } from './pages/FishingSpotsPage'
import { RecordSuccessPage } from './pages/RecordSuccessPage'
import { BottomNavigation } from './layout/BottomNavigation'
import { PhotoModal } from './common/PhotoModal'

export type PageType = 'homepage' | 'record' | 'fishdex' | 'leaderboard' | 'profile' | 
                      'fish-detail' | 'career' | 'fishing-spots' | 'success'

interface AppState {
  currentPage: PageType
  fishDetailId?: string
  uploadedPhotos: Array<{url: string, type: string}>
  recordData?: any
}

export function AppWrapper() {
  const [appState, setAppState] = useState<AppState>({
    currentPage: 'homepage',
    uploadedPhotos: []
  })

  const [photoModalData, setPhotoModalData] = useState<{
    isOpen: boolean
    photos: string[]
    currentIndex: number
  }>({
    isOpen: false,
    photos: [],
    currentIndex: 0
  })

  const navigateToPage = useCallback((page: PageType, data?: any) => {
    setAppState(prev => ({
      ...prev,
      currentPage: page,
      ...data
    }))
  }, [])

  const showFishDetail = useCallback((fishId: string) => {
    navigateToPage('fish-detail', { fishDetailId: fishId })
  }, [navigateToPage])

  const openPhotoModal = useCallback((photos: string[], index: number = 0) => {
    setPhotoModalData({
      isOpen: true,
      photos,
      currentIndex: index
    })
  }, [])

  const closePhotoModal = useCallback(() => {
    setPhotoModalData(prev => ({ ...prev, isOpen: false }))
  }, [])

  const updateUploadedPhotos = useCallback((photos: Array<{url: string, type: string}>) => {
    setAppState(prev => ({ ...prev, uploadedPhotos: photos }))
  }, [])

  const saveRecord = useCallback((recordData: any) => {
    setAppState(prev => ({ 
      ...prev, 
      recordData,
      uploadedPhotos: [] // 清空已上传的照片
    }))
    navigateToPage('success')
  }, [navigateToPage])

  const renderCurrentPage = () => {
    const pageProps = {
      navigateToPage,
      showFishDetail,
      openPhotoModal,
      appState
    }

    switch (appState.currentPage) {
      case 'homepage':
        return <HomePage {...pageProps} />
      case 'record':
        return <RecordPage 
          {...pageProps} 
          uploadedPhotos={appState.uploadedPhotos}
          updateUploadedPhotos={updateUploadedPhotos}
          saveRecord={saveRecord}
        />
      case 'fishdex':
        return <FishdexPage {...pageProps} />
      case 'leaderboard':
        return <LeaderboardPage {...pageProps} />
      case 'profile':
        return <ProfilePage {...pageProps} />
      case 'fish-detail':
        return <FishDetailPage 
          {...pageProps} 
          fishId={appState.fishDetailId || ''} 
        />
      case 'career':
        return <CareerPage {...pageProps} />
      case 'fishing-spots':
        return <FishingSpotsPage {...pageProps} />
      case 'success':
        return <RecordSuccessPage 
          {...pageProps}
          recordData={appState.recordData}
          uploadedPhotos={appState.uploadedPhotos}
        />
      default:
        return <HomePage {...pageProps} />
    }
  }

  return (
    <>
      <div className="relative overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={appState.currentPage}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30,
              duration: 0.3
            }}
            className="min-h-screen"
          >
            {renderCurrentPage()}
          </motion.div>
        </AnimatePresence>
      </div>

      <BottomNavigation 
        currentPage={appState.currentPage}
        onNavigate={navigateToPage}
      />

      <PhotoModal
        isOpen={photoModalData.isOpen}
        photos={photoModalData.photos}
        currentIndex={photoModalData.currentIndex}
        onClose={closePhotoModal}
      />
    </>
  )
}
