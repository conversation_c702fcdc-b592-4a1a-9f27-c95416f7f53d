# 钓鱼APP - React Native版本

这是基于H5版本的钓鱼应用的React Native实现，使用Expo框架开发。

## 项目特点

- 🎣 完整的钓鱼记录功能
- 📱 原生移动端体验
- 🎨 精美的UI设计和动画效果
- 📊 数据统计和排行榜
- 🐟 鱼类图鉴系统
- 🏆 成就系统

## 技术栈

- **React Native** - 跨平台移动应用框架
- **Expo** - React Native开发平台
- **TypeScript** - 类型安全的JavaScript
- **React Native Reanimated** - 高性能动画库
- **Expo Linear Gradient** - 渐变背景
- **React Navigation** - 导航管理
- **Expo Vector Icons** - 图标库

## 项目结构

```
front-native/
├── src/
│   ├── components/           # 组件目录
│   │   ├── common/          # 通用组件
│   │   │   ├── AnimatedButton.tsx
│   │   │   ├── GradientBackground.tsx
│   │   │   ├── LoadingSpinner.tsx
│   │   │   └── PhotoModal.tsx
│   │   ├── layout/          # 布局组件
│   │   │   ├── BottomNavigation.tsx
│   │   │   └── TopNavigation.tsx
│   │   ├── pages/           # 页面组件
│   │   │   ├── HomePage.tsx
│   │   │   ├── RecordPage.tsx
│   │   │   ├── FishdexPage.tsx
│   │   │   ├── LeaderboardPage.tsx
│   │   │   ├── ProfilePage.tsx
│   │   │   ├── FishDetailPage.tsx
│   │   │   ├── CareerPage.tsx
│   │   │   ├── FishingSpotsPage.tsx
│   │   │   └── RecordSuccessPage.tsx
│   │   └── AppWrapper.tsx   # 主应用包装器
│   ├── constants/           # 常量定义
│   │   └── colors.ts        # 颜色和样式常量
│   ├── styles/              # 样式文件
│   │   └── index.ts         # 全局样式
│   ├── types/               # 类型定义
│   │   └── index.ts         # TypeScript类型
│   └── utils/               # 工具函数
├── App.tsx                  # 应用入口
├── app.json                 # Expo配置
└── package.json             # 依赖配置
```

## 安装和运行

### 前置要求

- Node.js (版本 16 或更高)
- npm 或 yarn
- Expo CLI (可选，用于更好的开发体验)

### 安装依赖

```bash
cd front-native
npm install
```

### 运行应用

```bash
# 启动开发服务器
npm start

# 在Web浏览器中运行
npm run web

# 在Android设备/模拟器中运行
npm run android

# 在iOS设备/模拟器中运行 (需要macOS)
npm run ios
```

## 功能特性

### 已实现功能

1. **主页面 (HomePage)**
   - 用户信息展示
   - 快速统计数据
   - 记录渔获按钮
   - 本周排行榜
   - 最近记录列表

2. **导航系统**
   - 底部导航栏
   - 页面转场动画
   - 返回按钮

3. **样式系统**
   - 自定义颜色主题
   - 渐变背景
   - 阴影效果
   - 响应式布局

4. **动画效果**
   - 页面切换动画
   - 按钮交互动画
   - 加载动画
   - 入场动画

### 待完善功能

- 记录页面的拍照和表单功能
- 鱼类图鉴的详细实现
- 排行榜数据展示
- 个人中心功能
- 数据持久化
- 网络请求集成

## 设计原则

1. **与H5版本保持一致** - 确保用户体验的连贯性
2. **原生性能优化** - 利用React Native的性能优势
3. **组件化开发** - 提高代码复用性和维护性
4. **类型安全** - 使用TypeScript确保代码质量
5. **响应式设计** - 适配不同屏幕尺寸

## 开发说明

### 样式系统

项目使用自定义样式系统，不依赖NativeWind：

- 颜色定义在 `src/constants/colors.ts`
- 全局样式在 `src/styles/index.ts`
- 组件样式使用StyleSheet.create()

### 动画实现

使用React Native Reanimated v3实现高性能动画：

- 页面转场动画
- 按钮交互反馈
- 加载状态动画
- 列表项入场动画

### 状态管理

当前使用React的useState和useCallback进行状态管理，后续可考虑集成Redux或Zustand。

## 部署

### 构建生产版本

```bash
# 构建Android APK
expo build:android

# 构建iOS IPA
expo build:ios

# 使用EAS Build (推荐)
eas build --platform all
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
