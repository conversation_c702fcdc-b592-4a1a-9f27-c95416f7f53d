var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.StepNumber=void 0;var _react=_interopRequireDefault(require("react"));var _reactNative=require("react-native");var _styles=require("../utils/styles");var _jsxRuntime=require("react/jsx-runtime");var _this=this,_jsxFileName="/Users/<USER>/Desktop/Projects/react-native-slider/package/src/components/StepNumber.tsx";var StepNumber=exports.StepNumber=function StepNumber(_ref){var i=_ref.i,style=_ref.style;return(0,_jsxRuntime.jsx)(_reactNative.View,{style:_styles.styles.stepNumber,children:(0,_jsxRuntime.jsx)(_reactNative.Text,{style:style,children:i})});};