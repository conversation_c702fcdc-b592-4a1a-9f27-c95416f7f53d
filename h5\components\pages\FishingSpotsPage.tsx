'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Plus, Map, List, Star, Navigation, TrendingUp, MapPin, DollarSign, Calendar, Fish, BarChart3, ChevronDown } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import type { PageType } from '../AppWrapper'

interface FishingSpotsPageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  appState: any
}

export function FishingSpotsPage({ navigateToPage }: FishingSpotsPageProps) {
  const [currentView, setCurrentView] = useState<'map' | 'list'>('map')

  const spotStats = {
    total: 25,
    favorites: 8,
    visited: 15
  }

  const fishingSpots = [
    {
      id: 'donghu',
      name: '东湖公园钓点',
      type: '公园湖泊',
      distance: '2.3km',
      cost: '免费',
      rating: 4.8,
      records: 28,
      lastVisit: '今天',
      successRate: '85%',
      isFavorite: true,
      description: '位于市区东湖公园内的天然湖泊，水质清澈，鱼类丰富。',
      bestTime: '早晨6-9点，傍晚5-7点',
      mainFish: ['鲫鱼', '草鱼', '鲤鱼'],
      facilities: ['停车场', '洗手间', '小卖部'],
      position: { top: '16%', left: '20%' }
    },
    {
      id: 'xihu',
      name: '西湖钓点',
      type: '天然湖泊',
      distance: '5.8km',
      cost: '收费',
      rating: 4.2,
      records: 15,
      lastVisit: '昨天',
      successRate: '72%',
      isFavorite: false,
      description: '风景优美的天然湖泊，适合休闲垂钓。',
      bestTime: '全天',
      mainFish: ['草鱼', '鲫鱼', '鲢鱼'],
      facilities: ['餐厅', '钓具租赁', '观景台'],
      position: { top: '32%', left: '40%' }
    },
    {
      id: 'beihe',
      name: '北河钓场',
      type: '人工鱼塘',
      distance: '12.5km',
      cost: '收费',
      rating: 3.8,
      records: 8,
      lastVisit: '3天前',
      successRate: '60%',
      isFavorite: false,
      description: '专业钓场，鱼种丰富，设施齐全。',
      bestTime: '上午8-12点',
      mainFish: ['鲤鱼', '草鱼', '黑鱼'],
      facilities: ['钓台', '遮阳棚', '鱼具店'],
      position: { top: '20%', right: '24%' }
    }
  ]

  const filterOptions = [
    { id: 'all', name: '全部', active: true },
    { id: 'favorites', name: '收藏', active: false },
    { id: 'recent', name: '最近访问', active: false },
    { id: 'productive', name: '高产出', active: false }
  ]

  const switchView = (view: 'map' | 'list') => {
    setCurrentView(view)
  }

  const showSpotDetail = (spotId: string) => {
    const spot = fishingSpots.find(s => s.id === spotId)
    if (!spot) return

    const detailInfo = `
钓点名称: ${spot.name}
类型: ${spot.type}
距离: ${spot.distance}
费用: ${spot.cost}
评分: ${spot.rating}分
记录: ${spot.records}条
最近访问: ${spot.lastVisit}
成功率: ${spot.successRate}

描述: ${spot.description}
最佳时间: ${spot.bestTime}
主要鱼种: ${spot.mainFish.join('、')}
设施: ${spot.facilities.join('、')}
    `
    
    alert(detailInfo)
  }

  const addNewSpot = () => {
    alert('添加新钓点功能：\n1. 打开地图选择位置\n2. 填写钓点信息\n3. 上传照片\n4. 保存钓点')
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={12}
        className={index < Math.floor(rating) ? 'text-sunrise-gold fill-current' : 'text-gray-300'}
      />
    ))
  }

  return (
    <div className="pb-24">
      <TopNavigation
        title="钓点管理"
        showBack
        onBack={() => navigateToPage('profile')}
        rightIcon="plus"
        onRightClick={addNewSpot}
      />

      {/* 视图切换 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="flex items-center bg-white p-1 rounded-lg shadow-sm">
          <motion.button
            onClick={() => switchView('map')}
            className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all flex items-center justify-center space-x-2 ${
              currentView === 'map' 
                ? 'text-white bg-aqua-teal' 
                : 'text-secondary-text hover:text-primary-text'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Map size={16} />
            <span>地图视图</span>
          </motion.button>
          <motion.button
            onClick={() => switchView('list')}
            className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all flex items-center justify-center space-x-2 ${
              currentView === 'list' 
                ? 'text-white bg-aqua-teal' 
                : 'text-secondary-text hover:text-primary-text'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <List size={16} />
            <span>列表视图</span>
          </motion.button>
        </div>
      </motion.div>

      {/* 地图视图 */}
      {currentView === 'map' && (
        <motion.div 
          className="px-6 py-4"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            {/* 模拟地图区域 */}
            <div className="relative h-64 bg-gradient-to-br from-blue-100 to-green-100">
              {/* 地图背景 */}
              <div className="absolute inset-0 opacity-20">
                <div className="w-full h-full bg-gradient-to-br from-blue-200 via-green-200 to-blue-300"></div>
              </div>
              
              {/* 钓点标记 */}
              {fishingSpots.map((spot, index) => (
                <motion.div
                  key={spot.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2"
                  style={spot.position}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.3 + index * 0.1, type: "spring", stiffness: 300 }}
                >
                  <div className="relative">
                    <motion.div
                      className={`w-8 h-8 ${spot.isFavorite ? 'bg-sunrise-gold' : 'bg-aqua-teal'} rounded-full flex items-center justify-center shadow-lg cursor-pointer`}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => showSpotDetail(spot.id)}
                    >
                      {spot.isFavorite ? (
                        <Star className="text-white" size={16} />
                      ) : (
                        <MapPin className="text-white" size={16} />
                      )}
                    </motion.div>
                    <motion.div 
                      className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-primary-text whitespace-nowrap"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                    >
                      {spot.name.replace('钓点', '').replace('钓场', '')}
                    </motion.div>
                    <div className="pulse-ring"></div>
                  </div>
                </motion.div>
              ))}

              {/* 地图控制按钮 */}
              <div className="absolute top-4 right-4 flex flex-col space-y-2">
                <motion.button 
                  className="w-8 h-8 bg-white rounded shadow flex items-center justify-center hover:bg-gray-50 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Plus className="text-gray-600" size={16} />
                </motion.button>
                <motion.button 
                  className="w-8 h-8 bg-white rounded shadow flex items-center justify-center hover:bg-gray-50 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <span className="text-gray-600 text-sm font-bold">-</span>
                </motion.button>
                <motion.button 
                  className="w-8 h-8 bg-white rounded shadow flex items-center justify-center hover:bg-gray-50 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Navigation className="text-gray-600" size={14} />
                </motion.button>
              </div>

              {/* 添加钓点提示 */}
              <motion.div 
                className="absolute bottom-4 left-4 right-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <div className="bg-white bg-opacity-90 p-3 rounded-lg">
                  <div className="text-xs text-primary-text font-medium mb-1">💡 使用提示</div>
                  <div className="text-xs text-secondary-text">
                    点击地图上的标记查看钓点详情，点击右上角+号添加新钓点
                  </div>
                </div>
              </motion.div>
            </div>

            {/* 地图底部信息 */}
            <div className="p-4 border-t border-divider">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-aqua-teal rounded-full"></div>
                    <span className="text-xs text-secondary-text">常规钓点</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-sunrise-gold rounded-full"></div>
                    <span className="text-xs text-secondary-text">收藏钓点</span>
                  </div>
                </div>
                <div className="text-xs text-secondary-text">共 {spotStats.total} 个钓点</div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* 列表视图 */}
      {currentView === 'list' && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* 钓点统计 */}
          <motion.div 
            className="px-6 py-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <div className="bg-white p-4 rounded-xl shadow-sm">
              <h3 className="text-lg font-bold text-primary-text mb-4">📊 钓点统计</h3>
              <div className="grid grid-cols-3 gap-3">
                {[
                  { value: spotStats.total, label: '总钓点', color: 'text-aqua-teal' },
                  { value: spotStats.favorites, label: '收藏点', color: 'text-sunrise-gold' },
                  { value: spotStats.visited, label: '本月去过', color: 'text-primary-text' }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    className="text-center p-3 bg-light-slate rounded-lg"
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.2 + index * 0.1 }}
                  >
                    <div className={`text-xl font-bold ${stat.color}`}>{stat.value}</div>
                    <div className="text-xs text-secondary-text">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* 钓点筛选 */}
          <motion.div 
            className="px-6 py-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="flex space-x-2 overflow-x-auto">
              {filterOptions.map((option, index) => (
                <motion.button
                  key={option.id}
                  className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all ${
                    option.active 
                      ? 'bg-aqua-teal text-white' 
                      : 'bg-white text-secondary-text border border-divider hover:border-aqua-teal'
                  }`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 + index * 0.05 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {option.name}
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* 钓点列表 */}
          <motion.div 
            className="px-6 py-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="p-4 border-b border-divider">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold text-primary-text">钓点列表</h3>
                  <div className="flex items-center space-x-2 text-sm text-secondary-text">
                    <span>按访问频次排序</span>
                    <TrendingUp size={14} />
                  </div>
                </div>
              </div>

              {/* 钓点项目 */}
              <div className="divide-y divide-divider">
                {fishingSpots.map((spot, index) => (
                  <motion.div
                    key={spot.id}
                    className="p-4 hover:bg-light-slate transition-colors cursor-pointer fishing-spot-item"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    whileHover={{ x: 4 }}
                    onClick={() => showSpotDetail(spot.id)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="relative">
                        <div className={`w-12 h-12 ${
                          spot.type === '公园湖泊' ? 'bg-gradient-to-br from-blue-400 to-green-400' :
                          spot.type === '天然湖泊' ? 'bg-gradient-to-br from-green-400 to-blue-400' :
                          'bg-gradient-to-br from-blue-500 to-cyan-400'
                        } rounded-lg flex items-center justify-center`}>
                          {spot.type === '公园湖泊' ? (
                            <MapPin className="text-white" size={20} />
                          ) : spot.type === '天然湖泊' ? (
                            <Fish className="text-white" size={20} />
                          ) : (
                            <BarChart3 className="text-white" size={20} />
                          )}
                        </div>
                        {spot.isFavorite && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-sunrise-gold rounded-full flex items-center justify-center">
                            <Star className="text-white" size={10} />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-bold text-primary-text">{spot.name}</h4>
                          {spot.isFavorite && (
                            <span className="text-xs bg-sunrise-gold text-white px-2 py-1 rounded">收藏</span>
                          )}
                        </div>
                        <div className="text-xs text-secondary-text mb-2">
                          距离 {spot.distance} • {spot.type} • {spot.cost}
                        </div>
                        <div className="grid grid-cols-3 gap-3 text-xs">
                          <div className="flex items-center space-x-1">
                            <Fish className="text-aqua-teal" size={12} />
                            <span className="text-secondary-text">{spot.records}条记录</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="text-aqua-teal" size={12} />
                            <span className="text-secondary-text">最近：{spot.lastVisit}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <BarChart3 className="text-aqua-teal" size={12} />
                            <span className="text-secondary-text">成功率 {spot.successRate}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-bold text-primary-text">{spot.rating}</div>
                        <div className="flex items-center text-xs">
                          {renderStars(spot.rating)}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}

                {/* 查看更多 */}
                <motion.div 
                  className="p-4 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.0 }}
                >
                  <motion.button 
                    className="text-aqua-teal text-sm font-medium flex items-center justify-center space-x-1"
                    whileHover={{ scale: 1.05 }}
                  >
                    <span>查看全部 {spotStats.total} 个钓点</span>
                    <ChevronDown size={14} />
                  </motion.button>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* 钓点管理提示 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 p-4 rounded-xl border border-aqua-teal/20">
          <div className="flex items-center space-x-2 mb-2">
            <MapPin className="text-aqua-teal" size={20} />
            <span className="text-sm font-medium text-primary-text">钓点管理</span>
          </div>
          <div className="text-xs text-secondary-text leading-relaxed">
            • 记录每次钓鱼时会自动添加GPS定位<br/>
            • 收藏常用钓点便于快速导航<br/>
            • 查看钓点统计数据优化垂钓策略<br/>
            • 分享优质钓点给钓友社区
          </div>
        </div>
      </motion.div>
    </div>
  )
}
