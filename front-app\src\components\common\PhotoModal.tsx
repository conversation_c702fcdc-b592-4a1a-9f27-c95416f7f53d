import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  View, 
  Image, 
  Text, 
  TouchableOpacity, 
  ScrollView,
  Dimensions,
  StatusBar
} from 'react-native';
import { BlurView } from 'expo-blur';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  withSpring,
  interpolate
} from 'react-native-reanimated';
import { X, ChevronLeft, ChevronRight } from 'lucide-react-native';
import { colors, borderRadius, spacing, fontSize } from '../../constants/styles';

interface PhotoModalProps {
  isOpen: boolean;
  photos: string[];
  currentIndex: number;
  onClose: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export function PhotoModal({ isOpen, photos, currentIndex, onClose }: PhotoModalProps) {
  const [activeIndex, setActiveIndex] = useState(currentIndex);
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);

  useEffect(() => {
    setActiveIndex(currentIndex);
  }, [currentIndex]);

  useEffect(() => {
    if (isOpen) {
      opacity.value = withTiming(1, { duration: 300 });
      scale.value = withSpring(1);
    } else {
      opacity.value = withTiming(0, { duration: 300 });
      scale.value = withTiming(0.8, { duration: 300 });
    }
  }, [isOpen, opacity, scale]);

  const navigatePhoto = (direction: number) => {
    const newIndex = activeIndex + direction;
    if (newIndex < 0) {
      setActiveIndex(photos.length - 1);
    } else if (newIndex >= photos.length) {
      setActiveIndex(0);
    } else {
      setActiveIndex(newIndex);
    }
  };

  const goToPhoto = (index: number) => {
    setActiveIndex(index);
  };

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  if (!isOpen || photos.length === 0) return null;

  return (
    <Modal
      visible={isOpen}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <Animated.View 
        style={[
          { flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.9)' }, 
          modalAnimatedStyle
        ]}
      >
        <BlurView
          intensity={20}
          tint="dark"
          style={{ flex: 1 }}
        >
          <View style={{ 
            flex: 1, 
            alignItems: 'center', 
            justifyContent: 'center', 
            padding: spacing.md 
          }}>
            {/* 关闭按钮 */}
            <TouchableOpacity
              onPress={onClose}
              style={{
                position: 'absolute',
                top: 48,
                right: spacing.md,
                width: 40,
                height: 40,
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: borderRadius.full,
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 10,
                elevation: 5
              }}
            >
              <X size={20} color="white" />
            </TouchableOpacity>

            {/* 导航按钮 */}
            {photos.length > 1 && (
              <>
                <TouchableOpacity
                  onPress={() => navigatePhoto(-1)}
                  style={{
                    position: 'absolute',
                    left: spacing.md,
                    top: '50%',
                    width: 48,
                    height: 48,
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: borderRadius.full,
                    alignItems: 'center',
                    justifyContent: 'center',
                    transform: [{ translateY: -24 }],
                    elevation: 5
                  }}
                >
                  <ChevronLeft size={24} color="white" />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => navigatePhoto(1)}
                  style={{
                    position: 'absolute',
                    right: spacing.md,
                    top: '50%',
                    width: 48,
                    height: 48,
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: borderRadius.full,
                    alignItems: 'center',
                    justifyContent: 'center',
                    transform: [{ translateY: -24 }],
                    elevation: 5
                  }}
                >
                  <ChevronRight size={24} color="white" />
                </TouchableOpacity>
              </>
            )}

            {/* 主要照片显示 */}
            <Animated.View 
              style={[contentAnimatedStyle, { alignItems: 'center' }]}
            >
              <View style={{ 
                maxWidth: screenWidth - 32, 
                maxHeight: screenHeight * 0.7 
              }}>
                <Image
                  source={{ uri: photos[activeIndex] }}
                  style={{ 
                    width: screenWidth - 32,
                    height: screenHeight * 0.6,
                    borderRadius: borderRadius.md
                  }}
                  resizeMode="contain"
                />
              </View>
              <View style={{ marginTop: spacing.md }}>
                <Text style={{
                  color: colors.white,
                  fontSize: fontSize.sm,
                  opacity: 0.8,
                  textAlign: 'center'
                }}>
                  照片 {activeIndex + 1} / {photos.length}
                </Text>
              </View>
            </Animated.View>

            {/* 底部缩略图导航 */}
            {photos.length > 1 && (
              <View style={{
                position: 'absolute',
                bottom: 32,
                left: '50%',
                transform: [{ translateX: -((photos.length * 56) / 2) }]
              }}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={{ 
                    flexDirection: 'row', 
                    gap: spacing.sm 
                  }}>
                    {photos.map((photo, index) => (
                      <TouchableOpacity
                        key={index}
                        onPress={() => goToPhoto(index)}
                        style={{
                          width: 48,
                          height: 48,
                          borderRadius: borderRadius.md,
                          overflow: 'hidden',
                          borderWidth: 2,
                          borderColor: index === activeIndex 
                            ? colors.sunriseGold 
                            : 'transparent'
                        }}
                      >
                        <Image
                          source={{ uri: photo }}
                          style={{ width: '100%', height: '100%' }}
                          resizeMode="cover"
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}
          </View>
        </BlurView>
      </Animated.View>
    </Modal>
  );
}