{"version": "0.1.22", "name": "react-native-css-interop", "description": "", "main": "dist/index", "types": "dist/index.d.ts", "sideEffects": false, "keywords": ["react-native", "react", "native", "theme", "style"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/marklawlor/nativewind.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/marklawlor"}, "homepage": "https://nativewind.dev", "bugs": {"url": "https://github.com/marklawlor/nativewind/issues"}, "engines": {"node": ">=18"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "lint": "eslint .", "prepublishOnly": "npm run build && npm test", "build": "tsc -p tsconfig.build.json", "build:watch": "tsc -p tsconfig.build.json -w", "dev": "tsc -p tsconfig.build.json --watch --preserveWatchOutput", "cloc": "npm run build && cloc --by-file --exclude-ext=d.ts ./dist/runtime/native ./dist/runtime/api.native.js ./dist/runtime/observable.js ./dist/runtime/jsx-runtime.js ./dist/runtime/wrap-jsx.js ./dist/runtime/config.js"}, "files": ["babel.js", "css-to-rn/", "dist/", "src/", "doctor/", "jsx-dev-runtime/", "jsx-runtime/", "metro/", "test/", "types.d.ts"], "dependencies": {"@babel/helper-module-imports": "^7.22.15", "@babel/traverse": "^7.23.0", "@babel/types": "^7.23.0", "debug": "^4.3.7", "lightningcss": "^1.27.0", "semver": "^7.6.3"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.22.5", "@testing-library/react-native": "^12.0.1", "@types/babel__helper-module-imports": "^7.18.1", "@types/jest": "^29.5.1", "@types/react": "^18.2.14", "babel-plugin-tester": "^11.0.4", "metro-react-native-babel-preset": "^0.77.0", "react": "^18.2.0", "react-native": "0.75.2", "react-native-reanimated": "3.10.1"}, "peerDependencies": {"react": ">=18", "react-native": "*", "react-native-reanimated": ">=3.6.2", "tailwindcss": "~3"}, "peerDependenciesMeta": {"react-native-svg": {"optional": true}, "react-native-safe-area-context": {"optional": true}}}