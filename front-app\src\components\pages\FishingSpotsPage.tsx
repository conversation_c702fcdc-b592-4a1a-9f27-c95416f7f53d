import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Plus, Map, List, Star, Navigation, TrendingUp, MapPin, DollarSign, Calendar, Fish, BarChart3, ChevronDown } from 'lucide-react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  withSpring
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { TopNavigation } from '../layout/TopNavigation';
import type { PageType } from '../AppWrapper';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface FishingSpotsPageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  appState: any;
}

export function FishingSpotsPage({ navigateToPage }: FishingSpotsPageProps) {
  const [currentView, setCurrentView] = useState<'map' | 'list'>('map');
  
  const viewSwitchOpacity = useSharedValue(0);
  const mapOpacity = useSharedValue(0);
  const listOpacity = useSharedValue(0);
  const tipsOpacity = useSharedValue(0);

  React.useEffect(() => {
    viewSwitchOpacity.value = withDelay(100, withTiming(1, { duration: 600 }));
    if (currentView === 'map') {
      mapOpacity.value = withTiming(1, { duration: 300 });
      listOpacity.value = withTiming(0, { duration: 300 });
    } else {
      listOpacity.value = withTiming(1, { duration: 300 });
      mapOpacity.value = withTiming(0, { duration: 300 });
    }
    tipsOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
  }, [currentView]);

  const viewSwitchStyle = useAnimatedStyle(() => ({
    opacity: viewSwitchOpacity.value,
    transform: [{ translateY: withTiming(viewSwitchOpacity.value === 1 ? 0 : 20) }],
  }));

  const mapStyle = useAnimatedStyle(() => ({
    opacity: mapOpacity.value,
  }));

  const listStyle = useAnimatedStyle(() => ({
    opacity: listOpacity.value,
  }));

  const tipsStyle = useAnimatedStyle(() => ({
    opacity: tipsOpacity.value,
    transform: [{ translateY: withTiming(tipsOpacity.value === 1 ? 0 : 20) }],
  }));

  const spotStats = {
    total: 25,
    favorites: 8,
    visited: 15
  };

  const fishingSpots = [
    {
      id: 'donghu',
      name: '东湖公园钓点',
      type: '公园湖泊',
      distance: '2.3km',
      cost: '免费',
      rating: 4.8,
      records: 28,
      lastVisit: '今天',
      successRate: '85%',
      isFavorite: true,
      description: '位于市区东湖公园内的天然湖泊，水质清澈，鱼类丰富。',
      bestTime: '早晨6-9点，傍晚5-7点',
      mainFish: ['鲫鱼', '草鱼', '鲤鱼'],
      facilities: ['停车场', '洗手间', '小卖部']
    },
    {
      id: 'xihu',
      name: '西湖钓点',
      type: '天然湖泊',
      distance: '5.8km',
      cost: '收费',
      rating: 4.2,
      records: 15,
      lastVisit: '昨天',
      successRate: '72%',
      isFavorite: false,
      description: '风景优美的天然湖泊，适合休闲垂钓。',
      bestTime: '全天',
      mainFish: ['草鱼', '鲫鱼', '鲢鱼'],
      facilities: ['餐厅', '钓具租赁', '观景台']
    },
    {
      id: 'beihe',
      name: '北河钓场',
      type: '人工鱼塘',
      distance: '12.5km',
      cost: '收费',
      rating: 3.8,
      records: 8,
      lastVisit: '3天前',
      successRate: '60%',
      isFavorite: false,
      description: '专业钓场，鱼种丰富，设施齐全。',
      bestTime: '上午8-12点',
      mainFish: ['鲤鱼', '草鱼', '黑鱼'],
      facilities: ['钓台', '遮阳棚', '鱼具店']
    }
  ];

  const filterOptions = [
    { id: 'all', name: '全部', active: true },
    { id: 'favorites', name: '收藏', active: false },
    { id: 'recent', name: '最近访问', active: false },
    { id: 'productive', name: '高产出', active: false }
  ];

  const switchView = (view: 'map' | 'list') => {
    setCurrentView(view);
  };

  const showSpotDetail = (spotId: string) => {
    const spot = fishingSpots.find(s => s.id === spotId);
    if (!spot) return;

    const detailInfo = `钓点名称: ${spot.name}
类型: ${spot.type}
距离: ${spot.distance}
费用: ${spot.cost}
评分: ${spot.rating}分
记录: ${spot.records}条
最近访问: ${spot.lastVisit}
成功率: ${spot.successRate}

描述: ${spot.description}
最佳时间: ${spot.bestTime}
主要鱼种: ${spot.mainFish.join('、')}
设施: ${spot.facilities.join('、')}`;
    
    Alert.alert('钓点详情', detailInfo);
  };

  const addNewSpot = () => {
    Alert.alert('添加钓点', '添加新钓点功能：\n1. 打开地图选择位置\n2. 填写钓点信息\n3. 上传照片\n4. 保存钓点');
  };

  const renderStars = (rating: number) => {
    return (
      <View className="flex-row">
        {Array.from({ length: 5 }, (_, index) => (
          <Star
            key={index}
            size={12}
            color={index < Math.floor(rating) ? '#FFC759' : '#E5E7EB'}
            fill={index < Math.floor(rating) ? '#FFC759' : '#E5E7EB'}
          />
        ))}
      </View>
    );
  };

  const getSpotIcon = (type: string) => {
    switch (type) {
      case '公园湖泊':
        return <MapPin color="white" size={20} />;
      case '天然湖泊':
        return <Fish color="white" size={20} />;
      default:
        return <BarChart3 color="white" size={20} />;
    }
  };

  const getSpotGradient = (type: string) => {
    switch (type) {
      case '公园湖泊':
        return ['#3B82F6', '#10B981'];
      case '天然湖泊':
        return ['#10B981', '#3B82F6'];
      default:
        return ['#2563EB', '#0891B2'];
    }
  };

  return (
    <ScrollView 
      className="flex-1 bg-page-bg" 
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <View>
        <TopNavigation
          title="钓点管理"
          showBack
          onBack={() => navigateToPage('profile')}
          rightIcon="plus"
          onRightClick={addNewSpot}
        />

        {/* 视图切换 */}
        <AnimatedView style={viewSwitchStyle} className="px-6 py-4">
          <View className="flex-row bg-white p-1 rounded-lg shadow-sm">
            <TouchableOpacity
              onPress={() => switchView('map')}
              className={`flex-1 py-2 px-4 rounded-md flex-row items-center justify-center gap-x-2 ${
                currentView === 'map' ? 'bg-aqua-teal' : 'bg-transparent'
              }`}
            >
              <Map color={currentView === 'map' ? 'white' : '#6B7280'} size={16} />
              <Text className={`text-sm font-medium ${
                currentView === 'map' ? 'text-white' : 'text-secondary-text'
              }`}>地图视图</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => switchView('list')}
              className={`flex-1 py-2 px-4 rounded-md flex-row items-center justify-center gap-x-2 ${
                currentView === 'list' ? 'bg-aqua-teal' : 'bg-transparent'
              }`}
            >
              <List color={currentView === 'list' ? 'white' : '#6B7280'} size={16} />
              <Text className={`text-sm font-medium ${
                currentView === 'list' ? 'text-white' : 'text-secondary-text'
              }`}>列表视图</Text>
            </TouchableOpacity>
          </View>
        </AnimatedView>

        {/* 地图视图 */}
        {currentView === 'map' && (
          <AnimatedView style={mapStyle} className="px-6 py-4">
            <View className="bg-white rounded-xl shadow-sm overflow-hidden">
              {/* 模拟地图区域 */}
              <View className="h-64 relative overflow-hidden">
                <LinearGradient
                  colors={['#DBEAFE', '#D1FAE5']}
                  className="absolute inset-0"
                />
                
                {/* 钓点标记 */}
                <View className="absolute top-12 left-16">
                  <TouchableOpacity
                    className="w-8 h-8 bg-sunrise-gold rounded-full items-center justify-center shadow-lg"
                    onPress={() => showSpotDetail('donghu')}
                  >
                    <Star color="white" size={16} />
                  </TouchableOpacity>
                  <View className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow">
                    <Text className="text-xs text-primary-text">东湖公园</Text>
                  </View>
                </View>

                <View className="absolute top-20 right-20">
                  <TouchableOpacity
                    className="w-8 h-8 bg-aqua-teal rounded-full items-center justify-center shadow-lg"
                    onPress={() => showSpotDetail('xihu')}
                  >
                    <MapPin color="white" size={16} />
                  </TouchableOpacity>
                  <View className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow">
                    <Text className="text-xs text-primary-text">西湖</Text>
                  </View>
                </View>

                <View className="absolute top-16 right-16">
                  <TouchableOpacity
                    className="w-8 h-8 bg-aqua-teal rounded-full items-center justify-center shadow-lg"
                    onPress={() => showSpotDetail('beihe')}
                  >
                    <MapPin color="white" size={16} />
                  </TouchableOpacity>
                  <View className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow">
                    <Text className="text-xs text-primary-text">北河钓场</Text>
                  </View>
                </View>

                {/* 地图控制按钮 */}
                <View className="absolute top-4 right-4 gap-y-2">
                  <TouchableOpacity className="w-8 h-8 bg-white rounded shadow items-center justify-center">
                    <Plus color="#6B7280" size={16} />
                  </TouchableOpacity>
                  <TouchableOpacity className="w-8 h-8 bg-white rounded shadow items-center justify-center">
                    <Text className="text-gray-600 text-sm font-bold">-</Text>
                  </TouchableOpacity>
                  <TouchableOpacity className="w-8 h-8 bg-white rounded shadow items-center justify-center">
                    <Navigation color="#6B7280" size={14} />
                  </TouchableOpacity>
                </View>

                {/* 添加钓点提示 */}
                <View className="absolute bottom-4 left-4 right-4">
                  <View className="bg-white/90 p-3 rounded-lg">
                    <Text className="text-xs text-primary-text font-medium mb-1">💡 使用提示</Text>
                    <Text className="text-xs text-secondary-text">
                      点击地图上的标记查看钓点详情，点击右上角+号添加新钓点
                    </Text>
                  </View>
                </View>
              </View>

              {/* 地图底部信息 */}
              <View className="p-4 border-t border-gray-100">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center gap-x-4">
                    <View className="flex-row items-center gap-x-2">
                      <View className="w-3 h-3 bg-aqua-teal rounded-full" />
                      <Text className="text-xs text-secondary-text">常规钓点</Text>
                    </View>
                    <View className="flex-row items-center gap-x-2">
                      <View className="w-3 h-3 bg-sunrise-gold rounded-full" />
                      <Text className="text-xs text-secondary-text">收藏钓点</Text>
                    </View>
                  </View>
                  <Text className="text-xs text-secondary-text">共 {spotStats.total} 个钓点</Text>
                </View>
              </View>
            </View>
          </AnimatedView>
        )}

        {/* 列表视图 */}
        {currentView === 'list' && (
          <AnimatedView style={listStyle}>
            {/* 钓点统计 */}
            <View className="px-6 py-4">
              <View className="bg-white p-4 rounded-xl shadow-sm">
                <Text className="text-lg font-bold text-primary-text mb-4">📊 钓点统计</Text>
                <View className="flex-row justify-around">
                  {[
                    { value: spotStats.total, label: '总钓点', color: '#00A79D' },
                    { value: spotStats.favorites, label: '收藏点', color: '#FFC759' },
                    { value: spotStats.visited, label: '本月去过', color: '#121212' }
                  ].map((stat) => (
                    <View key={stat.label} className="items-center p-3 bg-light-slate rounded-lg">
                      <Text 
                        className="text-xl font-bold"
                        style={{ color: stat.color }}
                      >
                        {stat.value}
                      </Text>
                      <Text className="text-xs text-secondary-text">{stat.label}</Text>
                    </View>
                  ))}
                </View>
              </View>
            </View>

            {/* 钓点筛选 */}
            <View className="px-6 py-2">
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View className="flex-row gap-x-2">
                  {filterOptions.map((option) => (
                    <TouchableOpacity
                      key={option.id}
                      className={`px-4 py-2 rounded-full ${
                        option.active 
                          ? 'bg-aqua-teal' 
                          : 'bg-white border border-gray-200'
                      }`}
                    >
                      <Text className={`text-sm ${
                        option.active ? 'text-white' : 'text-secondary-text'
                      }`}>
                        {option.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            {/* 钓点列表 */}
            <View className="px-6 py-4">
              <View className="bg-white rounded-xl shadow-sm overflow-hidden">
                <View className="p-4 border-b border-gray-100">
                  <View className="flex-row items-center justify-between">
                    <Text className="text-lg font-bold text-primary-text">钓点列表</Text>
                    <View className="flex-row items-center space-x-2">
                      <Text className="text-sm text-secondary-text">按访问频次排序</Text>
                      <TrendingUp color="#6B7280" size={14} />
                    </View>
                  </View>
                </View>

                {/* 钓点项目 */}
                {fishingSpots.map((spot) => (
                  <TouchableOpacity
                    key={spot.id}
                    className="p-4 border-b border-gray-50"
                    onPress={() => showSpotDetail(spot.id)}
                  >
                    <View className="flex-row items-start space-x-3">
                      <View className="relative">
                        <LinearGradient
                          colors={getSpotGradient(spot.type)}
                          className="w-12 h-12 rounded-lg items-center justify-center"
                        >
                          {getSpotIcon(spot.type)}
                        </LinearGradient>
                        {spot.isFavorite && (
                          <View className="absolute -top-1 -right-1 w-5 h-5 bg-sunrise-gold rounded-full items-center justify-center">
                            <Star color="white" size={10} />
                          </View>
                        )}
                      </View>
                      <View className="flex-1">
                        <View className="flex-row items-center gap-x-2 mb-1">
                          <Text className="text-sm font-bold text-primary-text">{spot.name}</Text>
                          {spot.isFavorite && (
                            <View className="bg-sunrise-gold px-2 py-1 rounded">
                              <Text className="text-xs text-white">收藏</Text>
                            </View>
                          )}
                        </View>
                        <Text className="text-xs text-secondary-text mb-2">
                          距离 {spot.distance} • {spot.type} • {spot.cost}
                        </Text>
                        <View className="gap-y-1">
                          <View className="flex-row items-center gap-x-1">
                            <Fish color="#00A79D" size={12} />
                            <Text className="text-xs text-secondary-text">{spot.records}条记录</Text>
                          </View>
                          <View className="flex-row items-center gap-x-1">
                            <Calendar color="#00A79D" size={12} />
                            <Text className="text-xs text-secondary-text">最近：{spot.lastVisit}</Text>
                          </View>
                          <View className="flex-row items-center gap-x-1">
                            <BarChart3 color="#00A79D" size={12} />
                            <Text className="text-xs text-secondary-text">成功率 {spot.successRate}</Text>
                          </View>
                        </View>
                      </View>
                      <View className="items-end">
                        <Text className="text-sm font-bold text-primary-text">{spot.rating}</Text>
                        {renderStars(spot.rating)}
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}

                {/* 查看更多 */}
                <TouchableOpacity className="p-4 items-center">
                  <View className="flex-row items-center gap-x-1">
                    <Text className="text-aqua-teal text-sm font-medium">
                      查看全部 {spotStats.total} 个钓点
                    </Text>
                    <ChevronDown color="#00A79D" size={14} />
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </AnimatedView>
        )}

        {/* 钓点管理提示 */}
        <AnimatedView style={tipsStyle} className="px-6 py-4">
          <LinearGradient
            colors={['rgba(0,167,157,0.1)', 'rgba(255,199,89,0.1)']}
            className="p-4 rounded-xl border border-aqua-teal/20"
          >
            <View className="flex-row items-center space-x-2 mb-2">
              <MapPin color="#00A79D" size={20} />
              <Text className="text-sm font-medium text-primary-text">钓点管理</Text>
            </View>
            <Text className="text-xs text-secondary-text leading-5">
              • 记录每次钓鱼时会自动添加GPS定位{'\n'}
              • 收藏常用钓点便于快速导航{'\n'}
              • 查看钓点统计数据优化垂钓策略{'\n'}
              • 分享优质钓点给钓友社区
            </Text>
          </LinearGradient>
        </AnimatedView>
      </View>
    </ScrollView>
  );
}