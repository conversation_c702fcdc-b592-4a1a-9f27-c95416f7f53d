'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Camera, Image as ImageIcon, X, Check, Star } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import { AnimatedButton } from '../common/AnimatedButton'
import { LoadingSpinner } from '../common/LoadingSpinner'
import type { PageType } from '../AppWrapper'

interface RecordPageProps {
  navigateToPage: (page: PageType) => void
  uploadedPhotos: Array<{url: string, type: string}>
  updateUploadedPhotos: (photos: Array<{url: string, type: string}>) => void
  saveRecord: (recordData: any) => void
  appState: any
}

export function RecordPage({ 
  navigateToPage, 
  uploadedPhotos, 
  updateUploadedPhotos, 
  saveRecord 
}: RecordPageProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isScanning, setIsScanning] = useState(false)
  const [showUnlockEffect, setShowUnlockEffect] = useState(false)
  const [scanProgress, setScanProgress] = useState(0)
  const [identificationResult, setIdentificationResult] = useState<any>(null)

  const goToStep = useCallback((step: number) => {
    if (step === 2 && uploadedPhotos.length === 0) {
      alert('请先上传至少一张照片')
      return
    }
    
    setCurrentStep(step)
    
    if (step === 2) {
      startAIScanning()
    }
  }, [uploadedPhotos])

  const startAIScanning = useCallback(() => {
    setIsScanning(true)
    setScanProgress(0)
    
    // 模拟扫描进度
    const progressInterval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval)
          setIsScanning(false)
          setIdentificationResult({
            fishName: '鲫鱼',
            scientificName: 'Crucian Carp',
            confidence: 96,
            isNewSpecies: true
          })
          setCurrentStep(3)
          // 显示解锁特效
          setTimeout(() => setShowUnlockEffect(true), 500)
          return 100
        }
        return prev + 2
      })
    }, 100)
  }, [])

  const hideUnlockEffect = useCallback(() => {
    setShowUnlockEffect(false)
  }, [])

  const simulatePhotoCapture = useCallback(() => {
    const newPhoto = {
      url: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=300&fit=crop&auto=format',
      type: '拍摄照片'
    }
    updateUploadedPhotos([...uploadedPhotos, newPhoto])
  }, [uploadedPhotos, updateUploadedPhotos])

  const simulatePhotoUpload = useCallback(() => {
    const photos = [
      'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=300&h=300&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=300&h=300&fit=crop&auto=format'
    ]
    const randomPhoto = {
      url: photos[Math.floor(Math.random() * photos.length)],
      type: '上传照片'
    }
    updateUploadedPhotos([...uploadedPhotos, randomPhoto])
  }, [uploadedPhotos, updateUploadedPhotos])

  const removePhoto = useCallback((index: number) => {
    const newPhotos = uploadedPhotos.filter((_, i) => i !== index)
    updateUploadedPhotos(newPhotos)
  }, [uploadedPhotos, updateUploadedPhotos])

  const clearPhotos = useCallback(() => {
    updateUploadedPhotos([])
  }, [updateUploadedPhotos])

  const handleSaveRecord = useCallback(() => {
    const recordData = {
      fishName: identificationResult?.fishName || '鲫鱼',
      scientificName: identificationResult?.scientificName || 'Crucian Carp',
      confidence: identificationResult?.confidence || 96,
      photos: uploadedPhotos,
      weight: 0.8,
      length: 25,
      location: '东湖公园钓点',
      timestamp: new Date(),
      isNewSpecies: identificationResult?.isNewSpecies || false
    }
    saveRecord(recordData)
  }, [identificationResult, uploadedPhotos, saveRecord])

  const renderStep1 = () => (
    <motion.div 
      className="step-content px-6 py-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="bg-white p-6 rounded-xl shadow-sm text-center">
        <h3 className="text-lg font-bold text-primary-text mb-2">拍摄或上传渔获照片</h3>
        <p className="text-sm text-secondary-text mb-6">可以上传多张照片，AI将自动选择最佳角度识别</p>
        
        {/* 照片预览区域 */}
        <AnimatePresence>
          {uploadedPhotos.length > 0 ? (
            <motion.div 
              className="mb-6"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <div className="grid grid-cols-2 gap-3">
                {uploadedPhotos.map((photo, index) => (
                  <motion.div
                    key={index}
                    className="photo-item"
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Image
                      src={photo.url}
                      alt={photo.type}
                      width={150}
                      height={150}
                      className="w-full h-full object-cover"
                    />
                    <motion.button
                      onClick={() => removePhoto(index)}
                      className="remove-btn"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <X size={12} />
                    </motion.button>
                  </motion.div>
                ))}
                
                {uploadedPhotos.length < 4 && (
                  <motion.div
                    className="photo-item border-dashed bg-light-slate flex items-center justify-center cursor-pointer"
                    onClick={simulatePhotoUpload}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span className="text-secondary-text text-2xl">+</span>
                  </motion.div>
                )}
              </div>
              <motion.button 
                onClick={clearPhotos}
                className="mt-3 text-sm text-secondary-text underline"
                whileHover={{ scale: 1.05 }}
              >
                清除所有照片
              </motion.button>
            </motion.div>
          ) : (
            <motion.div 
              className="mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className="w-32 h-32 mx-auto bg-light-slate rounded-xl flex items-center justify-center border-2 border-dashed border-divider mb-4">
                <Camera className="text-secondary-text" size={48} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="grid grid-cols-2 gap-3 mb-4">
          <AnimatedButton
            onClick={simulatePhotoCapture}
            variant="primary"
            icon={<Camera size={20} />}
          >
            立即拍照
          </AnimatedButton>
          <AnimatedButton
            onClick={simulatePhotoUpload}
            variant="secondary"
            icon={<ImageIcon size={20} />}
          >
            从相册选择
          </AnimatedButton>
        </div>

        <AnimatedButton
          onClick={() => goToStep(2)}
          disabled={uploadedPhotos.length === 0}
          className="w-full"
        >
          下一步：AI识别
        </AnimatedButton>
      </div>
    </motion.div>
  )

  const renderStep2 = () => (
    <motion.div 
      className="step-content px-6 py-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="bg-white p-6 rounded-xl shadow-sm text-center">
        <h3 className="text-lg font-bold text-primary-text mb-6">AI正在分析您的渔获...</h3>
        
        {/* 扫描动画区域 */}
        <div className="relative mb-6">
          <div className="w-48 h-48 mx-auto rounded-xl overflow-hidden border-2 border-aqua-teal bg-light-slate relative">
            {uploadedPhotos.length > 0 && (
              <Image
                src={uploadedPhotos[0].url}
                alt="扫描中的鱼"
                width={200}
                height={200}
                className="w-full h-full object-cover"
              />
            )}
            {/* 扫描线条动画 */}
            <motion.div
              className="scan-line"
              animate={{ y: [0, 192] }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>
          
          {/* 雷达扫描效果 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div
              className="radar-pulse"
              animate={{
                scale: [0.3, 1, 1.2],
                opacity: [1, 0.3, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-center space-x-2">
            <LoadingSpinner />
            <span className="text-secondary-text">正在识别鱼种特征...</span>
          </div>
          
          {/* 进度条 */}
          <div className="w-full bg-light-slate rounded-full h-2">
            <motion.div 
              className="bg-aqua-teal h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${scanProgress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>
          
          <div className="text-xs text-secondary-text space-y-1">
            <motion.div 
              className={scanProgress > 30 ? 'text-aqua-teal' : 'opacity-50'}
              animate={{ opacity: scanProgress > 30 ? 1 : 0.5 }}
            >
              🔍 分析图像质量... {scanProgress > 30 && <span className="text-aqua-teal">完成</span>}
            </motion.div>
            <motion.div 
              className={scanProgress > 60 ? 'text-aqua-teal' : 'opacity-50'}
              animate={{ opacity: scanProgress > 60 ? 1 : 0.5 }}
            >
              🧠 AI模型识别中... {scanProgress > 60 && <span className="text-aqua-teal">完成</span>}
            </motion.div>
            <motion.div 
              className={scanProgress > 90 ? 'text-aqua-teal' : 'opacity-50'}
              animate={{ opacity: scanProgress > 90 ? 1 : 0.5 }}
            >
              🎯 匹配数据库... {scanProgress > 90 && <span className="text-aqua-teal">完成</span>}
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  )

  const renderStep3 = () => (
    <motion.div 
      className="step-content px-6 py-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="bg-white p-4 rounded-xl shadow-sm">
        <h3 className="text-lg font-bold text-primary-text mb-4">识别完成！</h3>
        
        {/* 主要识别结果 */}
        <motion.div 
          className="mb-6"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 rounded-xl border-2 border-aqua-teal">
            <div className="relative">
              {uploadedPhotos.length > 0 && (
                <Image
                  src={uploadedPhotos[0].url}
                  alt="识别的鱼"
                  width={64}
                  height={64}
                  className="rounded-lg object-cover"
                />
              )}
              <motion.div 
                className="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.5, type: "spring", stiffness: 400 }}
              >
                <Star className="text-white" size={12} />
              </motion.div>
            </div>
            <div className="flex-1">
              <div className="text-lg font-bold text-primary-text">鲫鱼</div>
              <div className="text-sm text-secondary-text">Crucian Carp</div>
              <div className="text-xs text-aqua-teal font-medium">置信度: 96%</div>
            </div>
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring", stiffness: 400 }}
              >
                <Check className="text-aqua-teal" size={24} />
              </motion.div>
              <div className="text-xs text-secondary-text mt-1">已确认</div>
            </div>
          </div>
        </motion.div>

        {/* 其他可能选项 */}
        <motion.div 
          className="mb-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="text-sm text-secondary-text mb-3">其他可能选项:</div>
          <div className="space-y-2">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
              <Image
                src="https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=40&h=40&fit=crop&auto=format"
                alt="鲤鱼"
                width={40}
                height={40}
                className="rounded-lg object-cover"
              />
              <div className="flex-1">
                <div className="text-sm font-medium text-primary-text">鲤鱼</div>
                <div className="text-xs text-secondary-text">置信度: 78%</div>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
              <Image
                src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=40&h=40&fit=crop&auto=format"
                alt="草鱼"
                width={40}
                height={40}
                className="rounded-lg object-cover"
              />
              <div className="flex-1">
                <div className="text-sm font-medium text-primary-text">草鱼</div>
                <div className="text-xs text-secondary-text">置信度: 65%</div>
              </div>
            </div>
          </div>
        </motion.div>

        <AnimatedButton
          onClick={() => setCurrentStep(4)}
          className="w-full"
        >
          确认识别结果，继续填写信息
        </AnimatedButton>
      </div>
    </motion.div>
  )

  const renderStep4 = () => (
    <motion.div 
      className="step-content px-6 py-6 pb-24"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="bg-white p-4 rounded-xl shadow-sm">
        <h3 className="text-lg font-bold text-primary-text mb-4">补充详细信息</h3>
        
        {/* 已识别的鱼种信息 */}
        <motion.div 
          className="mb-6 p-3 bg-light-slate rounded-lg"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center space-x-3">
            {uploadedPhotos.length > 0 && (
              <Image
                src={uploadedPhotos[0].url}
                alt="鲫鱼"
                width={48}
                height={48}
                className="rounded-lg object-cover"
              />
            )}
            <div>
              <div className="font-medium text-primary-text">鲫鱼 (Crucian Carp)</div>
              <div className="text-sm text-secondary-text">AI识别 - 96% 置信度</div>
            </div>
          </div>
        </motion.div>

        <motion.div 
          className="space-y-4"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm text-secondary-text mb-1">重量 (kg) *</label>
              <input 
                type="number" 
                step="0.1" 
                placeholder="0.8" 
                className="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none transition-colors"
              />
            </div>
            <div>
              <label className="block text-sm text-secondary-text mb-1">长度 (cm)</label>
              <input 
                type="number" 
                placeholder="25" 
                className="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none transition-colors"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm text-secondary-text mb-1">钓组配置</label>
            <input 
              type="text" 
              placeholder="3号主线 + 2号子线 + 4号钩" 
              className="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none transition-colors"
            />
          </div>
          
          <div>
            <label className="block text-sm text-secondary-text mb-1">使用饵料</label>
            <input 
              type="text" 
              placeholder="玉米粒、面包虫" 
              className="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none transition-colors"
            />
          </div>
          
          <div>
            <label className="block text-sm text-secondary-text mb-1">钓鱼心得</label>
            <textarea 
              placeholder="今天天气不错，鱼情活跃，在水草边上钓获..." 
              rows={3}
              className="w-full p-3 border border-divider rounded-lg focus:border-aqua-teal focus:outline-none resize-none transition-colors"
            />
          </div>
          
          {/* 自动记录信息 */}
          <motion.div 
            className="p-4 bg-gradient-to-r from-light-slate to-white rounded-lg border border-divider"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="text-sm font-medium text-primary-text mb-3">📍 自动记录信息</div>
            <div className="space-y-2 text-sm text-secondary-text">
              <div className="flex items-center space-x-2">
                <span className="text-aqua-teal">📅</span>
                <span>2024年1月15日 14:30</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-aqua-teal">📍</span>
                <span>东湖公园钓点 (精确位置已加密)</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-aqua-teal">🌤️</span>
                <span>多云转晴 15°C，微风2级</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-aqua-teal">💧</span>
                <span>水温约12°C，适宜垂钓</span>
              </div>
            </div>
          </motion.div>
          
          <AnimatedButton
            onClick={handleSaveRecord}
            className="w-full py-4 text-lg"
            icon={<Check size={20} />}
          >
            保存到渔获日志
          </AnimatedButton>
        </motion.div>
      </div>
    </motion.div>
  )

  return (
    <div className="pb-24">
      <TopNavigation
        title="记录渔获"
        showBack
        onBack={() => navigateToPage('homepage')}
        stepInfo={`步骤 ${currentStep}/4`}
      />

      {/* 进度条 */}
      <motion.div 
        className="px-6 py-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <div className="w-full bg-light-slate rounded-full h-2">
          <motion.div 
            className="bg-aqua-teal h-2 rounded-full transition-all duration-500"
            initial={{ width: "0%" }}
            animate={{ width: `${currentStep * 25}%` }}
          />
        </div>
      </motion.div>

      {/* 解锁特效覆盖层 */}
      <AnimatePresence>
        {showUnlockEffect && (
          <motion.div 
            className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div className="text-center text-white">
              <motion.div 
                className="mb-4"
                initial={{ scale: 0, rotate: 0 }}
                animate={{
                  scale: [0, 1.2, 1],
                  rotate: [0, 180, 360]
                }}
                transition={{ duration: 1, ease: "easeInOut" }}
              >
                <div className="w-32 h-32 mx-auto bg-gradient-sunrise rounded-full flex items-center justify-center">
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 180, 360]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Star className="text-white" size={48} />
                  </motion.div>
                </div>
              </motion.div>
              <motion.h2 
                className="text-2xl font-bold mb-2"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                🎉 新鱼种解锁！
              </motion.h2>
              <motion.p 
                className="text-lg mb-2"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                恭喜您发现了
              </motion.p>
              <motion.p 
                className="text-xl font-bold text-sunrise-gold mb-4"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.7 }}
              >
                鲫鱼 (Crucian Carp)
              </motion.p>
              <motion.p 
                className="text-sm opacity-80 mb-6"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                图鉴完成度：18% → 19%
              </motion.p>
              <motion.div
                className="flex justify-center"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 }}
              >
                <AnimatedButton
                  onClick={hideUnlockEffect}
                  className="px-6 py-2"
                >
                  继续记录
                </AnimatedButton>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 步骤内容 */}
      <AnimatePresence mode="wait">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
        {currentStep === 4 && renderStep4()}
      </AnimatePresence>
    </div>
  )
}
