{"name": "expo-image-picker", "version": "16.1.4", "description": "Provides access to the system's UI for selecting images and videos from the phone's library or taking a photo with the camera.", "main": "build/ImagePicker.js", "types": "build/ImagePicker.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "image", "picker", "image-picker"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-image-picker"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/imagepicker/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"expo-image-loader": "~5.1.0"}, "devDependencies": {"expo-module-scripts": "^4.1.6", "@testing-library/user-event": "^14.5.2"}, "peerDependencies": {"expo": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}