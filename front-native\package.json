{"name": "front-native", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.0", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "expo": "~53.0.22", "expo-camera": "^16.1.11", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "^17.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.6", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.15.3", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}