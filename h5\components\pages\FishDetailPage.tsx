'use client'

import { motion } from 'framer-motion'
import { Share2, Star, TrendingUp, Weight, Ruler, Target, Calendar, MapPin, Cloud, ArrowRight, ChevronDown } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import type { PageType } from '../AppWrapper'

interface FishDetailPageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  fishId: string
  appState: any
}

export function FishDetailPage({ navigateToPage, fishId }: FishDetailPageProps) {
  const fishSpeciesData = {
    'crucian': {
      name: '鲫鱼',
      scientific: 'Crucian Carp',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format',
      totalCatches: 8,
      maxWeight: '1.2kg',
      successRate: '75%',
      unlockDate: '2024年1月10日',
      bestRig: '3号主线+2号子线',
      bestBait: '玉米粒'
    },
    'grass-carp': {
      name: '草鱼',
      scientific: 'Grass Carp',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=120&h=120&fit=crop&auto=format',
      totalCatches: 5,
      maxWeight: '3.2kg',
      successRate: '60%',
      unlockDate: '2024年1月8日',
      bestRig: '5号主线+4号子线',
      bestBait: '玉米粒'
    },
    'carp': {
      name: '鲤鱼',
      scientific: 'Common Carp',
      image: 'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=120&h=120&fit=crop&auto=format',
      totalCatches: 12,
      maxWeight: '2.8kg',
      successRate: '85%',
      unlockDate: '2024年1月5日',
      bestRig: '4号主线+3号子线',
      bestBait: '面包虫'
    }
  }

  const fishData = fishSpeciesData[fishId as keyof typeof fishSpeciesData] || fishSpeciesData.crucian

  const weightTrend = [
    { weight: 0.8, date: '1/9' },
    { weight: 0.5, date: '1/10' },
    { weight: 1.0, date: '1/11' },
    { weight: 1.2, date: '1/12' },
    { weight: 0.9, date: '1/13' },
    { weight: 0.6, date: '1/14' },
    { weight: 0.8, date: '1/15' }
  ]

  const catchHistory = [
    {
      id: 1,
      location: '东湖公园钓点',
      date: '今天 14:30',
      weight: '0.8kg',
      length: '25cm',
      weather: '多云 15°C',
      rig: '3号主线+2号子线',
      bait: '玉米粒',
      isRecord: false
    },
    {
      id: 2,
      location: '西湖钓点',
      date: '1月14日 09:15',
      weight: '1.2kg',
      length: '32cm',
      weather: '晴 18°C',
      rig: '4号主线+3号子线',
      bait: '面包虫',
      isRecord: true
    },
    {
      id: 3,
      location: '北河钓场',
      date: '1月12日 16:20',
      weight: '0.6kg',
      length: '22cm',
      weather: '小雨 12°C',
      rig: '2号主线+1号子线',
      bait: '蚯蚓',
      isRecord: false
    }
  ]

  const shareSpeciesData = () => {
    if (navigator.share) {
      navigator.share({
        title: `我的${fishData.name}钓获统计`,
        text: `我已经钓获了${fishData.totalCatches}条${fishData.name}，最大重量${fishData.maxWeight}！`,
        url: window.location.href
      })
    } else {
      alert('分享功能：已复制数据到剪贴板')
    }
  }

  const viewCatchDetail = (recordId: number) => {
    alert(`查看记录详情 #${recordId}`)
  }

  const maxTrendWeight = Math.max(...weightTrend.map(t => t.weight))

  return (
    <div className="pb-24">
      <TopNavigation
        title={`${fishData.name}详情`}
        showBack
        onBack={() => navigateToPage('fishdex')}
        rightIcon="share"
        onRightClick={shareSpeciesData}
      />

      {/* 鱼种信息卡片 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="bg-white p-6 rounded-xl shadow-sm text-center">
          <motion.div
            className="relative inline-block mb-4"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
          >
            <Image
              src={fishData.image}
              alt={fishData.name}
              width={96}
              height={96}
              className="rounded-xl object-cover border-2 border-aqua-teal mx-auto"
            />
            <motion.div 
              className="absolute -top-2 -right-2 w-8 h-8 bg-sunrise-gold rounded-full flex items-center justify-center"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.4, type: "spring", stiffness: 400 }}
            >
              <Star className="text-white" size={16} />
            </motion.div>
          </motion.div>
          
          <motion.h2 
            className="text-xl font-bold text-primary-text mb-1"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {fishData.name}
          </motion.h2>
          <motion.p 
            className="text-sm text-secondary-text mb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {fishData.scientific}
          </motion.p>
          
          {/* 统计数据 */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            {[
              { value: fishData.totalCatches, label: '总钓获', color: 'text-aqua-teal' },
              { value: fishData.maxWeight, label: '最大重量', color: 'text-sunrise-gold' },
              { value: fishData.successRate, label: '成功率', color: 'text-primary-text' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 + index * 0.1 }}
              >
                <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
                <div className="text-xs text-secondary-text">{stat.label}</div>
              </motion.div>
            ))}
          </div>

          {/* 解锁时间 */}
          <motion.div 
            className="text-xs text-secondary-text"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <Calendar className="inline mr-1" size={12} />
            首次解锁：{fishData.unlockDate}
          </motion.div>
        </div>
      </motion.div>

      {/* 数据分析 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <h3 className="text-lg font-bold text-primary-text mb-4">📊 数据分析</h3>
          
          {/* 重量趋势 */}
          <motion.div 
            className="mb-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-primary-text">重量趋势</span>
              <span className="text-xs text-secondary-text">近7次记录</span>
            </div>
            <div className="relative h-20 bg-light-slate rounded-lg p-3">
              <div className="flex items-end justify-between h-full">
                {weightTrend.map((data, index) => {
                  const height = (data.weight / maxTrendWeight) * 100
                  const isMax = data.weight === maxTrendWeight
                  return (
                    <motion.div
                      key={index}
                      className="flex flex-col items-center"
                      initial={{ height: 0 }}
                      animate={{ height: "auto" }}
                      transition={{ delay: 0.8 + index * 0.1 }}
                    >
                      <motion.div
                        className={`w-6 rounded-t ${isMax ? 'bg-sunrise-gold' : 'bg-aqua-teal'}`}
                        style={{ height: `${height}%` }}
                        initial={{ height: 0 }}
                        animate={{ height: `${height}%` }}
                        transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                      />
                      <span className="text-xs text-secondary-text mt-1">{data.weight}</span>
                    </motion.div>
                  )
                })}
              </div>
            </div>
          </motion.div>

          {/* 最佳配置 */}
          <motion.div 
            className="grid grid-cols-2 gap-3"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
          >
            <div className="bg-light-slate p-3 rounded-lg">
              <div className="text-xs text-secondary-text mb-1">最佳钓组</div>
              <div className="text-sm font-medium text-primary-text">{fishData.bestRig}</div>
            </div>
            <div className="bg-light-slate p-3 rounded-lg">
              <div className="text-xs text-secondary-text mb-1">最佳饵料</div>
              <div className="text-sm font-medium text-primary-text">{fishData.bestBait}</div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* 历史记录列表 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
      >
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-4 border-b border-divider">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-primary-text">🎣 钓获记录</h3>
              <div className="flex items-center space-x-2 text-sm text-secondary-text">
                <span>按时间排序</span>
                <TrendingUp size={14} />
              </div>
            </div>
          </div>

          {/* 记录列表 */}
          <div className="divide-y divide-divider">
            {catchHistory.map((record, index) => (
              <motion.div
                key={record.id}
                className="p-4 hover:bg-light-slate transition-colors cursor-pointer"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1.1 + index * 0.1 }}
                whileHover={{ x: 4 }}
                onClick={() => viewCatchDetail(record.id)}
              >
                <div className="flex items-start space-x-3">
                  <Image
                    src={fishData.image}
                    alt={fishData.name}
                    width={48}
                    height={48}
                    className="rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <div className="text-sm font-medium text-primary-text">{record.location}</div>
                      <div className="text-xs text-secondary-text">{record.date}</div>
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-secondary-text mb-2">
                      <span className="flex items-center space-x-1">
                        <Weight className="text-aqua-teal" size={12} />
                        <span>{record.weight}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Ruler className="text-aqua-teal" size={12} />
                        <span>{record.length}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Cloud className="text-aqua-teal" size={12} />
                        <span>{record.weather}</span>
                      </span>
                    </div>
                    <div className="text-xs text-secondary-text">
                      钓组: {record.rig} | 饵料: {record.bait}
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    {record.isRecord && (
                      <span className="text-xs bg-sunrise-gold text-white px-2 py-1 rounded mb-1">最大</span>
                    )}
                    <ArrowRight className="text-secondary-text" size={16} />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* 查看更多 */}
          <motion.div 
            className="p-4 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          >
            <motion.button 
              className="text-aqua-teal text-sm font-medium flex items-center justify-center space-x-1"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>查看全部 {fishData.totalCatches} 条记录</span>
              <ChevronDown size={14} />
            </motion.button>
          </motion.div>
        </div>
      </motion.div>

      {/* 钓鱼建议 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2 }}
      >
        <div className="bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 p-4 rounded-xl border border-aqua-teal/20">
          <div className="flex items-center space-x-2 mb-2">
            <Target className="text-aqua-teal" size={20} />
            <span className="text-sm font-medium text-primary-text">钓鱼建议</span>
          </div>
          <div className="text-xs text-secondary-text leading-relaxed">
            基于您的钓获数据分析，{fishData.name}在温度适中、天气稳定的条件下活跃度更高。
            建议使用{fishData.bestRig}的钓组配置，{fishData.bestBait}作为主要饵料，
            在水草边缘或结构物附近垂钓效果更佳。
          </div>
        </div>
      </motion.div>
    </div>
  )
}
