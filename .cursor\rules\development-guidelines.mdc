---
description: 开发指导原则和技术实现规范
---

# 开发指导原则

## MVP核心目标
基于 [开发需求文档.md](mdc:开发需求文档.md) 的核心价值主张：
验证 **"AI识别 -> 自动记录 -> 解锁图鉴"** 的核心功能循环

## 技术架构
**前端**: React Native 或 Flutter (跨平台开发)
**后端**: Go + Gin框架 + GORM + TDD测试  
**数据库**: PostgreSQL + PostGIS (地理位置数据)
**AI服务**: Fishial.ai鱼种识别 + Clipdrop背景移除

## 性能要求
- **AI处理时间**: 5-8秒内返回识别+抠图结果
- **记录流程**: 5步以内、60秒内完成
- **闪退率**: < 0.5% (主流机型)

## 数据隐私
- GPS位置数据**必须加密存储**
- 精确钓点**绝不公开展示**
- 排行榜等公共界面只显示大概区域

## 成功指标
**用户激活率**: 7天内完成首次AI记录 > 40%
**用户留存率**: 次日 > 30%，7日 > 15%  
**功能参与度**: 平均每周记录 > 1.5次/活跃用户
**AI准确率**: 用户直接确认比例 > 75%

## 代码规范
1. **优雅的项目结构** - 清晰的模块划分
2. **TDD测试驱动** - 核心功能必须有测试覆盖
3. **API设计** - RESTful规范，低延迟高并发
4. **错误处理** - 优雅的错误提示和降级策略

## UI/UX原则
1. **移动优先** - 针对手机屏幕优化
2. **直观易用** - 新用户无需学习即可上手  
3. **即时反馈** - 每个操作都有明确的视觉反馈
4. **游戏化** - 通过成就感驱动用户持续使用

## 开发优先级
1. **AI渔获日志** - 核心功能，优先开发
2. **鱼种图鉴** - 游戏化核心，紧随其后  
3. **用户系统** - 支撑功能，必要但非核心
4. **排行榜** - 社交功能，最后实现

## 第三方服务集成
- **地图服务**: 高德地图SDK/Mapbox (位置显示)
- **天气服务**: 和风天气/OpenWeatherMap (自动记录)
- **推送服务**: 用户留存和re-engagement
- **分析服务**: 用户行为分析和A/B测试