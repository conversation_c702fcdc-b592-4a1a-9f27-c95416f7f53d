import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { TopNavigation } from '../layout/TopNavigation'
import { globalStyles } from '../../styles'
import { Colors, Spacing, FontSizes, FontWeights } from '../../constants/colors'
import type { NavigationProps } from '../../types'

interface RecordPageProps extends NavigationProps {
  uploadedPhotos: Array<{url: string, type: string}>
  updateUploadedPhotos: (photos: Array<{url: string, type: string}>) => void
  saveRecord: (recordData: any) => void
}

export function RecordPage({ 
  navigateToPage, 
  uploadedPhotos, 
  updateUploadedPhotos, 
  saveRecord 
}: RecordPageProps) {
  const handleBack = () => {
    navigateToPage('homepage')
  }

  return (
    <View style={globalStyles.container}>
      <TopNavigation 
        title="记录渔获" 
        showBack 
        onBack={handleBack}
      />
      <View style={styles.content}>
        <Text style={styles.placeholder}>记录页面开发中...</Text>
        <Text style={styles.description}>
          这里将实现拍照、选择鱼类、输入重量长度等功能
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
  },
  
  placeholder: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.primaryText,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  
  description: {
    fontSize: FontSizes.md,
    color: Colors.secondaryText,
    textAlign: 'center',
    lineHeight: 24,
  },
})
