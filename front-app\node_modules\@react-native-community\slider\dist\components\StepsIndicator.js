Object.defineProperty(exports,"__esModule",{value:true});exports.StepsIndicator=void 0;var _react=_interopRequireWildcard(require("react"));var _reactNative=require("react-native");var _StepNumber=require("./StepNumber");var _TrackMark=require("./TrackMark");var _styles=require("../utils/styles");var _constants=require("../utils/constants");var _jsxRuntime=require("react/jsx-runtime");var _this=this,_jsxFileName="/Users/<USER>/Desktop/Projects/react-native-slider/package/src/components/StepsIndicator.tsx";function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap(),n=new WeakMap();return(_interopRequireWildcard=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var o,i,f={__proto__:null,default:e};if(null===e||"object"!=typeof e&&"function"!=typeof e)return f;if(o=t?n:r){if(o.has(e))return o.get(e);o.set(e,f);}for(var _t in e)"default"!==_t&&{}.hasOwnProperty.call(e,_t)&&((i=(o=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,_t))&&(i.get||i.set)?o(f,_t,i):f[_t]=e[_t]);return f;})(e,t);}var StepsIndicator=exports.StepsIndicator=function StepsIndicator(_ref){var options=_ref.options,sliderWidth=_ref.sliderWidth,currentValue=_ref.currentValue,StepMarker=_ref.StepMarker,renderStepNumber=_ref.renderStepNumber,thumbImage=_ref.thumbImage,isLTR=_ref.isLTR;var stepNumberFontStyle=(0,_react.useMemo)(function(){return{fontSize:options.length>9?_constants.constants.STEP_NUMBER_TEXT_FONT_SMALL:_constants.constants.STEP_NUMBER_TEXT_FONT_BIG};},[options.length]);var values=isLTR?options.reverse():options;var renderStepIndicator=(0,_react.useCallback)(function(i,index){return(0,_jsxRuntime.jsx)(_react.Fragment,{children:(0,_jsxRuntime.jsxs)(_reactNative.View,{style:_styles.styles.stepIndicatorElement,children:[(0,_jsxRuntime.jsx)(_TrackMark.SliderTrackMark,{isTrue:currentValue===i,index:i,thumbImage:thumbImage,StepMarker:StepMarker,currentValue:currentValue,min:options[0],max:options[options.length-1]},`${index}-SliderTrackMark`),renderStepNumber?(0,_jsxRuntime.jsx)(_StepNumber.StepNumber,{i:i,style:stepNumberFontStyle},`${index}th-step`):null]},`${index}-View`)},index);},[currentValue,StepMarker,options,thumbImage,renderStepNumber,stepNumberFontStyle]);return(0,_jsxRuntime.jsx)(_reactNative.View,{pointerEvents:"none",style:[_styles.styles.stepsIndicator,{marginHorizontal:sliderWidth*_constants.constants.MARGIN_HORIZONTAL_PADDING}],children:values.map(function(i,index){return renderStepIndicator(i,index);})});};