import React, { useState } from 'react';
import { View, StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { HomePage } from './pages/HomePage';
import { FishdexPage } from './pages/FishdexPage';
import { RecordPage } from './pages/RecordPage';
import { LeaderboardPage } from './pages/LeaderboardPage';
import { ProfilePage } from './pages/ProfilePage';
import { FishDetailPage } from './pages/FishDetailPage';
import { RecordSuccessPage } from './pages/RecordSuccessPage';
import { CareerPage } from './pages/CareerPage';
import { FishingSpotsPage } from './pages/FishingSpotsPage';
import { BottomNavigation } from './layout/BottomNavigation';
import { PhotoModal } from './common/PhotoModal';
import { globalStyles, colors } from '../constants/styles';
import type { PageType, AppState } from '../types';

export function AppWrapper() {
  const [appState, setAppState] = useState<AppState>({
    currentPage: 'home',
    selectedFishId: undefined,
    photoModalPhotos: [],
    photoModalIndex: 0,
    isPhotoModalVisible: false,
    recordData: null,
    uploadedPhotos: []
  });

  const navigateToPage = (page: PageType) => {
    setAppState(prev => ({ ...prev, currentPage: page }));
  };

  const showFishDetail = (fishId: string) => {
    setAppState(prev => ({ 
      ...prev, 
      selectedFishId: fishId,
      currentPage: 'fishdex' 
    }));
  };

  const openPhotoModal = (photos: string[], index: number = 0) => {
    setAppState(prev => ({ 
      ...prev, 
      photoModalPhotos: photos,
      photoModalIndex: index,
      isPhotoModalVisible: true 
    }));
  };

  const closePhotoModal = () => {
    setAppState(prev => ({ 
      ...prev, 
      isPhotoModalVisible: false,
      photoModalPhotos: [],
      photoModalIndex: 0 
    }));
  };

  const renderCurrentPage = () => {
    switch (appState.currentPage) {
      case 'home':
      case 'homepage':
        return (
          <HomePage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
      case 'fishdex':
        return (
          <FishdexPage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
      case 'record':
        return (
          <RecordPage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
      case 'leaderboard':
        return (
          <LeaderboardPage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
      case 'profile':
        return (
          <ProfilePage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
      case 'fish-detail':
        return (
          <FishDetailPage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            fishId={appState.selectedFishId || 'crucian'}
            appState={appState}
          />
        );
      case 'record-success':
        return (
          <RecordSuccessPage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            recordData={appState.recordData}
            uploadedPhotos={appState.uploadedPhotos}
            appState={appState}
          />
        );
      case 'career':
        return (
          <CareerPage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
      case 'fishing-spots':
        return (
          <FishingSpotsPage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
      default:
        return (
          <HomePage
            navigateToPage={navigateToPage}
            showFishDetail={showFishDetail}
            openPhotoModal={openPhotoModal}
            appState={appState}
          />
        );
    }
  };

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" backgroundColor={colors.lightSlate} />
      <View style={globalStyles.container}>
        <View style={globalStyles.containerMobile}>
          <View style={{ flex: 1 }}>
            {renderCurrentPage()}
          </View>
          
          <BottomNavigation
            currentPage={appState.currentPage}
            onNavigate={navigateToPage}
          />

          <PhotoModal
            isOpen={appState.isPhotoModalVisible}
            photos={appState.photoModalPhotos}
            currentIndex={appState.photoModalIndex}
            onClose={closePhotoModal}
          />
        </View>
      </View>
    </SafeAreaProvider>
  );
}