'use client'

import { motion } from 'framer-motion'
import { ArrowLeft, Bell, Share2, Settings, Download, Plus } from 'lucide-react'
import type { PageType } from '../AppWrapper'

interface TopNavigationProps {
  title: string
  showBack?: boolean
  onBack?: () => void
  rightIcon?: 'bell' | 'share' | 'settings' | 'download' | 'plus'
  onRightClick?: () => void
  stepInfo?: string
  className?: string
}

export function TopNavigation({ 
  title, 
  showBack, 
  onBack, 
  rightIcon, 
  onRightClick, 
  stepInfo,
  className = ''
}: TopNavigationProps) {
  const getRightIcon = () => {
    switch (rightIcon) {
      case 'bell': return <Bell size={20} />
      case 'share': return <Share2 size={20} />
      case 'settings': return <Settings size={20} />
      case 'download': return <Download size={20} />
      case 'plus': return <Plus size={20} />
      default: return null
    }
  }

  return (
    <motion.div 
      className={`gradient-bg px-6 py-4 text-white ${className}`}
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {showBack && (
            <motion.button
              onClick={onBack}
              className="text-xl"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ArrowLeft size={20} />
            </motion.button>
          )}
          <h1 className="text-lg font-bold">{title}</h1>
        </div>
        
        <div className="flex items-center space-x-3">
          {stepInfo && (
            <div className="text-sm">
              <span>{stepInfo}</span>
            </div>
          )}
          {rightIcon && (
            <motion.button
              onClick={onRightClick}
              className="text-xl"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {getRightIcon()}
            </motion.button>
          )}
        </div>
      </div>
    </motion.div>
  )
}
