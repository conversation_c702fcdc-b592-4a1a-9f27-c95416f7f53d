import React from 'react';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withTiming
} from 'react-native-reanimated';
import { Home, Book, Plus, Trophy, User } from 'lucide-react-native';
import { colors, gradients, shadows, spacing, borderRadius, fontSize } from '../../constants/styles';
import type { PageType } from '../../types';

interface BottomNavigationProps {
  currentPage: PageType;
  onNavigate: (page: PageType) => void;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const { width } = Dimensions.get('window');

export function BottomNavigation({ currentPage, onNavigate }: BottomNavigationProps) {
  const insets = useSafeAreaInsets();
  const translateY = useSharedValue(100);

  React.useEffect(() => {
    translateY.value = withSpring(0);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const getNavId = (page: PageType): string => {
    if (page === 'home') return 'home';
    if (page === 'fishdex') return 'fishdex';
    if (page === 'career' || page === 'fishingSpots') return 'profile';
    return page;
  };

  const activeNav = getNavId(currentPage);

  const navItems = [
    { id: 'home', icon: Home, label: '首页', page: 'home' as PageType },
    { id: 'fishdex', icon: Book, label: '图鉴', page: 'fishdex' as PageType },
    { id: 'record', icon: Plus, label: '记录', page: 'record' as PageType, isCenter: true },
    { id: 'leaderboard', icon: Trophy, label: '排行', page: 'leaderboard' as PageType },
    { id: 'profile', icon: User, label: '我的', page: 'profile' as PageType }
  ];

  return (
    <Animated.View
      style={[
        animatedStyle,
        {
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 50,
        }
      ]}
    >
      <LinearGradient
        colors={gradients.ocean}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ 
          paddingBottom: Math.max(insets.bottom, 20),
          borderTopWidth: 1,
          borderTopColor: 'rgba(255, 255, 255, 0.1)'
        }}
      >
        <View 
          style={{ 
            maxWidth: 400, 
            alignSelf: 'center',
            width: '100%'
          }}
        >
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-around',
            paddingVertical: spacing.md,
            paddingHorizontal: spacing.lg
          }}>
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeNav === item.id;
              
              if (item.isCenter) {
                return (
                  <AnimatedTouchableOpacity
                    key={item.id}
                    onPress={() => onNavigate(item.page)}
                    style={{ position: 'relative' }}
                  >
                    <LinearGradient
                      colors={gradients.sunrise}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                      style={[
                        {
                          width: 56,
                          height: 56,
                          borderRadius: borderRadius.full,
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginTop: -24
                        },
                        shadows.recordButton
                      ]}
                    >
                      <Icon size={24} color="white" />
                    </LinearGradient>
                  </AnimatedTouchableOpacity>
                );
              }

              return (
                <AnimatedTouchableOpacity
                  key={item.id}
                  onPress={() => onNavigate(item.page)}
                  style={{
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 4
                  }}
                >
                  <View>
                    <Icon 
                      size={20} 
                      color={isActive ? colors.sunriseGold : colors.secondaryText} 
                    />
                  </View>
                  <Text 
                    style={{ 
                      color: isActive ? colors.sunriseGold : colors.secondaryText,
                      fontSize: fontSize.xs
                    }}
                  >
                    {item.label}
                  </Text>
                </AnimatedTouchableOpacity>
              );
            })}
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );
}