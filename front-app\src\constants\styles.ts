import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const colors = {
  // 钓鱼APP配色方案
  oceanBlue: '#1A2E40',
  lightSlate: '#F0F2F5',
  sunriseGold: '#FFC759',
  sunriseGoldDark: '#FFB840',
  aquaTeal: '#00A79D',
  primaryText: '#121212',
  secondaryText: '#6B7280',
  divider: '#E5E7EB',
  white: '#FFFFFF',
  
  // 状态颜色
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  
  // 透明度变体
  oceanBlueLight: 'rgba(26, 46, 64, 0.8)',
  sunriseGoldLight: 'rgba(255, 199, 89, 0.3)',
  aquaTealLight: 'rgba(0, 167, 157, 0.3)',
};

export const gradients = {
  ocean: ['#1A2E40', '#203347'],
  sunrise: ['#FFC759', '#FFB840'],
  card: ['#ffffff', '#f8fafc'],
  locked: ['#e2e8f0', '#cbd5e1'],
  badge: ['#00A79D', '#FFC759'],
};

export const shadows = {
  recordButton: {
    shadowColor: colors.sunriseGold,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 24,
    elevation: 8,
  },
  fishHover: {
    shadowColor: colors.aquaTeal,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 8,
  },
  card: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  small: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 999,
};

export const fontSize = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

export const fontWeight = {
  normal: '400' as '400',
  medium: '500' as '500',
  semibold: '600' as '600',
  bold: '700' as '700',
};

export const layout = {
  screenWidth: width,
  screenHeight: height,
  containerPadding: spacing.md,
  headerHeight: 60,
  bottomTabHeight: 60,
};

export const globalStyles = StyleSheet.create({
  // 容器样式
  container: {
    flex: 1,
    backgroundColor: colors.lightSlate,
  },
  containerCentered: {
    flex: 1,
    backgroundColor: colors.lightSlate,
    alignItems: 'center',
    justifyContent: 'center',
  },
  containerMobile: {
    maxWidth: 428, // md max width
    width: '100%',
    backgroundColor: colors.white,
    minHeight: '100%',
    position: 'relative',
    alignSelf: 'center',
    ...shadows.card,
  },
  
  // 页面内容样式
  pageContent: {
    flex: 1,
    padding: spacing.md,
  },
  pageContentScrollable: {
    flexGrow: 1,
    padding: spacing.md,
  },
  
  // 卡片样式
  card: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    ...shadows.card,
  },
  cardLocked: {
    backgroundColor: colors.divider,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    borderWidth: 2,
    borderColor: colors.divider,
  },
  cardUnlocked: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    borderWidth: 2,
    borderColor: colors.aquaTeal,
    ...shadows.fishHover,
  },
  
  // 文本样式
  textPrimary: {
    color: colors.primaryText,
    fontSize: fontSize.base,
    fontWeight: fontWeight.normal,
  },
  textSecondary: {
    color: colors.secondaryText,
    fontSize: fontSize.sm,
    fontWeight: fontWeight.normal,
  },
  textTitle: {
    color: colors.primaryText,
    fontSize: fontSize.xl,
    fontWeight: fontWeight.bold,
  },
  textSubtitle: {
    color: colors.primaryText,
    fontSize: fontSize.lg,
    fontWeight: fontWeight.semibold,
  },
  textCaption: {
    color: colors.secondaryText,
    fontSize: fontSize.xs,
    fontWeight: fontWeight.normal,
  },
  
  // 按钮基础样式
  buttonBase: {
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonSmall: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    minHeight: 36,
  },
  buttonMedium: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    minHeight: 48,
  },
  buttonLarge: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    minHeight: 56,
  },
  
  // 输入框样式
  input: {
    borderWidth: 1,
    borderColor: colors.divider,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: fontSize.base,
    color: colors.primaryText,
    backgroundColor: colors.white,
  },
  inputFocused: {
    borderColor: colors.aquaTeal,
  },
  
  // 分隔线
  divider: {
    height: 1,
    backgroundColor: colors.divider,
  },
  dividerVertical: {
    width: 1,
    backgroundColor: colors.divider,
  },
  
  // Flex 布局辅助
  flexRow: {
    flexDirection: 'row',
  },
  flexColumn: {
    flexDirection: 'column',
  },
  flexCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  flexBetween: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  // 网格布局
  fishGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  fishCard: {
    width: (width - spacing.md * 3) / 2, // 2列布局，考虑容器padding和gap
    aspectRatio: 1,
    borderRadius: borderRadius.xl,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginBottom: spacing.md,
  },
  
  // 加载动画
  loadingSpinner: {
    width: 20,
    height: 20,
    borderRadius: borderRadius.full,
    borderWidth: 2,
    borderColor: colors.divider,
    borderTopColor: colors.aquaTeal,
  },
  
  // 导航相关
  navActive: {
    color: colors.sunriseGold,
  },
  navInactive: {
    color: colors.secondaryText,
  },
});

// 动画相关常量
export const animations = {
  spring: {
    damping: 15,
    stiffness: 150,
  },
  timing: {
    duration: 300,
  },
};