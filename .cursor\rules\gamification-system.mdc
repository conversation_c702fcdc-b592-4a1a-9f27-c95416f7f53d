---
description: 游戏化系统和鱼种图鉴的设计规范
---

# 游戏化系统设计规范

## 鱼种图鉴 (Fishdex)
参考原型实现：[fishing_app_prototype.html](mdc:fishing_app_prototype.html) 中的 `#fishdex` 部分

### 核心机制
- **收集进度**：显示已解锁数量 / 总数量
- **解锁状态**：已解锁(彩色) vs 未解锁(灰色剪影)
- **解锁触发**：首次记录新鱼种时触发特效

### 视觉设计
**已解锁鱼种**：
- 真实鱼类照片
- 水波青边框 (`border-aqua-teal`)
- 右上角金色星星徽章

**未解锁鱼种**：
- 灰色问号占位符
- 次要文本灰背景
- 激发收集欲望的神秘感

### 进度系统
```css
进度条：渐变色 from-aqua-teal to-sunrise-gold
完成度计算：(已解锁数 / 总数) * 100%
```

## 成就系统
**徽章类型**：
- 🏆 初次记录 (sunrise-gold渐变)
- 🐟 鱼种收集者 (aqua-teal渐变)  
- 📅 连续记录 (purple渐变)
- 👑 月度冠军 (灰色未解锁状态)

## 排行榜竞争
**排名机制**：
- 地区自动匹配
- 月度最重渔获排名
- 个人位置突出显示

**视觉层次**：
- 前三名特殊渐变色设计
- 个人排名金色高亮卡片
- 距离上一名的差距提示

## 动画特效库
```css
/* 解锁动画 */
@keyframes unlock-scale {
  0% { transform: scale(0) rotate(0deg); }
  50% { transform: scale(1.2) rotate(180deg); }
  100% { transform: scale(1) rotate(360deg); }
}

/* 脉冲效果 */
@keyframes radar-pulse {
  0% { transform: scale(0.3); opacity: 1; }
  70% { transform: scale(1); opacity: 0.3; }
  100% { transform: scale(1.2); opacity: 0; }
}
```