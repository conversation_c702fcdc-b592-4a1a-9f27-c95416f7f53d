import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { Trophy, MapPin, Medal, Crown } from 'lucide-react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  withSpring
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { TopNavigation } from '../layout/TopNavigation';
import { colors, spacing, borderRadius, fontSize, fontWeight, shadows, globalStyles } from '../../constants/styles';
import type { PageType } from '../AppWrapper';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface LeaderboardPageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  appState: any;
}

export function LeaderboardPage({ navigateToPage }: LeaderboardPageProps) {
  const headerOpacity = useSharedValue(0);
  const typesOpacity = useSharedValue(0);
  const myRankOpacity = useSharedValue(0);
  const listOpacity = useSharedValue(0);
  const rulesOpacity = useSharedValue(0);

  React.useEffect(() => {
    headerOpacity.value = withDelay(100, withTiming(1, { duration: 600 }));
    typesOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
    myRankOpacity.value = withDelay(400, withTiming(1, { duration: 600 }));
    listOpacity.value = withDelay(500, withTiming(1, { duration: 600 }));
    rulesOpacity.value = withDelay(1000, withTiming(1, { duration: 600 }));
  }, []);

  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: withTiming(headerOpacity.value === 1 ? 0 : 20) }],
  }));

  const typesStyle = useAnimatedStyle(() => ({
    opacity: typesOpacity.value,
    transform: [{ translateY: withTiming(typesOpacity.value === 1 ? 0 : 10) }],
  }));

  const myRankStyle = useAnimatedStyle(() => ({
    opacity: myRankOpacity.value,
    transform: [{ translateY: withTiming(myRankOpacity.value === 1 ? 0 : 20) }],
  }));

  const listStyle = useAnimatedStyle(() => ({
    opacity: listOpacity.value,
    transform: [{ translateY: withTiming(listOpacity.value === 1 ? 0 : 20) }],
  }));

  const rulesStyle = useAnimatedStyle(() => ({
    opacity: rulesOpacity.value,
    transform: [{ translateY: withTiming(rulesOpacity.value === 1 ? 0 : 20) }],
  }));

  const leaderboardTypes = [
    { id: 'monthly-weight', name: '月度最重', active: true },
    { id: 'total-catch', name: '总渔获数', active: false },
    { id: 'species-collection', name: '鱼种收集', active: false }
  ];

  const topRankers = [
    {
      rank: 1,
      name: '钓鱼大师',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format',
      fish: '草鱼',
      weight: '2.8kg',
      date: '1月12日',
      score: 2800
    },
    {
      rank: 2,
      name: '江边老钓',
      avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=40&h=40&fit=crop&crop=face&auto=format',
      fish: '鲤鱼',
      weight: '2.1kg',
      date: '1月10日',
      score: 2100
    },
    {
      rank: 3,
      name: '钓鱼新手',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format',
      fish: '鲫鱼',
      weight: '1.8kg',
      date: '1月8日',
      score: 1800
    }
  ];

  const otherRankers = [
    {
      rank: 4,
      name: '湖边渔者',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '鲤鱼',
      weight: '1.5kg',
      score: 1500
    },
    {
      rank: 5,
      name: '钓鱼小白',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '草鱼',
      weight: '1.4kg',
      score: 1400
    }
  ];

  const userRank = {
    rank: 8,
    fish: '草鱼',
    weight: '1.2kg',
    score: 1200,
    progress: '距离第7名还差0.1kg'
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown color="#FFC759" size={20} />;
      case 2:
        return <Medal color="#9CA3AF" size={18} />;
      case 3:
        return <Medal color="#FB923C" size={18} />;
      default:
        return <Text style={styles.defaultRankText}>{rank}</Text>;
    }
  };

  const getRankBgColors = (rank: number) => {
    switch (rank) {
      case 1:
        return ['#FFC759', '#F59E0B'];
      case 2:
        return ['#D1D5DB', '#6B7280'];
      case 3:
        return ['#FB923C', '#EA580C'];
      default:
        return ['#F0F2F5', '#F0F2F5'];
    }
  };

  return (
    <ScrollView 
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <View>
        <TopNavigation
          title="排行榜"
          showBack
          onBack={() => navigateToPage('homepage')}
        />

        {/* 地区选择 */}
        <AnimatedView style={[headerStyle, styles.sectionContainer]}>
          <View style={[globalStyles.card, styles.locationCard]}>
            <View style={styles.locationContent}>
              <View>
                <Text style={styles.locationLabel}>当前地区</Text>
                <Text style={styles.locationValue}>广东省 深圳市</Text>
              </View>
              <TouchableOpacity>
                <MapPin color={colors.aquaTeal} size={20} />
              </TouchableOpacity>
            </View>
          </View>
        </AnimatedView>

        {/* 排行榜类型 */}
        <AnimatedView style={[typesStyle, styles.typesContainer]}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.typesRow}>
              {leaderboardTypes.map((type) => (
                <TouchableOpacity
                  key={type.id}
                  style={[
                    styles.typeButton,
                    type.active ? styles.typeButtonActive : styles.typeButtonInactive
                  ]}
                >
                  <Text style={[
                    styles.typeButtonText,
                    { color: type.active ? colors.white : colors.secondaryText }
                  ]}>
                    {type.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </AnimatedView>

        {/* 我的排名 */}
        <AnimatedView style={[myRankStyle, styles.sectionContainer]}>
          <LinearGradient
            colors={[colors.sunriseGold, colors.sunriseGoldDark]}
            style={styles.myRankCard}
          >
            <View style={styles.myRankContent}>
              <View style={styles.myRankBadge}>
                <Text style={styles.myRankNumber}>{userRank.rank}</Text>
              </View>
              <View style={styles.myRankInfo}>
                <Text style={styles.myRankTitle}>你的排名</Text>
                <Text style={styles.myRankSubtitle}>{userRank.fish} {userRank.weight} | 本月最佳</Text>
              </View>
              <View style={styles.myRankStats}>
                <Text style={styles.myRankWeight}>{userRank.weight}</Text>
                <Text style={styles.myRankProgress}>{userRank.progress}</Text>
              </View>
            </View>
          </LinearGradient>
        </AnimatedView>

        {/* 排行榜列表 */}
        <AnimatedView style={[listStyle, styles.sectionContainer]}>
          <View style={styles.leaderboardList}>
            {/* 前三名 */}
            {topRankers.map((ranker, index) => (
              <TouchableOpacity
                key={ranker.rank}
                style={styles.rankerItem}
              >
                <View style={styles.rankerContent}>
                  <LinearGradient
                    colors={getRankBgColors(ranker.rank)}
                    style={styles.rankBadgeLarge}
                  >
                    {getRankIcon(ranker.rank)}
                  </LinearGradient>
                  <Image
                    source={{ uri: ranker.avatar }}
                    style={styles.rankerAvatar}
                  />
                  <View style={styles.rankerInfo}>
                    <Text style={styles.rankerName}>{ranker.name}</Text>
                    <Text style={styles.rankerFish}>{ranker.fish} {ranker.weight}</Text>
                  </View>
                  <View style={styles.rankerStats}>
                    <Text style={styles.rankerWeight}>{ranker.weight}</Text>
                    <Text style={styles.rankerDate}>{ranker.date}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}

            {/* 其他排名 */}
            {otherRankers.map((ranker) => (
              <TouchableOpacity
                key={ranker.rank}
                style={styles.rankerItemSmall}
              >
                <View style={styles.rankerContentSmall}>
                  <View style={styles.rankBadgeSmall}>
                    <Text style={styles.rankTextSmall}>{ranker.rank}</Text>
                  </View>
                  <Image
                    source={{ uri: ranker.avatar }}
                    style={styles.rankerAvatarSmall}
                  />
                  <View style={styles.rankerInfoSmall}>
                    <Text style={styles.rankerNameSmall}>{ranker.name}</Text>
                    <Text style={styles.rankerFishSmall}>{ranker.fish} {ranker.weight}</Text>
                  </View>
                  <Text style={styles.rankerWeightSmall}>{ranker.weight}</Text>
                </View>
              </TouchableOpacity>
            ))}

            {/* 查看更多 */}
            <TouchableOpacity style={styles.viewMoreButton}>
              <Text style={styles.viewMoreText}>查看完整排行榜 →</Text>
            </TouchableOpacity>
          </View>
        </AnimatedView>

        {/* 排行榜说明 */}
        <AnimatedView style={[rulesStyle, styles.sectionContainer]}>
          <View style={styles.rulesCard}>
            <View style={styles.rulesHeader}>
              <Trophy color={colors.aquaTeal} size={20} />
              <Text style={styles.rulesTitle}>排行规则</Text>
            </View>
            <Text style={styles.rulesText}>
              • 月度排行榜每月1号重置{'\n'}
              • 排名基于当月最大单鱼重量{'\n'}
              • 地区排名基于GPS定位自动匹配{'\n'}
              • 数据每小时更新一次
            </Text>
          </View>
        </AnimatedView>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: colors.lightSlate,
  },
  sectionContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  defaultRankText: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.bold,
    color: colors.primaryText,
  },
  locationCard: {
    // Uses globalStyles.card
  },
  locationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  locationLabel: {
    fontSize: fontSize.sm,
    color: colors.secondaryText,
  },
  locationValue: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.bold,
    color: colors.primaryText,
  },
  typesContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  typesRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  typeButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.full,
  },
  typeButtonActive: {
    backgroundColor: colors.aquaTeal,
  },
  typeButtonInactive: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.divider,
  },
  typeButtonText: {
    fontSize: fontSize.sm,
  },
  myRankCard: {
    padding: spacing.md,
    borderRadius: borderRadius.xl,
  },
  myRankContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  myRankBadge: {
    width: 48,
    height: 48,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  myRankNumber: {
    fontSize: fontSize.xl,
    fontWeight: fontWeight.bold,
    color: colors.white,
  },
  myRankInfo: {
    flex: 1,
  },
  myRankTitle: {
    fontWeight: fontWeight.bold,
    color: colors.white,
  },
  myRankSubtitle: {
    fontSize: fontSize.sm,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  myRankStats: {
    alignItems: 'flex-end',
  },
  myRankWeight: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.bold,
    color: colors.white,
  },
  myRankProgress: {
    fontSize: fontSize.xs,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  leaderboardList: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.xl,
    ...shadows.small,
    overflow: 'hidden',
  },
  rankerItem: {
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  rankerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  rankBadgeLarge: {
    width: 48,
    height: 48,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rankerAvatar: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.full,
  },
  rankerInfo: {
    flex: 1,
  },
  rankerName: {
    fontWeight: fontWeight.medium,
    color: colors.primaryText,
  },
  rankerFish: {
    fontSize: fontSize.sm,
    color: colors.secondaryText,
  },
  rankerStats: {
    alignItems: 'flex-end',
  },
  rankerWeight: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.bold,
    color: colors.primaryText,
  },
  rankerDate: {
    fontSize: fontSize.xs,
    color: colors.secondaryText,
  },
  rankerItemSmall: {
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.divider,
  },
  rankerContentSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  rankBadgeSmall: {
    width: 32,
    height: 32,
    backgroundColor: colors.lightSlate,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rankTextSmall: {
    color: colors.secondaryText,
    fontWeight: fontWeight.medium,
  },
  rankerAvatarSmall: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.full,
  },
  rankerInfoSmall: {
    flex: 1,
  },
  rankerNameSmall: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    color: colors.primaryText,
  },
  rankerFishSmall: {
    fontSize: fontSize.xs,
    color: colors.secondaryText,
  },
  rankerWeightSmall: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    color: colors.primaryText,
  },
  viewMoreButton: {
    padding: spacing.md,
    alignItems: 'center',
  },
  viewMoreText: {
    color: colors.aquaTeal,
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
  },
  rulesCard: {
    backgroundColor: `${colors.aquaTeal}20`,
    padding: spacing.md,
    borderRadius: borderRadius.xl,
    borderWidth: 1,
    borderColor: `${colors.aquaTeal}40`,
  },
  rulesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  rulesTitle: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    color: colors.primaryText,
  },
  rulesText: {
    fontSize: fontSize.xs,
    color: colors.secondaryText,
    lineHeight: 20,
  },
});