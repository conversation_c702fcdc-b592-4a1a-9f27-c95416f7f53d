'use client'

import { motion } from 'framer-motion'
import { Home, Book, Plus, Trophy, User } from 'lucide-react'
import type { PageType } from '../AppWrapper'

interface BottomNavigationProps {
  currentPage: PageType
  onNavigate: (page: PageType) => void
}

export function BottomNavigation({ currentPage, onNavigate }: BottomNavigationProps) {
  const getNavId = (page: PageType): string => {
    if (page === 'homepage') return 'home'
    if (page === 'fish-detail') return 'fishdex'
    if (page === 'career' || page === 'fishing-spots') return 'profile'
    if (page === 'success') return currentPage
    return page
  }

  const activeNav = getNavId(currentPage)

  const navItems = [
    { id: 'home', icon: Home, label: '首页', page: 'homepage' as PageType },
    { id: 'fishdex', icon: Book, label: '图鉴', page: 'fishdex' as PageType },
    { id: 'record', icon: Plus, label: '记录', page: 'record' as PageType, isCenter: true },
    { id: 'leaderboard', icon: Trophy, label: '排行', page: 'leaderboard' as PageType },
    { id: 'profile', icon: User, label: '我的', page: 'profile' as PageType }
  ]

  return (
    <motion.div 
      className="fixed bottom-0 left-0 right-0 gradient-bg z-50"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="max-w-md mx-auto">
        <div className="flex items-center justify-around py-3 px-6">
        {navItems.map((item) => {
          const Icon = item.icon
          const isActive = activeNav === item.id
          
          if (item.isCenter) {
            return (
              <motion.button
                key={item.id}
                onClick={() => onNavigate(item.page)}
                className="nav-tab relative"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div 
                  className="w-14 h-14 bg-gradient-sunrise rounded-full flex items-center justify-center -mt-6 shadow-record-button"
                  whileHover={{ boxShadow: "0 12px 32px rgba(255, 199, 89, 0.6)" }}
                  transition={{ duration: 0.2 }}
                >
                  <Icon className="text-white text-xl" size={24} />
                </motion.div>
              </motion.button>
            )
          }

          return (
            <motion.button
              key={item.id}
              onClick={() => onNavigate(item.page)}
              className="nav-tab flex flex-col items-center space-y-1"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={{
                  color: isActive ? '#FFC759' : '#6B7280',
                  scale: isActive ? 1.1 : 1
                }}
                transition={{ duration: 0.2 }}
              >
                <Icon size={20} />
              </motion.div>
              <motion.span 
                className="text-xs"
                animate={{
                  color: isActive ? '#FFC759' : '#6B7280'
                }}
                transition={{ duration: 0.2 }}
              >
                {item.label}
              </motion.span>
            </motion.button>
          )
        })}
        </div>
      </div>
    </motion.div>
  )
}
