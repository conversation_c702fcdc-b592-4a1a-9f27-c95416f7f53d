import React, { useEffect } from 'react'
import { View, StyleSheet, ViewStyle } from 'react-native'
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat,
  withTiming,
  Easing
} from 'react-native-reanimated'
import { Colors, Spacing } from '../../constants/colors'

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  color?: string
  style?: ViewStyle
}

export function LoadingSpinner({ 
  size = 'medium', 
  color = Colors.aquaTeal,
  style 
}: LoadingSpinnerProps) {
  const rotation = useSharedValue(0)

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }]
  }))

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 1000,
        easing: Easing.linear
      }),
      -1,
      false
    )
  }, [])

  const getSize = () => {
    switch (size) {
      case 'small':
        return 16
      case 'medium':
        return 24
      case 'large':
        return 32
      default:
        return 24
    }
  }

  const spinnerSize = getSize()

  return (
    <View style={[styles.container, style]}>
      <Animated.View 
        style={[
          animatedStyle,
          {
            width: spinnerSize,
            height: spinnerSize,
            borderRadius: spinnerSize / 2,
            borderWidth: 2,
            borderColor: Colors.divider,
            borderLeftColor: color,
          }
        ]} 
      />
    </View>
  )
}

// 脉冲加载动画
export function PulseLoader({ 
  size = 'medium', 
  color = Colors.aquaTeal,
  style 
}: LoadingSpinnerProps) {
  const scale = useSharedValue(1)
  const opacity = useSharedValue(1)

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value
  }))

  useEffect(() => {
    scale.value = withRepeat(
      withTiming(1.2, {
        duration: 800,
        easing: Easing.inOut(Easing.ease)
      }),
      -1,
      true
    )
    
    opacity.value = withRepeat(
      withTiming(0.3, {
        duration: 800,
        easing: Easing.inOut(Easing.ease)
      }),
      -1,
      true
    )
  }, [])

  const getSize = () => {
    switch (size) {
      case 'small':
        return 16
      case 'medium':
        return 24
      case 'large':
        return 32
      default:
        return 24
    }
  }

  const pulseSize = getSize()

  return (
    <View style={[styles.container, style]}>
      <Animated.View 
        style={[
          animatedStyle,
          {
            width: pulseSize,
            height: pulseSize,
            borderRadius: pulseSize / 2,
            backgroundColor: color,
          }
        ]} 
      />
    </View>
  )
}

// 雷达扫描动画（用于钓鱼记录页面）
export function RadarLoader({ 
  size = 'large',
  style 
}: { size?: 'medium' | 'large'; style?: ViewStyle }) {
  const scale = useSharedValue(0.3)
  const opacity = useSharedValue(1)

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value
  }))

  useEffect(() => {
    scale.value = withRepeat(
      withTiming(1.2, {
        duration: 2000,
        easing: Easing.out(Easing.ease)
      }),
      -1,
      false
    )
    
    opacity.value = withRepeat(
      withTiming(0, {
        duration: 2000,
        easing: Easing.out(Easing.ease)
      }),
      -1,
      false
    )
  }, [])

  const radarSize = size === 'large' ? 96 : 64

  return (
    <View style={[styles.container, style]}>
      <Animated.View 
        style={[
          animatedStyle,
          {
            width: radarSize,
            height: radarSize,
            borderRadius: radarSize / 2,
            borderWidth: 2,
            borderColor: Colors.sunriseGold,
          }
        ]} 
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  }
})
