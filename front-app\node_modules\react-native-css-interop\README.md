# react-native-css-interop

Provides a layer of interoperability between React Native and CSS stylesheets allowing to use CSS as a styling language for both React Native and React Native Web. The interoperability goes beyond basic styles allows the use of modern features such as CSS Variables, Viewport Units and, Media and Container queries.

This is not a full implementation of the CSS spec. It is a highly **opinionated** view on how React Native projects can work with CSS.
