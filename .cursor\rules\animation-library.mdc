---
globs: *.css,*.html,*.tsx,*.jsx
description: 动画效果库和CSS规范
---

# 动画效果库

参考完整实现：[fishing_app_prototype.html](mdc:fishing_app_prototype.html)

## 核心动画效果

### AI扫描动画
```css
/* 扫描线条 */
.scan-line {
  position: absolute;
  top: 0; left: 0; right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #00A79D, transparent);
  animation: scan-move 2s linear infinite;
}

@keyframes scan-move {
  0% { transform: translateY(0); opacity: 1; }
  100% { transform: translateY(192px); opacity: 0.3; }
}

/* 雷达脉冲 */
.radar-pulse {
  width: 100px; height: 100px;
  border: 2px solid #FFC759;
  border-radius: 50%;
  opacity: 0.7;
  animation: radar-pulse 2s ease-in-out infinite;
}

@keyframes radar-pulse {
  0% { transform: scale(0.3); opacity: 1; }
  70% { transform: scale(1); opacity: 0.3; }
  100% { transform: scale(1.2); opacity: 0; }
}
```

### 解锁特效动画
```css
.unlock-animation {
  animation: unlock-scale 1s ease-in-out;
}

@keyframes unlock-scale {
  0% { transform: scale(0) rotate(0deg); }
  50% { transform: scale(1.2) rotate(180deg); }
  100% { transform: scale(1) rotate(360deg); }
}
```

### 加载动画
```css
.loading-spinner {
  width: 20px; height: 20px;
  border: 2px solid #E5E7EB;
  border-left: 2px solid #00A79D;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

### 页面过渡动画
```css
.step-content {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 交互动画

### 按钮悬停效果
```css
.hover\:scale-105:hover {
  transform: scale(1.05);
}

.transition-all {
  transition: all 0.3s ease;
}
```

### 进度条动画
```css
.transition-all.duration-500 {
  transition: all 0.5s ease;
}
```

### Tailwind动画类
- `animate-pulse` - 脉冲效果
- `animate-bounce` - 弹跳效果  
- `animate-spin` - 旋转效果

## 动画时长规范
- **微交互**: 0.15s-0.3s (按钮悬停、状态切换)
- **页面切换**: 0.3s-0.5s (步骤切换、页面跳转)  
- **特效动画**: 1s-2s (解锁特效、扫描动画)
- **加载动画**: 无限循环直到完成

## 缓动函数选择
- `ease-out` - 页面进入动画
- `ease-in-out` - 对称动画效果
- `linear` - 匀速动画(扫描线条)
- `ease` - 默认自然缓动

## 性能优化
1. 使用 `transform` 而非改变位置属性
2. 避免动画期间重排重绘
3. 合理使用 `will-change` 属性
4. 复杂动画使用 `transform3d` 启用硬件加速