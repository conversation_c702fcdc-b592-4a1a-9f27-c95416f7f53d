/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./App.{js,jsx,ts,tsx}",
    "./src/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
    "./app/**/*.{js,jsx,ts,tsx}"
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        // 钓鱼APP配色方案
        'ocean-blue': '#1A2E40',
        'light-slate': '#F0F2F5',
        'sunrise-gold': '#FFC759',
        'aqua-teal': '#00A79D',
        'primary-text': '#121212',
        'secondary-text': '#6B7280',
        'divider': '#E5E7EB',
      },
      backgroundImage: {
        'gradient-ocean': 'linear-gradient(135deg, #1A2E40 0%, #203347 100%)',
        'gradient-sunrise': 'linear-gradient(135deg, #FFC759 0%, #FFB840 100%)',
        'gradient-card': 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        'gradient-locked': 'linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%)',
        'gradient-badge': 'linear-gradient(135deg, #00A79D 0%, #FFC759 100%)'
      },
      keyframes: {
        "scan-move": {
          "0%": { transform: "translateY(0)", opacity: "1" },
          "100%": { transform: "translateY(192px)", opacity: "0.3" }
        },
        "radar-pulse": {
          "0%": { transform: "scale(0.3)", opacity: "1" },
          "70%": { transform: "scale(1)", opacity: "0.3" },
          "100%": { transform: "scale(1.2)", opacity: "0" }
        },
        "unlock-scale": {
          "0%": { transform: "scale(0) rotate(0deg)" },
          "50%": { transform: "scale(1.2) rotate(180deg)" },
          "100%": { transform: "scale(1) rotate(360deg)" }
        },
        "confetti-fall": {
          "0%": { transform: "translateY(-50px) rotate(0deg)", opacity: "1" },
          "100%": { transform: "translateY(150px) rotate(360deg)", opacity: "0" }
        },
        "fade-in-up": {
          "0%": { opacity: "0", transform: "translateY(20px)" },
          "100%": { opacity: "1", transform: "translateY(0)" }
        },
        "pulse-ring": {
          "0%": { transform: "scale(0.5)", opacity: "1" },
          "100%": { transform: "scale(2.5)", opacity: "0" }
        },
        "pulse-glow": {
          "0%": { shadowOpacity: 0.3 },
          "100%": { shadowOpacity: 0.4 }
        }
      },
      animation: {
        "scan-move": "scan-move 2s linear infinite",
        "radar-pulse": "radar-pulse 2s ease-in-out infinite",
        "unlock-scale": "unlock-scale 1s ease-in-out",
        "confetti-fall": "confetti-fall 2s ease-out infinite",
        "fade-in-up": "fade-in-up 0.5s ease-out",
        "pulse-ring": "pulse-ring 2s ease-out infinite",
        "pulse-glow": "pulse-glow 2s ease-in-out infinite alternate"
      }
    },
  },
  plugins: [],
}

