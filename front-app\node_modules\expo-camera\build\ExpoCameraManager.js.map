{"version": 3, "file": "ExpoCameraManager.js", "sourceRoot": "", "sources": ["../src/ExpoCameraManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAAqB,YAAY,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { CameraNativeModule } from './Camera.types';\n\nexport default requireNativeModule<CameraNativeModule>('ExpoCamera');\n"]}