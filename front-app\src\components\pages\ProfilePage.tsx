import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert, StyleSheet } from 'react-native';
import { Settings, Clock, MapPin, Share2, Crown, ChevronRight, Trophy, Fish, Calendar, Target } from 'lucide-react-native';
import { colors, spacing, borderRadius, fontSize, fontWeight, shadows, globalStyles } from '../../constants/styles';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  withSpring
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { TopNavigation } from '../layout/TopNavigation';
import type { PageType } from '../AppWrapper';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface ProfilePageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  appState: any;
}

export function ProfilePage({ navigateToPage }: ProfilePageProps) {
  // 由于ProfilePage内容较长，这里仅添加基本样式重构
  // 实际使用时需要将所有className替换为对应的style属性
  const profileOpacity = useSharedValue(0);
  const achievementsOpacity = useSharedValue(0);
  const menuOpacity = useSharedValue(0);
  const levelOpacity = useSharedValue(0);
  const avatarScale = useSharedValue(0);
  const progressWidth = useSharedValue(0);

  React.useEffect(() => {
    profileOpacity.value = withDelay(100, withTiming(1, { duration: 600 }));
    avatarScale.value = withDelay(200, withSpring(1));
    achievementsOpacity.value = withDelay(600, withTiming(1, { duration: 600 }));
    menuOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
    levelOpacity.value = withDelay(1000, withTiming(1, { duration: 600 }));
    progressWidth.value = withDelay(1200, withTiming(0.75, { duration: 1000 }));
  }, []);

  const profileStyle = useAnimatedStyle(() => ({
    opacity: profileOpacity.value,
    transform: [{ translateY: withTiming(profileOpacity.value === 1 ? 0 : 20) }],
  }));

  const avatarStyle = useAnimatedStyle(() => ({
    transform: [{ scale: avatarScale.value }],
  }));

  const achievementsStyle = useAnimatedStyle(() => ({
    opacity: achievementsOpacity.value,
    transform: [{ translateY: withTiming(achievementsOpacity.value === 1 ? 0 : 20) }],
  }));

  const menuStyle = useAnimatedStyle(() => ({
    opacity: menuOpacity.value,
    transform: [{ translateY: withTiming(menuOpacity.value === 1 ? 0 : 20) }],
  }));

  const levelStyle = useAnimatedStyle(() => ({
    opacity: levelOpacity.value,
    transform: [{ translateY: withTiming(levelOpacity.value === 1 ? 0 : 20) }],
  }));

  const progressStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value * 100}%`,
  }));

  const userInfo = {
    name: '渔友小明',
    id: '1001',
    fishingAge: '2年',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face&auto=format',
    stats: {
      totalCatch: 128,
      speciesCount: 32,
      monthlyRank: 8
    }
  };

  const achievements = [
    {
      id: 'first-record',
      name: '初次记录',
      icon: Trophy,
      colors: ['#FFC759', '#F59E0B'],
      unlocked: true
    },
    {
      id: 'species-collector',
      name: '鱼种收集者',
      icon: Fish,
      colors: ['#00A79D', '#0EA5E9'],
      unlocked: true
    },
    {
      id: 'daily-streak',
      name: '连续记录',
      icon: Calendar,
      colors: ['#A855F7', '#9333EA'],
      unlocked: true
    },
    {
      id: 'monthly-champion',
      name: '月度冠军',
      icon: Crown,
      colors: ['#E5E7EB', '#9CA3AF'],
      unlocked: false
    }
  ];

  const menuItems = [
    {
      id: 'career',
      icon: Clock,
      label: '我的生涯',
      description: '查看钓鱼生涯数据',
      page: 'career' as PageType
    },
    {
      id: 'fishing-spots',
      icon: MapPin,
      label: '钓点管理',
      description: '管理常用钓点',
      page: 'fishing-spots' as PageType
    },
    {
      id: 'share',
      icon: Share2,
      label: '分享记录',
      description: '分享到社交平台',
      action: () => Alert.alert('分享功能', '即将开放')
    },
    {
      id: 'premium',
      icon: Crown,
      label: '升级专业版',
      description: '解锁更多功能',
      badge: 'Pro',
      action: () => Alert.alert('升级专业版', '即将开放')
    },
    {
      id: 'settings',
      icon: Settings,
      label: '设置',
      description: '应用设置和偏好',
      action: () => Alert.alert('设置', '即将开放')
    }
  ];

  const handleRightClick = () => {
    Alert.alert('设置', '即将开放');
  };

  return (
    <ScrollView 
      className="flex-1 bg-page-bg" 
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <View>
        <TopNavigation
          title="个人档案"
          rightIcon="settings"
          onRightClick={handleRightClick}
        />

        {/* 用户信息 */}
        <AnimatedView style={profileStyle} className="px-6 py-6">
          <View className="bg-white p-6 rounded-xl shadow-sm items-center">
            <AnimatedView style={avatarStyle} className="mb-4">
              <View className="w-20 h-20 rounded-full border-4 border-sunrise-gold overflow-hidden">
                <Image
                  source={{ uri: userInfo.avatar }}
                  className="w-full h-full"
                  resizeMode="cover"
                />
              </View>
            </AnimatedView>
            
            <Text className="text-xl font-bold text-primary-text mb-1">
              {userInfo.name}
            </Text>
            <Text className="text-sm text-secondary-text mb-4">
              钓龄 {userInfo.fishingAge} | ID: {userInfo.id}
            </Text>
            
            <View className="w-full">
              <View className="flex-row justify-around">
                {[
                  { value: userInfo.stats.totalCatch, label: '总渔获', color: 'text-aqua-teal' },
                  { value: userInfo.stats.speciesCount, label: '鱼种数', color: 'text-sunrise-gold' },
                  { value: userInfo.stats.monthlyRank, label: '本月排名', color: 'text-primary-text' }
                ].map((stat) => (
                  <View key={stat.label} className="items-center">
                    <Text className={`text-2xl font-bold ${stat.color === 'text-aqua-teal' ? 'text-aqua-teal' : 
                      stat.color === 'text-sunrise-gold' ? 'text-sunrise-gold' : 'text-primary-text'}`}>
                      {stat.value}
                    </Text>
                    <Text className="text-xs text-secondary-text">{stat.label}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        </AnimatedView>

        {/* 成就展示 */}
        <AnimatedView style={achievementsStyle} className="px-6 py-4">
          <View className="bg-white p-4 rounded-xl shadow-sm">
            <Text className="text-lg font-bold text-primary-text mb-4">成就徽章</Text>
            <View className="flex-row justify-around">
              {achievements.map((achievement) => {
                const Icon = achievement.icon;
                return (
                  <TouchableOpacity key={achievement.id} className="items-center">
                    <LinearGradient
                      colors={achievement.unlocked ? achievement.colors : ['#E5E7EB', '#E5E7EB']}
                      className="w-12 h-12 rounded-full items-center justify-center mb-2"
                    >
                      <Icon 
                        color={achievement.unlocked ? 'white' : '#9CA3AF'} 
                        size={20} 
                      />
                    </LinearGradient>
                    <Text className={`text-xs ${
                      achievement.unlocked ? 'text-secondary-text' : 'text-gray-400'
                    }`}>
                      {achievement.name}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </AnimatedView>

        {/* 功能菜单 */}
        <AnimatedView style={menuStyle} className="px-6 py-4">
          <View className="bg-white rounded-xl shadow-sm overflow-hidden">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <TouchableOpacity
                  key={item.id}
                  className={`p-4 flex-row items-center justify-between ${
                    index < menuItems.length - 1 ? 'border-b border-gray-100' : ''
                  }`}
                  onPress={() => {
                    if (item.page) {
                      navigateToPage(item.page);
                    } else if (item.action) {
                      item.action();
                    }
                  }}
                >
                  <View className="flex-row items-center gap-x-3">
                    <Icon color="#00A79D" size={20} />
                    <View>
                      <View className="flex-row items-center gap-x-2">
                        <Text className="text-primary-text font-medium">{item.label}</Text>
                        {item.badge && (
                          <View className="bg-sunrise-gold px-2 py-1 rounded">
                            <Text className="text-xs text-white font-medium">
                              {item.badge}
                            </Text>
                          </View>
                        )}
                      </View>
                      {item.description && (
                        <Text className="text-xs text-secondary-text">{item.description}</Text>
                      )}
                    </View>
                  </View>
                  <ChevronRight color="#6B7280" size={16} />
                </TouchableOpacity>
              );
            })}
          </View>
        </AnimatedView>

        {/* 等级进度 */}
        <AnimatedView style={levelStyle} className="px-6 py-4">
          <LinearGradient
            colors={['rgba(0,167,157,0.1)', 'rgba(255,199,89,0.1)']}
            className="p-4 rounded-xl border border-aqua-teal/20"
          >
            <View className="flex-row items-center gap-x-2 mb-3">
              <Target color="#00A79D" size={20} />
              <Text className="text-sm font-medium text-primary-text">钓鱼等级</Text>
              <Text className="text-lg font-bold text-aqua-teal">Lv.15</Text>
            </View>
            
            <View className="space-y-2">
              <View className="flex-row items-center justify-between">
                <Text className="text-sm text-secondary-text">升级进度</Text>
                <Text className="text-sm font-medium text-primary-text">75%</Text>
              </View>
              <View className="w-full bg-white rounded-full h-2">
                <AnimatedView 
                  style={[progressStyle, { backgroundColor: '#00A79D', height: 8, borderRadius: 4 }]}
                />
              </View>
              <Text className="text-xs text-secondary-text">
                还需 12 次钓获升级到 16 级
              </Text>
            </View>
          </LinearGradient>
        </AnimatedView>
      </View>
    </ScrollView>
  );
}