/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
	],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // 钓鱼APP配色方案
        'ocean-blue': '#1A2E40',
        'light-slate': '#F0F2F5',
        'sunrise-gold': '#FFC759',
        'aqua-teal': '#00A79D',
        'primary-text': '#121212',
        'secondary-text': '#6B7280',
        'divider': '#E5E7EB',
        
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
        "scan-move": {
          "0%": { transform: "translateY(0)", opacity: "1" },
          "100%": { transform: "translateY(192px)", opacity: "0.3" }
        },
        "radar-pulse": {
          "0%": { transform: "scale(0.3)", opacity: "1" },
          "70%": { transform: "scale(1)", opacity: "0.3" },
          "100%": { transform: "scale(1.2)", opacity: "0" }
        },
        "unlock-scale": {
          "0%": { transform: "scale(0) rotate(0deg)" },
          "50%": { transform: "scale(1.2) rotate(180deg)" },
          "100%": { transform: "scale(1) rotate(360deg)" }
        },
        "confetti-fall": {
          "0%": { transform: "translateY(-50px) rotate(0deg)", opacity: "1" },
          "100%": { transform: "translateY(150px) rotate(360deg)", opacity: "0" }
        },
        "fade-in-up": {
          "0%": { opacity: "0", transform: "translateY(20px)" },
          "100%": { opacity: "1", transform: "translateY(0)" }
        },
        "pulse-ring": {
          "0%": { transform: "translate(-50%, -50%) scale(0.5)", opacity: "1" },
          "100%": { transform: "translate(-50%, -50%) scale(2.5)", opacity: "0" }
        },
        "pulse-glow": {
          "0%": { boxShadow: "0 0 20px rgba(0, 167, 157, 0.3)" },
          "100%": { boxShadow: "0 0 30px rgba(255, 199, 89, 0.4)" }
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "scan-move": "scan-move 2s linear infinite",
        "radar-pulse": "radar-pulse 2s ease-in-out infinite",
        "unlock-scale": "unlock-scale 1s ease-in-out",
        "confetti-fall": "confetti-fall 2s ease-out infinite",
        "fade-in-up": "fade-in-up 0.5s ease-out",
        "pulse-ring": "pulse-ring 2s ease-out infinite",
        "pulse-glow": "pulse-glow 2s ease-in-out infinite alternate"
      },
      backgroundImage: {
        'gradient-ocean': 'linear-gradient(135deg, #1A2E40 0%, #203347 100%)',
        'gradient-sunrise': 'linear-gradient(135deg, #FFC759 0%, #FFB840 100%)',
        'gradient-card': 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        'gradient-locked': 'linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%)',
        'gradient-badge': 'linear-gradient(135deg, #00A79D 0%, #FFC759 100%)'
      },
      boxShadow: {
        'record-button': '0 8px 24px rgba(255, 199, 89, 0.4)',
        'fish-hover': '0 8px 24px rgba(0, 167, 157, 0.3)',
        'card': '0 4px 12px rgba(0, 0, 0, 0.1)'
      }
    },
  },
  plugins: [require("tailwindcss-animate")],
}
