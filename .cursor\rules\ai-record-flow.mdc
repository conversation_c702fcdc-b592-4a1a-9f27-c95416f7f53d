---
description: AI渔获记录核心流程的实现规范
---

# AI渔获记录流程规范

参考完整实现：[fishing_app_prototype.html](mdc:fishing_app_prototype.html)

## 四步交互流程

### 第一步：照片上传 📸
**功能要求**：
- 支持多张照片上传（最多4张）
- 拍照和相册选择两种方式
- 实时照片预览和管理
- 只有上传照片后才能进入下一步

**UI元素**：
- 照片网格布局 (grid-cols-2)
- 删除按钮在照片右上角
- "添加更多"的占位卡片
- 禁用/启用状态的下一步按钮

### 第二步：AI扫描 🤖
**动画效果**：
- 扫描线条动画 (scan-line class)
- 雷达脉冲效果 (radar-pulse class)
- 加载旋转器 (loading-spinner class)

**进度提示**：
```
🔍 分析图像质量... 完成
🧠 AI模型识别中... (2秒后完成)
🎯 匹配数据库... (4秒后完成)
```

**技术实现**：5秒总处理时间，分阶段更新进度

### 第三步：解锁特效 ✨
**新鱼种解锁动画**：
- 全屏遮罩 + 金色星星图标
- 旋转缩放动画 (unlock-scale)
- 弹跳文字效果
- 图鉴完成度更新提示

**识别结果展示**：
- 主结果渐变边框卡片
- 星星徽章标识新解锁
- 备选结果可点击切换
- 置信度百分比显示

### 第四步：信息补充 📝
**必填字段**：重量(kg)
**可选字段**：长度、钓组配置、饵料、心得
**自动记录**：时间、位置(加密)、天气、水温

## 关键函数
- `goToStep(step)` - 步骤切换
- `startAIScanning()` - AI扫描动画
- `showUnlockEffect()` - 解锁特效
- `updateProgressBar(percentage)` - 进度更新