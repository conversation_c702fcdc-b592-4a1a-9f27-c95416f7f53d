import React from 'react'
import { Text, StyleSheet, ViewStyle, TextStyle, Pressable } from 'react-native'
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withSequence,
  runOnJS
} from 'react-native-reanimated'
import { SunriseGradient } from './GradientBackground'
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../../constants/colors'

interface AnimatedButtonProps {
  title: string
  onPress: () => void
  style?: ViewStyle
  textStyle?: TextStyle
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  icon?: React.ReactNode
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable)

export function AnimatedButton({
  title,
  onPress,
  style,
  textStyle,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  icon
}: AnimatedButtonProps) {
  const scale = useSharedValue(1)
  const opacity = useSharedValue(1)

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value
  }))

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 300 })
  }

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 })
  }

  const handlePress = () => {
    if (disabled) return
    
    // 触觉反馈动画
    scale.value = withSequence(
      withSpring(0.9, { damping: 15, stiffness: 300 }),
      withSpring(1, { damping: 15, stiffness: 300 })
    )
    
    // 延迟执行onPress以配合动画
    setTimeout(() => {
      runOnJS(onPress)()
    }, 100)
  }

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[`${size}Button`]]
    
    if (disabled) {
      baseStyle.push(styles.disabledButton)
    } else {
      switch (variant) {
        case 'primary':
          baseStyle.push(styles.primaryButton)
          break
        case 'secondary':
          baseStyle.push(styles.secondaryButton)
          break
        case 'outline':
          baseStyle.push(styles.outlineButton)
          break
      }
    }
    
    return baseStyle
  }

  const getTextStyle = () => {
    const baseStyle = [styles.text, styles[`${size}Text`]]
    
    if (disabled) {
      baseStyle.push(styles.disabledText)
    } else {
      switch (variant) {
        case 'primary':
          baseStyle.push(styles.primaryText)
          break
        case 'secondary':
          baseStyle.push(styles.secondaryText)
          break
        case 'outline':
          baseStyle.push(styles.outlineText)
          break
      }
    }
    
    return baseStyle
  }

  if (variant === 'primary' && !disabled) {
    return (
      <AnimatedPressable
        style={[animatedStyle, style]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        disabled={disabled}
      >
        <SunriseGradient style={getButtonStyle()}>
          {icon && <>{icon}</>}
          <Text style={[getTextStyle(), textStyle]}>{title}</Text>
        </SunriseGradient>
      </AnimatedPressable>
    )
  }

  return (
    <AnimatedPressable
      style={[animatedStyle, getButtonStyle(), style]}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={handlePress}
      disabled={disabled}
    >
      {icon && <>{icon}</>}
      <Text style={[getTextStyle(), textStyle]}>{title}</Text>
    </AnimatedPressable>
  )
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.full,
  },
  
  // 尺寸样式
  smallButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
  },
  
  mediumButton: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  
  largeButton: {
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
  },
  
  // 变体样式
  primaryButton: {
    ...Shadows.recordButton,
  },
  
  secondaryButton: {
    backgroundColor: Colors.lightSlate,
    ...Shadows.md,
  },
  
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Colors.aquaTeal,
  },
  
  disabledButton: {
    backgroundColor: Colors.divider,
    opacity: 0.6,
  },
  
  // 文字样式
  text: {
    fontWeight: FontWeights.semibold,
    textAlign: 'center',
  },
  
  smallText: {
    fontSize: FontSizes.sm,
  },
  
  mediumText: {
    fontSize: FontSizes.md,
  },
  
  largeText: {
    fontSize: FontSizes.lg,
  },
  
  primaryText: {
    color: Colors.cardBackground,
  },
  
  secondaryText: {
    color: Colors.primaryText,
  },
  
  outlineText: {
    color: Colors.aquaTeal,
  },
  
  disabledText: {
    color: Colors.secondaryText,
  },
})
