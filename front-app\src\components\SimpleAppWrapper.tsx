import React, { useState } from 'react';
import { View, Text } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { SimpleHomePage } from './pages/SimpleHomePage';
import { BottomNavigation } from './layout/BottomNavigation';
import type { PageType } from '../types';

export function SimpleAppWrapper() {
  const [currentPage, setCurrentPage] = useState<PageType>('home');

  const navigateToPage = (page: PageType) => {
    setCurrentPage(page);
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'home':
        return <SimpleHomePage navigateToPage={navigateToPage} />;
      case 'fishdex':
        return (
          <View className="flex-1 bg-light-slate items-center justify-center">
            <Text className="text-xl font-bold text-primary-text">鱼种图鉴</Text>
            <Text className="text-secondary-text mt-2">功能开发中...</Text>
          </View>
        );
      case 'record':
        return (
          <View className="flex-1 bg-light-slate items-center justify-center">
            <Text className="text-xl font-bold text-primary-text">记录渔获</Text>
            <Text className="text-secondary-text mt-2">功能开发中...</Text>
          </View>
        );
      case 'leaderboard':
        return (
          <View className="flex-1 bg-light-slate items-center justify-center">
            <Text className="text-xl font-bold text-primary-text">排行榜</Text>
            <Text className="text-secondary-text mt-2">功能开发中...</Text>
          </View>
        );
      case 'profile':
        return (
          <View className="flex-1 bg-light-slate items-center justify-center">
            <Text className="text-xl font-bold text-primary-text">个人档案</Text>
            <Text className="text-secondary-text mt-2">功能开发中...</Text>
          </View>
        );
      default:
        return <SimpleHomePage navigateToPage={navigateToPage} />;
    }
  };

  return (
    <SafeAreaProvider>
      <View className="flex-1 bg-light-slate">
        {renderCurrentPage()}
        <BottomNavigation
          currentPage={currentPage}
          onNavigate={navigateToPage}
        />
      </View>
    </SafeAreaProvider>
  );
}