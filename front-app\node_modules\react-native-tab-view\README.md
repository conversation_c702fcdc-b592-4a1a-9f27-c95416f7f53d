# React Native Tab View

React Native Tab View is a cross-platform Tab View component for React Native implemented using [`react-native-pager-view`](https://github.com/callstack/react-native-viewpager) on Android & iOS, and [PanResponder](https://reactnative.dev/docs/panresponder) on Web, macOS, and Windows.

Installation instructions and documentation can be found on the [React Navigation website](https://reactnavigation.org/docs/tab-view/).
