import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  Image,
  Alert,
  StyleSheet
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Camera, Image as ImageIcon, Check } from 'lucide-react-native';
import { TopNavigation } from '../layout/TopNavigation';
import { AnimatedButton } from '../common/AnimatedButton';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { colors, spacing, borderRadius, fontSize, fontWeight, shadows, globalStyles } from '../../constants/styles';
import type { PageType } from '../../types';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withRepeat, 
  withSequence, 
  Easing 
} from 'react-native-reanimated';

interface RecordPageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  appState: any;
}

export function RecordPage({ navigateToPage, showFishDetail, openPhotoModal, appState }: RecordPageProps) {
  const insets = useSafeAreaInsets();
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);

  // 扫描线动画
  const scanY = useSharedValue(0);
  const scanStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: scanY.value }],
  }));

  React.useEffect(() => {
    if (isProcessing) {
      scanY.value = withRepeat(
        withSequence(
          withTiming(0, { duration: 0 }),
          withTiming(192, { duration: 1500, easing: Easing.linear })
        ),
        -1,
        false
      );
    } else {
      scanY.value = 0;
    }
  }, [isProcessing]);

  const handleTakePhoto = () => {
    // 模拟拍照和AI识别过程
    setIsProcessing(true);
    
    // 模拟照片拍摄
    setTimeout(() => {
      setCapturedPhoto('https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=300&fit=crop&auto=format');
      setCurrentStep(2);
      
      // 模拟AI识别处理
      setTimeout(() => {
        setIsProcessing(false);
        setCurrentStep(3);
      }, 3000);
    }, 1000);
  };

  const handleConfirmRecord = () => {
    Alert.alert(
      '记录成功',
      '渔获已成功记录到您的图鉴中！',
      [
        { text: '查看详情', onPress: () => navigateToPage('fishdex') },
        { text: '继续记录', onPress: () => {
          setCurrentStep(1);
          setCapturedPhoto(null);
          setIsProcessing(false);
        }}
      ]
    );
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>
        拍摄渔获照片
      </Text>
      <Text style={styles.stepDescription}>
        请将鱼类放在光线充足的地方，确保图片清晰完整
      </Text>
      
      {/* 模拟相机预览框 */}
      <View style={styles.cameraPreview}>
        <Camera size={48} color={colors.secondaryText} />
        <Text style={styles.cameraPreviewText}>相机预览</Text>
      </View>

      <AnimatedButton
        onPress={handleTakePhoto}
        variant="primary"
        size="lg"
        icon={<Camera size={24} color={colors.oceanBlue} />}
      >
        拍摄照片
      </AnimatedButton>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>
        AI识别中...
      </Text>
      
      {capturedPhoto && (
        <View style={styles.processingImageContainer}>
          <Image
            source={{ uri: capturedPhoto }}
            style={styles.processingImage}
          />
          
          {/* 扫描动画覆盖层 */}
          {isProcessing && (
            <View style={styles.processingOverlay}>
              <Animated.View 
                style={[
                  styles.scanLine,
                  scanStyle
                ]}
              />
              <LoadingSpinner size="lg" />
              <Text style={styles.processingText}>正在识别鱼种...</Text>
            </View>
          )}
        </View>
      )}
      
      <Text style={[styles.stepDescription, { textAlign: 'center' }]}>
        AI正在分析您的渔获，请稍等片刻
      </Text>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.step3Container}>
      <Text style={[styles.stepTitle, { marginBottom: spacing.lg, textAlign: 'center' }]}>
        识别结果
      </Text>
      
      <View style={[globalStyles.card, styles.resultCard]}>
        <View style={styles.resultHeader}>
          <View style={styles.successIcon}>
            <Check size={32} color={colors.success} />
          </View>
          <Text style={styles.fishName}>鲫鱼</Text>
          <Text style={styles.scientificName}>Carassius auratus</Text>
        </View>
        
        <View style={styles.resultDetails}>
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>置信度</Text>
            <Text style={styles.resultValue}>95%</Text>
          </View>
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>拍摄时间</Text>
            <Text style={styles.resultValue}>刚刚</Text>
          </View>
          <View style={styles.resultRow}>
            <Text style={styles.resultLabel}>状态</Text>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>新解锁</Text>
            </View>
          </View>
        </View>
      </View>

      {capturedPhoto && (
        <View style={[globalStyles.card, styles.photoCard]}>
          <Text style={styles.photoCardTitle}>拍摄照片</Text>
          <Image
            source={{ uri: capturedPhoto }}
            style={styles.capturedPhotoFinal}
          />
        </View>
      )}

      <AnimatedButton
        onPress={handleConfirmRecord}
        variant="primary"
        size="lg"
        icon={<Check size={24} color={colors.oceanBlue} />}
      >
        确认记录
      </AnimatedButton>
    </View>
  );

  return (
    <View style={[globalStyles.container, { paddingBottom: insets.bottom + 20 }]}>
      <TopNavigation
        title="记录渔获"
        showBack
        onBack={() => navigateToPage('home')}
        stepInfo={`${currentStep}/3`}
      />
      
      {currentStep === 1 && renderStep1()}
      {currentStep === 2 && renderStep2()}
      {currentStep === 3 && renderStep3()}
    </View>
  );
}

const styles = StyleSheet.create({
  stepContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.lg,
  },
  stepTitle: {
    fontSize: fontSize.xxl,
    fontWeight: fontWeight.bold,
    color: colors.primaryText,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  stepDescription: {
    color: colors.secondaryText,
    marginBottom: spacing.xxl,
    textAlign: 'center',
    lineHeight: 24,
  },
  cameraPreview: {
    width: 320,
    height: 320,
    backgroundColor: colors.divider,
    borderRadius: borderRadius.xxl,
    marginBottom: spacing.xxl,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: colors.secondaryText,
  },
  cameraPreviewText: {
    color: colors.secondaryText,
    marginTop: spacing.sm,
  },
  processingImageContainer: {
    width: 320,
    height: 320,
    borderRadius: borderRadius.xxl,
    overflow: 'hidden',
    marginBottom: spacing.xxl,
    position: 'relative',
  },
  processingImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  processingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanLine: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: colors.aquaTeal,
  },
  processingText: {
    color: colors.white,
    marginTop: spacing.md,
  },
  step3Container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  resultCard: {
    marginBottom: spacing.lg,
  },
  resultHeader: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  successIcon: {
    width: 80,
    height: 80,
    backgroundColor: `${colors.success}20`,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  fishName: {
    fontSize: fontSize.xl,
    fontWeight: fontWeight.bold,
    color: colors.primaryText,
  },
  scientificName: {
    color: colors.secondaryText,
  },
  resultDetails: {
    gap: spacing.md,
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resultLabel: {
    color: colors.secondaryText,
  },
  resultValue: {
    color: colors.primaryText,
    fontWeight: fontWeight.medium,
  },
  statusBadge: {
    backgroundColor: colors.aquaTeal,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
  },
  statusText: {
    color: colors.white,
    fontSize: fontSize.xs,
  },
  photoCard: {
    marginBottom: spacing.lg,
  },
  photoCardTitle: {
    fontWeight: fontWeight.medium,
    color: colors.primaryText,
    marginBottom: spacing.md,
  },
  capturedPhotoFinal: {
    width: '100%',
    height: 192,
    borderRadius: borderRadius.md,
    resizeMode: 'cover',
  },
});