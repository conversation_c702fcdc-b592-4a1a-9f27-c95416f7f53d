'use client'

import { motion } from 'framer-motion'
import { Trophy, MapPin, Medal, Crown } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import type { PageType } from '../AppWrapper'

interface LeaderboardPageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  appState: any
}

export function LeaderboardPage({ navigateToPage }: LeaderboardPageProps) {
  const leaderboardTypes = [
    { id: 'monthly-weight', name: '月度最重', active: true },
    { id: 'total-catch', name: '总渔获数', active: false },
    { id: 'species-collection', name: '鱼种收集', active: false }
  ]

  const topRankers = [
    {
      rank: 1,
      name: '钓鱼大师',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format',
      fish: '草鱼',
      weight: '2.8kg',
      date: '1月12日',
      score: 2800
    },
    {
      rank: 2,
      name: '江边老钓',
      avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=40&h=40&fit=crop&crop=face&auto=format',
      fish: '鲤鱼',
      weight: '2.1kg',
      date: '1月10日',
      score: 2100
    },
    {
      rank: 3,
      name: '钓鱼新手',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format',
      fish: '鲫鱼',
      weight: '1.8kg',
      date: '1月8日',
      score: 1800
    }
  ]

  const otherRankers = [
    {
      rank: 4,
      name: '湖边渔者',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '鲤鱼',
      weight: '1.5kg',
      score: 1500
    },
    {
      rank: 5,
      name: '钓鱼小白',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '草鱼',
      weight: '1.4kg',
      score: 1400
    }
  ]

  const userRank = {
    rank: 8,
    fish: '草鱼',
    weight: '1.2kg',
    score: 1200,
    progress: '距离第7名还差0.1kg'
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="text-yellow-500" size={20} />
      case 2:
        return <Medal className="text-gray-400" size={18} />
      case 3:
        return <Medal className="text-orange-500" size={18} />
      default:
        return <span className="text-sm font-bold">{rank}</span>
    }
  }

  const getRankBg = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600'
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500'
      case 3:
        return 'bg-gradient-to-r from-orange-400 to-orange-600'
      default:
        return 'bg-light-slate'
    }
  }

  return (
    <div className="pb-24">
      <TopNavigation
        title="排行榜"
        showBack
        onBack={() => navigateToPage('homepage')}
      />

      {/* 地区选择 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-secondary-text">当前地区</div>
              <div className="text-lg font-bold text-primary-text">广东省 深圳市</div>
            </div>
            <motion.button 
              className="text-aqua-teal"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <MapPin size={20} />
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* 排行榜类型 */}
      <motion.div 
        className="px-6 py-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex space-x-2 overflow-x-auto">
          {leaderboardTypes.map((type, index) => (
            <motion.button
              key={type.id}
              className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all ${
                type.active 
                  ? 'bg-aqua-teal text-white' 
                  : 'bg-white text-secondary-text border border-divider hover:border-aqua-teal'
              }`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.05 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {type.name}
            </motion.button>
          ))}
        </div>
      </motion.div>

      {/* 我的排名 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="bg-gradient-sunrise p-4 rounded-xl text-white">
          <div className="flex items-center space-x-3">
            <motion.div 
              className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-xl font-bold"
              whileHover={{ scale: 1.1 }}
            >
              {userRank.rank}
            </motion.div>
            <div className="flex-1">
              <div className="font-bold">你的排名</div>
              <div className="text-sm opacity-90">{userRank.fish} {userRank.weight} | 本月最佳</div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold">{userRank.weight}</div>
              <div className="text-xs opacity-90">{userRank.progress}</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 排行榜列表 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          {/* 前三名 */}
          <div className="space-y-0">
            {topRankers.map((ranker, index) => (
              <motion.div
                key={ranker.rank}
                className="p-4 border-b border-divider"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 + index * 0.1 }}
                whileHover={{ backgroundColor: '#F0F2F5' }}
              >
                <div className="flex items-center space-x-3">
                  <motion.div 
                    className={`w-12 h-12 ${getRankBg(ranker.rank)} rounded-full flex items-center justify-center text-white font-bold text-lg`}
                    whileHover={{ scale: 1.1 }}
                  >
                    {getRankIcon(ranker.rank)}
                  </motion.div>
                  <Image
                    src={ranker.avatar}
                    alt="用户"
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-primary-text">{ranker.name}</div>
                    <div className="text-sm text-secondary-text">{ranker.fish} {ranker.weight}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-primary-text">{ranker.weight}</div>
                    <div className="text-xs text-secondary-text">{ranker.date}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* 其他排名 */}
          <div className="space-y-0">
            {otherRankers.map((ranker, index) => (
              <motion.div
                key={ranker.rank}
                className="p-4 border-b border-divider"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 + index * 0.05 }}
                whileHover={{ backgroundColor: '#F0F2F5' }}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-light-slate rounded-full flex items-center justify-center text-secondary-text font-medium">
                    {ranker.rank}
                  </div>
                  <Image
                    src={ranker.avatar}
                    alt="用户"
                    width={32}
                    height={32}
                    className="rounded-full"
                  />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-primary-text">{ranker.name}</div>
                    <div className="text-xs text-secondary-text">{ranker.fish} {ranker.weight}</div>
                  </div>
                  <div className="text-sm font-medium text-primary-text">{ranker.weight}</div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* 查看更多 */}
          <motion.div 
            className="p-4 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2 }}
          >
            <motion.button 
              className="text-aqua-teal text-sm font-medium"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              查看完整排行榜 →
            </motion.button>
          </motion.div>
        </div>
      </motion.div>

      {/* 排行榜说明 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
      >
        <div className="bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 p-4 rounded-xl border border-aqua-teal/20">
          <div className="flex items-center space-x-2 mb-2">
            <Trophy className="text-aqua-teal" size={20} />
            <span className="text-sm font-medium text-primary-text">排行规则</span>
          </div>
          <div className="text-xs text-secondary-text leading-relaxed">
            • 月度排行榜每月1号重置<br/>
            • 排名基于当月最大单鱼重量<br/>
            • 地区排名基于GPS定位自动匹配<br/>
            • 数据每小时更新一次
          </div>
        </div>
      </motion.div>
    </div>
  )
}
