import React from 'react'
import { StyleSheet, ViewStyle } from 'react-native'
import { LinearGradient } from 'expo-linear-gradient'
import { Colors } from '../../constants/colors'

interface GradientBackgroundProps {
  children: React.ReactNode
  colors?: string[]
  style?: ViewStyle
  start?: { x: number; y: number }
  end?: { x: number; y: number }
}

export function GradientBackground({ 
  children, 
  colors = Colors.gradients.ocean,
  style,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 }
}: GradientBackgroundProps) {
  return (
    <LinearGradient
      colors={colors}
      start={start}
      end={end}
      style={[styles.gradient, style]}
    >
      {children}
    </LinearGradient>
  )
}

// 预定义的渐变组件
export function OceanGradient({ children, style }: { children: React.ReactNode; style?: ViewStyle }) {
  return (
    <GradientBackground colors={Colors.gradients.ocean} style={style}>
      {children}
    </GradientBackground>
  )
}

export function SunriseGradient({ children, style }: { children: React.ReactNode; style?: ViewStyle }) {
  return (
    <GradientBackground colors={Colors.gradients.sunrise} style={style}>
      {children}
    </GradientBackground>
  )
}

export function CardGradient({ children, style }: { children: React.ReactNode; style?: ViewStyle }) {
  return (
    <GradientBackground colors={Colors.gradients.card} style={style}>
      {children}
    </GradientBackground>
  )
}

export function BadgeGradient({ children, style }: { children: React.ReactNode; style?: ViewStyle }) {
  return (
    <GradientBackground colors={Colors.gradients.badge} style={style}>
      {children}
    </GradientBackground>
  )
}

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
  }
})
