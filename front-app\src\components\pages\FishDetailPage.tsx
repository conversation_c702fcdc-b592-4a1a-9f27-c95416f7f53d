import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert, Share, Dimensions } from 'react-native';
import { Share2, Star, TrendingUp, Weight, Ruler, Target, Calendar, MapPin, Cloud, ArrowRight, ChevronDown } from 'lucide-react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  withSpring
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { TopNavigation } from '../layout/TopNavigation';
import type { PageType } from '../AppWrapper';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface FishDetailPageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  fishId: string;
  appState: any;
}

export function FishDetailPage({ navigateToPage, fishId }: FishDetailPageProps) {
  const headerOpacity = useSharedValue(0);
  const analysisOpacity = useSharedValue(0);
  const historyOpacity = useSharedValue(0);
  const suggestionOpacity = useSharedValue(0);
  const avatarScale = useSharedValue(0);

  React.useEffect(() => {
    headerOpacity.value = withDelay(100, withTiming(1, { duration: 600 }));
    avatarScale.value = withDelay(200, withSpring(1));
    analysisOpacity.value = withDelay(600, withTiming(1, { duration: 600 }));
    historyOpacity.value = withDelay(1000, withTiming(1, { duration: 600 }));
    suggestionOpacity.value = withDelay(1200, withTiming(1, { duration: 600 }));
  }, []);

  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: withTiming(headerOpacity.value === 1 ? 0 : 20) }],
  }));

  const avatarStyle = useAnimatedStyle(() => ({
    transform: [{ scale: avatarScale.value }],
  }));

  const analysisStyle = useAnimatedStyle(() => ({
    opacity: analysisOpacity.value,
    transform: [{ translateY: withTiming(analysisOpacity.value === 1 ? 0 : 20) }],
  }));

  const historyStyle = useAnimatedStyle(() => ({
    opacity: historyOpacity.value,
    transform: [{ translateY: withTiming(historyOpacity.value === 1 ? 0 : 20) }],
  }));

  const suggestionStyle = useAnimatedStyle(() => ({
    opacity: suggestionOpacity.value,
    transform: [{ translateY: withTiming(suggestionOpacity.value === 1 ? 0 : 20) }],
  }));

  const fishSpeciesData = {
    'crucian': {
      name: '鲫鱼',
      scientific: 'Crucian Carp',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format',
      totalCatches: 8,
      maxWeight: '1.2kg',
      successRate: '75%',
      unlockDate: '2024年1月10日',
      bestRig: '3号主线+2号子线',
      bestBait: '玉米粒'
    },
    'grass-carp': {
      name: '草鱼',
      scientific: 'Grass Carp',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=120&h=120&fit=crop&auto=format',
      totalCatches: 5,
      maxWeight: '3.2kg',
      successRate: '60%',
      unlockDate: '2024年1月8日',
      bestRig: '5号主线+4号子线',
      bestBait: '玉米粒'
    },
    'carp': {
      name: '鲤鱼',
      scientific: 'Common Carp',
      image: 'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=120&h=120&fit=crop&auto=format',
      totalCatches: 12,
      maxWeight: '2.8kg',
      successRate: '85%',
      unlockDate: '2024年1月5日',
      bestRig: '4号主线+3号子线',
      bestBait: '面包虫'
    }
  };

  const fishData = fishSpeciesData[fishId as keyof typeof fishSpeciesData] || fishSpeciesData.crucian;

  const weightTrend = [
    { weight: 0.8, date: '1/9' },
    { weight: 0.5, date: '1/10' },
    { weight: 1.0, date: '1/11' },
    { weight: 1.2, date: '1/12' },
    { weight: 0.9, date: '1/13' },
    { weight: 0.6, date: '1/14' },
    { weight: 0.8, date: '1/15' }
  ];

  const catchHistory = [
    {
      id: 1,
      location: '东湖公园钓点',
      date: '今天 14:30',
      weight: '0.8kg',
      length: '25cm',
      weather: '多云 15°C',
      rig: '3号主线+2号子线',
      bait: '玉米粒',
      isRecord: false
    },
    {
      id: 2,
      location: '西湖钓点',
      date: '1月14日 09:15',
      weight: '1.2kg',
      length: '32cm',
      weather: '晴 18°C',
      rig: '4号主线+3号子线',
      bait: '面包虫',
      isRecord: true
    },
    {
      id: 3,
      location: '北河钓场',
      date: '1月12日 16:20',
      weight: '0.6kg',
      length: '22cm',
      weather: '小雨 12°C',
      rig: '2号主线+1号子线',
      bait: '蚯蚓',
      isRecord: false
    }
  ];

  const shareSpeciesData = () => {
    Share.share({
      message: `我已经钓获了${fishData.totalCatches}条${fishData.name}，最大重量${fishData.maxWeight}！`,
      title: `我的${fishData.name}钓获统计`,
    }).catch(() => {
      Alert.alert('分享失败', '请稍后再试');
    });
  };

  const viewCatchDetail = (recordId: number) => {
    Alert.alert('记录详情', `查看记录 #${recordId} 的详细信息`);
  };

  const maxTrendWeight = Math.max(...weightTrend.map(t => t.weight));

  return (
    <ScrollView 
      className="flex-1 bg-page-bg" 
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <View>
        <TopNavigation
          title={`${fishData.name}详情`}
          showBack
          onBack={() => navigateToPage('fishdex')}
          rightIcon="share"
          onRightClick={shareSpeciesData}
        />

        {/* 鱼种信息卡片 */}
        <AnimatedView style={headerStyle} className="px-6 py-4">
          <View className="bg-white p-6 rounded-xl shadow-sm items-center">
            <AnimatedView style={avatarStyle} className="relative mb-4">
              <View className="w-24 h-24 rounded-xl border-2 border-aqua-teal overflow-hidden">
                <Image
                  source={{ uri: fishData.image }}
                  className="w-full h-full"
                  resizeMode="cover"
                />
              </View>
              <View className="absolute -top-2 -right-2 w-8 h-8 bg-sunrise-gold rounded-full items-center justify-center">
                <Star color="white" size={16} />
              </View>
            </AnimatedView>
            
            <Text className="text-xl font-bold text-primary-text mb-1">
              {fishData.name}
            </Text>
            <Text className="text-sm text-secondary-text mb-4">
              {fishData.scientific}
            </Text>
            
            {/* 统计数据 */}
            <View className="w-full mb-4">
              <View className="flex-row justify-around">
                {[
                  { value: fishData.totalCatches, label: '总钓获', color: 'text-aqua-teal' },
                  { value: fishData.maxWeight, label: '最大重量', color: 'text-sunrise-gold' },
                  { value: fishData.successRate, label: '成功率', color: 'text-primary-text' }
                ].map((stat) => (
                  <View key={stat.label} className="items-center">
                    <Text className={`text-2xl font-bold ${
                      stat.color === 'text-aqua-teal' ? 'text-aqua-teal' :
                      stat.color === 'text-sunrise-gold' ? 'text-sunrise-gold' : 'text-primary-text'
                    }`}>
                      {stat.value}
                    </Text>
                    <Text className="text-xs text-secondary-text">{stat.label}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* 解锁时间 */}
            <View className="flex-row items-center">
              <Calendar color="#6B7280" size={12} />
              <Text className="text-xs text-secondary-text ml-1">
                首次解锁：{fishData.unlockDate}
              </Text>
            </View>
          </View>
        </AnimatedView>

        {/* 数据分析 */}
        <AnimatedView style={analysisStyle} className="px-6 py-4">
          <View className="bg-white p-4 rounded-xl shadow-sm">
            <Text className="text-lg font-bold text-primary-text mb-4">📊 数据分析</Text>
            
            {/* 重量趋势 */}
            <View className="mb-6">
              <View className="flex-row items-center justify-between mb-2">
                <Text className="text-sm font-medium text-primary-text">重量趋势</Text>
                <Text className="text-xs text-secondary-text">近7次记录</Text>
              </View>
              <View className="h-20 bg-light-slate rounded-lg p-3">
                <View className="flex-row items-end justify-between h-full">
                  {weightTrend.map((data, index) => {
                    const height = (data.weight / maxTrendWeight) * 100;
                    const isMax = data.weight === maxTrendWeight;
                    return (
                      <View key={index} className="items-center">
                        <View 
                          className={`w-6 rounded-t ${isMax ? 'bg-sunrise-gold' : 'bg-aqua-teal'}`}
                          style={{ height: `${height}%` }}
                        />
                        <Text className="text-xs text-secondary-text mt-1">{data.weight}</Text>
                      </View>
                    );
                  })}
                </View>
              </View>
            </View>

            {/* 最佳配置 */}
            <View className="flex-row gap-x-3">
              <View className="flex-1 bg-light-slate p-3 rounded-lg">
                <Text className="text-xs text-secondary-text mb-1">最佳钓组</Text>
                <Text className="text-sm font-medium text-primary-text">{fishData.bestRig}</Text>
              </View>
              <View className="flex-1 bg-light-slate p-3 rounded-lg">
                <Text className="text-xs text-secondary-text mb-1">最佳饵料</Text>
                <Text className="text-sm font-medium text-primary-text">{fishData.bestBait}</Text>
              </View>
            </View>
          </View>
        </AnimatedView>

        {/* 历史记录列表 */}
        <AnimatedView style={historyStyle} className="px-6 py-4">
          <View className="bg-white rounded-xl shadow-sm overflow-hidden">
            <View className="p-4 border-b border-gray-100">
              <View className="flex-row items-center justify-between">
                <Text className="text-lg font-bold text-primary-text">🎣 钓获记录</Text>
                <View className="flex-row items-center gap-x-2">
                  <Text className="text-sm text-secondary-text">按时间排序</Text>
                  <TrendingUp color="#6B7280" size={14} />
                </View>
              </View>
            </View>

            {/* 记录列表 */}
            {catchHistory.map((record) => (
              <TouchableOpacity
                key={record.id}
                className="p-4 border-b border-gray-50"
                onPress={() => viewCatchDetail(record.id)}
              >
                <View className="flex-row items-start gap-x-3">
                  <Image
                    source={{ uri: fishData.image }}
                    className="w-12 h-12 rounded-lg"
                    resizeMode="cover"
                  />
                  <View className="flex-1">
                    <View className="flex-row items-center justify-between mb-1">
                      <Text className="text-sm font-medium text-primary-text">{record.location}</Text>
                      <Text className="text-xs text-secondary-text">{record.date}</Text>
                    </View>
                    <View className="flex-row items-center gap-x-4 mb-2">
                      <View className="flex-row items-center gap-x-1">
                        <Weight color="#00A79D" size={12} />
                        <Text className="text-xs text-secondary-text">{record.weight}</Text>
                      </View>
                      <View className="flex-row items-center gap-x-1">
                        <Ruler color="#00A79D" size={12} />
                        <Text className="text-xs text-secondary-text">{record.length}</Text>
                      </View>
                      <View className="flex-row items-center gap-x-1">
                        <Cloud color="#00A79D" size={12} />
                        <Text className="text-xs text-secondary-text">{record.weather}</Text>
                      </View>
                    </View>
                    <Text className="text-xs text-secondary-text">
                      钓组: {record.rig} | 饵料: {record.bait}
                    </Text>
                  </View>
                  <View className="items-end">
                    {record.isRecord && (
                      <View className="bg-sunrise-gold px-2 py-1 rounded mb-1">
                        <Text className="text-xs text-white">最大</Text>
                      </View>
                    )}
                    <ArrowRight color="#6B7280" size={16} />
                  </View>
                </View>
              </TouchableOpacity>
            ))}

            {/* 查看更多 */}
            <TouchableOpacity className="p-4 items-center">
              <View className="flex-row items-center gap-x-1">
                <Text className="text-aqua-teal text-sm font-medium">
                  查看全部 {fishData.totalCatches} 条记录
                </Text>
                <ChevronDown color="#00A79D" size={14} />
              </View>
            </TouchableOpacity>
          </View>
        </AnimatedView>

        {/* 钓鱼建议 */}
        <AnimatedView style={suggestionStyle} className="px-6 py-4">
          <LinearGradient
            colors={['rgba(0,167,157,0.1)', 'rgba(255,199,89,0.1)']}
            className="p-4 rounded-xl border border-aqua-teal/20"
          >
            <View className="flex-row items-center gap-x-2 mb-2">
              <Target color="#00A79D" size={20} />
              <Text className="text-sm font-medium text-primary-text">钓鱼建议</Text>
            </View>
            <Text className="text-xs text-secondary-text leading-5">
              基于您的钓获数据分析，{fishData.name}在温度适中、天气稳定的条件下活跃度更高。
              建议使用{fishData.bestRig}的钓组配置，{fishData.bestBait}作为主要饵料，
              在水草边缘或结构物附近垂钓效果更佳。
            </Text>
          </LinearGradient>
        </AnimatedView>
      </View>
    </ScrollView>
  );
}