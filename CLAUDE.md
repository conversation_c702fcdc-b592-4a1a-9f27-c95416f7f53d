# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个以AI技术为驱动、以游戏化收集为核心乐趣的钓鱼日志应用。当前处于原型阶段，包含完整的UI高保真原型和详细的产品需求文档。

## 核心功能模块

1. **AI渔获日志** - 核心功能，用户拍照后AI自动识别鱼种并记录
2. **鱼种图鉴** - 游戏化收集系统，解锁新鱼种获得成就感  
3. **用户账户与个人档案** - 注册登录、个人信息管理
4. **地区排行榜** - 基于地理位置的月度竞争系统

## 技术架构

- **前端**: React Native
- **后端**: Go + Gin框架 + GORM + TDD测试
- **数据库**: PostgreSQL + PostGIS（处理地理位置数据）
- **AI服务**: 
  - Fishial.ai 鱼种识别
  - Clipdrop.co 背景移除API
- **第三方服务**: 高德地图/Mapbox、和风天气/OpenWeatherMap

## 设计系统

严格遵循配色方案文档的颜色系统：

### 主色系
- 静谧深蓝 `#1A2E40` - 导航栏、标签栏背景  
- 高级灰 `#F0F2F5` - 页面主背景色

### 点缀色  
- 拂晓金 `#FFC759` - CTA按钮、选中状态、徽章
- 水波青 `#00A79D` - 次要按钮、链接、状态提示

### 中性色
- 主要文本黑 `#121212` - 标题、正文
- 次要文本灰 `#6B7280` - 辅助文字、未激活状态
- 卡片背景 `#FFFFFF` - 信息卡片、弹窗背景

## 性能和安全要求

- **AI处理时间**: 5-8秒内返回识别+抠图结果
- **记录流程**: 5步以内、60秒内完成  
- **GPS隐私**: 位置数据必须加密存储，精确钓点绝不公开展示
- **闪退率**: < 0.5%（主流机型）

## 开发优先级

1. **AI渔获日志** - 核心功能，验证 "AI识别 -> 自动记录 -> 解锁图鉴" 的价值主张
2. **鱼种图鉴** - 游戏化核心，驱动用户持续使用
3. **用户系统** - 支撑功能，必要但非MVP核心  
4. **排行榜** - 社交功能，最后实现

## 重要文件

- `开发需求文档.md` - 完整的产品需求和用户故事
- `配色方案.md` - 详细的设计系统规范
- `fishing_app_prototype.html` - 完整的UI原型，包含所有页面和交互
- `.cursor/rules/` - 包含开发指导原则、设计规范等详细说明

## 开发原则

- **TDD测试驱动** - 核心功能必须有测试覆盖
- **移动优先** - 针对手机屏幕优化
- **游戏化体验** - 通过成就感驱动用户持续使用
- **即时反馈** - 每个操作都有明确的视觉反馈