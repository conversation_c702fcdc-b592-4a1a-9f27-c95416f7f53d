{"name": "react-native-modal", "version": "14.0.0-rc.1", "description": "An enhanced React Native modal", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/"], "scripts": {"test": "yarn run lint", "build": "tsc", "dev": "tsc --watch", "test:ts": "tsc --noEmit"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "keywords": ["react-native", "react", "native", "modal", "android", "ios", "backdrop", "simple", "animated"], "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "license": "MIT", "homepage": "https://github.com/react-native-modal/react-native-modal", "repository": {"type": "git", "url": "https://github.com/react-native-modal/react-native-modal"}, "dependencies": {"react-native-animatable": "1.4.0"}, "devDependencies": {"@types/react": "^19.0.10", "husky": "^3.0.9", "postinstall": "^0.5.1", "prettier": "^1.18.2", "pretty-quick": "^2.0.0", "react": "19.0.0", "react-native": "0.76.7", "typescript": "5.8.2"}, "peerDependencies": {"react": "*", "react-native": ">=0.70.0"}, "packageManager": "yarn@4.7.0"}