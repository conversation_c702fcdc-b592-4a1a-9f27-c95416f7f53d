import React from 'react'
import { View, Text, StyleSheet, ScrollView, Image, Pressable } from 'react-native'
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withD<PERSON>y,
  FadeInUp,
  FadeInLeft
} from 'react-native-reanimated'
import { Ionicons } from '@expo/vector-icons'
import { OceanGradient } from '../common/GradientBackground'
import { AnimatedButton } from '../common/AnimatedButton'
import { globalStyles } from '../../styles'
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../../constants/colors'
import type { NavigationProps, UserStats, FishRecord, LeaderboardEntry } from '../../types'

const AnimatedPressable = Animated.createAnimatedComponent(Pressable)

export function HomePage({ navigateToPage, showFishDetail, openPhotoModal }: NavigationProps) {
  const userStats: UserStats[] = [
    { value: '128', label: '总渔获', color: Colors.primaryText },
    { value: '32', label: '鱼种数', color: Colors.aquaTeal },
    { value: '15', label: '本月记录', color: Colors.sunriseGold }
  ]

  const recentRecords: FishRecord[] = [
    {
      id: 1,
      fishName: '鲫鱼',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop&auto=format',
      weight: '0.8kg',
      length: '25cm',
      time: '今天 14:30',
      location: '东湖钓点',
      isNew: true
    },
    {
      id: 2,
      fishName: '草鱼',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=60&h=60&fit=crop&auto=format',
      weight: '1.2kg',
      length: '35cm',
      time: '昨天 09:15',
      location: '西湖钓点',
      isNew: false
    }
  ]

  const leaderboard: LeaderboardEntry[] = [
    {
      rank: 1,
      name: '钓鱼大师',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format',
      score: 2800
    },
    {
      rank: 2,
      name: '江边老钓',
      avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=32&h=32&fit=crop&crop=face&auto=format',
      score: 2100
    }
  ]

  const StatCard = ({ stat, index }: { stat: UserStats; index: number }) => {
    const scale = useSharedValue(0.8)
    const opacity = useSharedValue(0)

    React.useEffect(() => {
      opacity.value = withDelay(300 + index * 100, withSpring(1))
      scale.value = withDelay(300 + index * 100, withSpring(1))
    }, [])

    const animatedStyle = useAnimatedStyle(() => ({
      opacity: opacity.value,
      transform: [{ scale: scale.value }]
    }))

    return (
      <Animated.View style={[styles.statCard, animatedStyle]}>
        <Text style={[styles.statValue, { color: stat.color }]}>{stat.value}</Text>
        <Text style={styles.statLabel}>{stat.label}</Text>
      </Animated.View>
    )
  }

  return (
    <ScrollView style={globalStyles.container} showsVerticalScrollIndicator={false}>
      {/* 顶部导航 */}
      <OceanGradient style={styles.header}>
        <Animated.View 
          entering={FadeInUp.delay(100)}
          style={styles.headerContent}
        >
          <View style={styles.userInfo}>
            <AnimatedPressable style={styles.avatar}>
              <Image
                source={{ uri: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format' }}
                style={styles.avatarImage}
              />
            </AnimatedPressable>
            <View>
              <Text style={styles.userName}>渔友小明</Text>
              <Text style={styles.userLevel}>钓龄 2年</Text>
            </View>
          </View>
          <AnimatedPressable style={styles.notificationButton}>
            <Ionicons name="notifications-outline" size={20} color={Colors.cardBackground} />
          </AnimatedPressable>
        </Animated.View>
      </OceanGradient>

      {/* 快速统计 */}
      <Animated.View 
        entering={FadeInUp.delay(200)}
        style={styles.statsSection}
      >
        <View style={styles.statsGrid}>
          {userStats.map((stat, index) => (
            <StatCard key={stat.label} stat={stat} index={index} />
          ))}
        </View>
      </Animated.View>

      {/* 记录渔获按钮 */}
      <Animated.View 
        entering={FadeInUp.delay(500)}
        style={styles.recordSection}
      >
        <AnimatedButton
          title="记录渔获"
          onPress={() => navigateToPage('record')}
          size="large"
          icon={<Ionicons name="camera" size={24} color={Colors.cardBackground} />}
          style={styles.recordButton}
        />
      </Animated.View>

      {/* 本周排行榜 */}
      <Animated.View 
        entering={FadeInUp.delay(600)}
        style={styles.section}
      >
        <View style={styles.card}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>本周排行榜</Text>
            <Pressable onPress={() => navigateToPage('leaderboard')}>
              <Text style={styles.sectionLink}>查看全部</Text>
            </Pressable>
          </View>
          <View style={styles.leaderboardList}>
            {leaderboard.map((item, index) => (
              <Animated.View
                key={item.rank}
                entering={FadeInLeft.delay(700 + index * 100)}
                style={styles.leaderboardItem}
              >
                <View style={[
                  styles.rankBadge,
                  { backgroundColor: item.rank === 1 ? Colors.sunriseGold : Colors.secondaryText }
                ]}>
                  <Text style={styles.rankText}>{item.rank}</Text>
                </View>
                <Image source={{ uri: item.avatar }} style={styles.leaderAvatar} />
                <View style={styles.leaderInfo}>
                  <Text style={styles.leaderName}>{item.name}</Text>
                  <Text style={styles.leaderScore}>积分: {item.score}</Text>
                </View>
              </Animated.View>
            ))}
          </View>
        </View>
      </Animated.View>

      {/* 最近记录 */}
      <Animated.View 
        entering={FadeInUp.delay(800)}
        style={[styles.section, { paddingBottom: 100 }]}
      >
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>最近记录</Text>
          <View style={styles.recordsList}>
            {recentRecords.map((record, index) => (
              <AnimatedPressable
                key={record.id}
                entering={FadeInUp.delay(900 + index * 100)}
                style={styles.recordItem}
                onPress={() => showFishDetail('crucian')}
              >
                <Image source={{ uri: record.image }} style={styles.recordImage} />
                <View style={styles.recordInfo}>
                  <View style={styles.recordHeader}>
                    <Text style={styles.recordName}>{record.fishName}</Text>
                    {record.isNew && (
                      <View style={styles.newBadge}>
                        <Text style={styles.newBadgeText}>新解锁</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.recordDetails}>
                    重量: {record.weight} | 长度: {record.length}
                  </Text>
                  <Text style={styles.recordMeta}>
                    {record.time} | {record.location}
                  </Text>
                </View>
              </AnimatedPressable>
            ))}
          </View>
        </View>
      </Animated.View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },

  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  avatar: {
    marginRight: Spacing.md,
  },

  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: Colors.sunriseGold,
  },

  userName: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.cardBackground,
  },

  userLevel: {
    fontSize: FontSizes.sm,
    color: Colors.cardBackground,
    opacity: 0.8,
  },

  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  statsSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },

  statsGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },

  statCard: {
    flex: 1,
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.xl,
    padding: Spacing.md,
    alignItems: 'center',
    ...Shadows.md,
  },

  statValue: {
    fontSize: FontSizes.xxl,
    fontWeight: FontWeights.bold,
  },

  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.secondaryText,
    marginTop: Spacing.xs,
  },

  recordSection: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },

  recordButton: {
    width: '100%',
  },

  section: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },

  card: {
    backgroundColor: Colors.cardBackground,
    borderRadius: BorderRadius.xl,
    padding: Spacing.md,
    ...Shadows.md,
  },

  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    color: Colors.primaryText,
  },

  sectionLink: {
    fontSize: FontSizes.sm,
    color: Colors.aquaTeal,
  },

  leaderboardList: {
    gap: Spacing.md,
  },

  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },

  rankBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },

  rankText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.bold,
    color: Colors.cardBackground,
  },

  leaderAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },

  leaderInfo: {
    flex: 1,
  },

  leaderName: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.primaryText,
  },

  leaderScore: {
    fontSize: FontSizes.xs,
    color: Colors.secondaryText,
  },

  recordsList: {
    gap: Spacing.md,
  },

  recordItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.md,
    padding: Spacing.sm,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.background,
  },

  recordImage: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.lg,
  },

  recordInfo: {
    flex: 1,
  },

  recordHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  recordName: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.primaryText,
  },

  newBadge: {
    backgroundColor: Colors.aquaTeal,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },

  newBadgeText: {
    fontSize: FontSizes.xs,
    color: Colors.cardBackground,
  },

  recordDetails: {
    fontSize: FontSizes.xs,
    color: Colors.secondaryText,
    marginTop: 2,
  },

  recordMeta: {
    fontSize: FontSizes.xs,
    color: Colors.secondaryText,
    marginTop: 2,
  },
})
