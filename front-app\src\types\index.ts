export type PageType = 
  | 'home' 
  | 'homepage'
  | 'fishdex' 
  | 'record' 
  | 'profile' 
  | 'leaderboard'
  | 'career'
  | 'fishing-spots'
  | 'record-success'
  | 'fish-detail'

export interface FishRecord {
  id: number
  fishName: string
  image: string
  weight: string
  length: string
  time: string
  location: string
  isNew?: boolean
}

export interface LeaderboardItem {
  rank: number
  name: string
  avatar: string
  fish: string
  weight: string
}

export interface UserStats {
  value: string
  label: string
  color: string
}

export interface FishSpecies {
  id: string
  name: string
  scientificName: string
  image: string
  isUnlocked: boolean
  description?: string
  habitat?: string
  bait?: string
  tips?: string
}

export interface AppState {
  currentPage: PageType
  selectedFishId?: string
  photoModalPhotos: string[]
  photoModalIndex: number
  isPhotoModalVisible: boolean
  recordData?: any
  uploadedPhotos: Array<{url: string, type: string}>
}