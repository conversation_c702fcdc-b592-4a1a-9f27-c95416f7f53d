{"version": 3, "file": "WebUserMediaManager.js", "sourceRoot": "", "sources": ["../../src/web/WebUserMediaManager.ts"], "names": [], "mappings": "AAAA,wBAAwB;AACxB;;GAEG;AACH,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,MAAM,CAAC,MAAM,kBAAkB,GAAY,KAAK,CAAC;AAEjD,MAAM,CAAC,MAAM,gBAAgB,GAAU,EAAE,CAAC;AAE1C,KAAK,UAAU,2BAA2B;AACxC,oCAAoC;AACpC,KAAyD;IAEzD,8DAA8D;IAC9D,MAAM,cAAc,GAAG,CAAC,EAA4B,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAE5F,MAAM,oBAAoB,GAAG,CAAC,UAAmC,EAAE,EAAE;QACnE,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC;QAEhC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QAC7B,CAAC;aAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC1D,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,MAAM,OAAO,GAAU,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;IACnD,gHAAgH;IAChH,gBAAgB,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC3D,CAAC;IAEF,IAAI,WAAW,GAAG,IAAI,CAAC;IACvB,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACzB,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC5B,WAAW,GAAG,MAAM,CAAC,EAAE,CAAC;QAC1B,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACnC,WAAW,GAAG,MAAM,CAAC,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,8GAA8G;IAC9G,4HAA4H;IAC5H,MAAM,aAAa,GAAG,oBAAoB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACnE,IAAI,aAAa,EAAE,CAAC;QAClB,WAAW,GAAG,aAAa,CAAC;IAC9B,CAAC;IAED,8GAA8G;IAC9G,4HAA4H;IAC5H,MAAM,aAAa,GAAG,oBAAoB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACnE,IAAI,aAAa,EAAE,CAAC;QAClB,WAAW,GAAG,aAAa,CAAC;IAC9B,CAAC;IAED,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,KAAK,UAAU,mBAAmB,CAChC,OAAgB,EAChB,gBAAkD,EAClD,gBAAkD;IAElD,MAAM,WAAW,GAA2B;QAC1C,KAAK,EAAE,OAAO,gBAAgB,KAAK,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI;KACzE,CAAC;IAEF,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,WAAW,CAAC,KAAK,GAAG,OAAO,gBAAgB,KAAK,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC;IACxF,CAAC;IAED,OAAO,MAAM,oBAAoB,CAAC,WAAW,CAAC,CAAC;AACjD,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,qBAAqB;AACzC,oCAAoC;AACpC,KAAmC,EACnC,UAAmB,IAAI;IAEvB,IAAI,eAAe,EAAE,EAAE,CAAC;QACtB,OAAO,MAAM,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IACtE,CAAC;IACD,8GAA8G;IAC9G,4HAA4H;IAC5H,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,2BAA2B,CAAC,KAAY,CAAC,CAAC;IACvE,OAAO,MAAM,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1D,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,WAAmC,EACnC,oBAA6B,KAAK;IAElC,IAAI,CAAC;QACH,OAAO,MAAM,iBAAiB,CAAC;YAC7B,GAAG,WAAW;YACd,KAAK,EAAE,iBAAiB,IAAI,WAAW,CAAC,KAAK;SAC9C,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IACE,CAAC,iBAAiB;YAClB,OAAO,KAAK,KAAK,QAAQ;YACzB,KAAK,EAAE,IAAI,KAAK,6BAA6B,EAC7C,CAAC;YACD,OAAO,MAAM,oBAAoB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACvD,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,WAAmC;IACzE,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;QAClE,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;IAC1D,CAAC;IAED,MAAM,aAAa,GACjB,SAAS,CAAC,iBAAiB,CAAC;QAC5B,SAAS,CAAC,oBAAoB,CAAC;QAC/B,4DAA4D;QAC5D,SAAS,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACrC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAC5D,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,eAAe;IAC7B,kHAAkH;IAClH,OAAO;IACL,MAAM;IACN,QAAQ,CAAC,cAAc;QACvB,4BAA4B;QAC5B,CAAC,CAAC,CACA,CAAC,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;YAC/D,SAAS,CAAC,iBAAiB,CAAC;YAC5B,SAAS,CAAC,oBAAoB,CAAC;YAC/B,4DAA4D;YAC5D,SAAS,CAAC,gBAAgB,CAAC,CAC5B,CACF,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,2BAA2B,CAC/C,OAA2B;IAE3B,OAAO,MAAM,kBAAkB,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAClF,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,0BAA0B,CAC9C,OAA2B;IAE3B,OAAO,MAAM,kBAAkB,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;AAC5E,CAAC;AAED,KAAK,UAAU,kBAAkB,CAC/B,MAAgB,EAChB,IAAY,EACZ,OAA2B;IAE3B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;IAC5D,CAAC;IACD,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;IAC/D,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CACnE,CAAC;IACF,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;QAC5C,IAAI,CAAC,CAAC,iBAAiB,IAAI,MAAM,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAI,MAAc,CAAC,eAAe,EAAE,CAAC;QACvD,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,EAAE,QAAQ,IAAI,SAAS,EAAE,QAAQ,IAAI,IAAI,CAAC;AAC5D,CAAC", "sourcesContent": ["/* eslint-env browser */\n/**\n * A web-only module for ponyfilling the UserMedia API.\n */\nimport { Platform } from 'expo-modules-core';\n\nexport const userMediaRequested: boolean = false;\n\nexport const mountedInstances: any[] = [];\n\nasync function requestLegacyUserMediaAsync(\n  // TODO(@kitten): Type this properly\n  props: { audioConstraints?: any; videoConstraints?: any }\n): Promise<any[]> {\n  // TODO(@kitten): This is never type checked against DOM types\n  const optionalSource = (id: string | string[] | null) => ({ optional: [{ sourceId: id }] });\n\n  const constraintToSourceId = (constraint: MediaTrackConstraintSet) => {\n    const { deviceId } = constraint;\n\n    if (typeof deviceId === 'string') {\n      return deviceId;\n    }\n\n    if (Array.isArray(deviceId)) {\n      return deviceId[0] ?? null;\n    } else if (typeof deviceId === 'object' && deviceId.ideal) {\n      return deviceId.ideal;\n    }\n\n    return null;\n  };\n\n  const sources: any[] = await new Promise((resolve) =>\n    // @ts-ignore: https://caniuse.com/#search=getSources Chrome for Android (78) & Samsung Internet (10.1) use this\n    MediaStreamTrack.getSources((sources) => resolve(sources))\n  );\n\n  let audioSource = null;\n  let videoSource = null;\n\n  sources.forEach((source) => {\n    if (source.kind === 'audio') {\n      audioSource = source.id;\n    } else if (source.kind === 'video') {\n      videoSource = source.id;\n    }\n  });\n\n  // NOTE(@kitten): This doesn't seem right. The types that should be used here don't contain `audioConstraints`\n  // If this is legacy, the type shouldn't have been dropped but marked as `@deprecated`. Alternatively, remove this code path\n  const audioSourceId = constraintToSourceId(props.audioConstraints);\n  if (audioSourceId) {\n    audioSource = audioSourceId;\n  }\n\n  // NOTE(@kitten): This doesn't seem right. The types that should be used here don't contain `videoConstraints`\n  // If this is legacy, the type shouldn't have been dropped but marked as `@deprecated`. Alternatively, remove this code path\n  const videoSourceId = constraintToSourceId(props.videoConstraints);\n  if (videoSourceId) {\n    videoSource = videoSourceId;\n  }\n\n  return [optionalSource(audioSource), optionalSource(videoSource)];\n}\n\nasync function sourceSelectedAsync(\n  isMuted: boolean,\n  audioConstraints?: MediaTrackConstraints | boolean,\n  videoConstraints?: MediaTrackConstraints | boolean\n): Promise<MediaStream> {\n  const constraints: MediaStreamConstraints = {\n    video: typeof videoConstraints !== 'undefined' ? videoConstraints : true,\n  };\n\n  if (!isMuted) {\n    constraints.audio = typeof audioConstraints !== 'undefined' ? audioConstraints : true;\n  }\n\n  return await getAnyUserMediaAsync(constraints);\n}\n\nexport async function requestUserMediaAsync(\n  // TODO(@kitten): Type this properly\n  props: { audio?: any; video?: any },\n  isMuted: boolean = true\n): Promise<MediaStream> {\n  if (canGetUserMedia()) {\n    return await sourceSelectedAsync(isMuted, props.audio, props.video);\n  }\n  // NOTE(@kitten): This doesn't seem right. The types that should be used here don't contain `videoConstraints`\n  // If this is legacy, the type shouldn't have been dropped but marked as `@deprecated`. Alternatively, remove this code path\n  const [audio, video] = await requestLegacyUserMediaAsync(props as any);\n  return await sourceSelectedAsync(isMuted, audio, video);\n}\n\nexport async function getAnyUserMediaAsync(\n  constraints: MediaStreamConstraints,\n  ignoreConstraints: boolean = false\n): Promise<MediaStream> {\n  try {\n    return await getUserMediaAsync({\n      ...constraints,\n      video: ignoreConstraints || constraints.video,\n    });\n  } catch (error: any) {\n    if (\n      !ignoreConstraints &&\n      typeof error === 'object' &&\n      error?.name === 'ConstraintNotSatisfiedError'\n    ) {\n      return await getAnyUserMediaAsync(constraints, true);\n    }\n    throw error;\n  }\n}\n\nexport async function getUserMediaAsync(constraints: MediaStreamConstraints): Promise<MediaStream> {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n\n  const _getUserMedia =\n    navigator['mozGetUserMedia'] ||\n    navigator['webkitGetUserMedia'] ||\n    // @ts-expect-error: TODO(@kitten): Remove / Drop IE support\n    navigator['msGetUserMedia'];\n  return new Promise((resolve, reject) =>\n    _getUserMedia.call(navigator, constraints, resolve, reject)\n  );\n}\n\nexport function canGetUserMedia(): boolean {\n  // TODO(@kitten): This is misaligned with the implementations in `expo-audio/src/AudioModule.web.ts` and `expo-av`\n  return (\n    // SSR\n    Platform.isDOMAvailable &&\n    // Has any form of media API\n    !!(\n      (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) ||\n      navigator['mozGetUserMedia'] ||\n      navigator['webkitGetUserMedia'] ||\n      // @ts-expect-error: TODO(@kitten): Remove / Drop IE support\n      navigator['msGetUserMedia']\n    )\n  );\n}\n\nexport async function isFrontCameraAvailableAsync(\n  devices?: MediaDeviceInfo[]\n): Promise<null | string> {\n  return await supportsCameraType(['front', 'user', 'facetime'], 'user', devices);\n}\n\nexport async function isBackCameraAvailableAsync(\n  devices?: MediaDeviceInfo[]\n): Promise<null | string> {\n  return await supportsCameraType(['back', 'rear'], 'environment', devices);\n}\n\nasync function supportsCameraType(\n  labels: string[],\n  type: string,\n  devices?: MediaDeviceInfo[]\n): Promise<null | string> {\n  if (!devices) {\n    if (!navigator.mediaDevices.enumerateDevices) {\n      return null;\n    }\n    devices = await navigator.mediaDevices.enumerateDevices();\n  }\n  const cameras = devices.filter((t) => t.kind === 'videoinput');\n  const [hasCamera] = cameras.filter((camera) =>\n    labels.some((label) => camera.label.toLowerCase().includes(label))\n  );\n  const [isCapable] = cameras.filter((camera) => {\n    if (!('getCapabilities' in camera)) {\n      return null;\n    }\n\n    const capabilities = (camera as any).getCapabilities();\n    if (!capabilities.facingMode) {\n      return null;\n    }\n\n    return capabilities.facingMode.find((_: string) => type);\n  });\n\n  return isCapable?.deviceId || hasCamera?.deviceId || null;\n}\n"]}