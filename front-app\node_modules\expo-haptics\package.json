{"name": "expo-haptics", "version": "14.1.4", "description": "Provides access to the system's haptics engine on iOS, vibration effects on Android, and Web Vibration API on web.", "main": "src/Haptics.ts", "types": "build/Haptics.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-haptics", "haptics"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-haptics"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/haptics/", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}