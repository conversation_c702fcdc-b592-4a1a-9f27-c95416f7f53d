### **钓鱼生涯记录APP - MVP开发需求文档 (v1.0)**

**1. 引言与愿景 (Introduction & Vision)**

* **1.1 产品愿景 (Product Vision):**
    我们致力于打造一款以AI技术为驱动、以游戏化收集为核心乐趣的次世代钓鱼日志应用。它不仅是一个记录工具，更是一个能让钓鱼爱好者沉浸其中，不断探索、解锁和分享个人成就的“钓鱼伙伴”。

* **1.2 目标用户 (Target Audience):**
    * **核心用户:** 20-40岁，熟悉智能手机操作，乐于尝试新鲜事物，享受游戏化体验（如收集、打卡、升级）的现代钓鱼爱好者。
    * **次要用户:** 所有希望通过简单、智能的方式来记录和回顾自己钓鱼生涯的钓友。

* **1.3 MVP核心目标 (Core MVP Goal):**
    验证产品的核心价值主张：用户是否愿意并喜爱使用 **“AI识别 -> 自动记录 -> 解锁图鉴”** 这一核心功能循环。通过MVP版本，我们将重点衡量用户对这一创新体验的接受度、参与度和留存率。

**2. 核心功能与用户故事 (Core Features & User Stories)**

**功能模块一：用户账户与个人档案 (User Onboarding & Profile)**

* **用户故事 1.1 (注册):**
    * **作为** 一名新用户，**我希望** 能通过手机号/邮箱或第三方社交账户（微信/Apple ID）快速完成注册，**以便** 我能立即开始使用APP。
* **用户故事 1.2 (创建个人档案):**
    * **作为** 一名用户，**我希望** 能设置我的昵称和上传个人头像，**以便** 在排行榜等功能中展示我的个性化身份。
* **用户故事 1.3 (查看个人档案):**
    * **作为** 一名用户，**我希望** 在我的个人页面上能看到核心成就的概览，如“累计渔获数量”和“已解锁鱼种”，**以便** 快速了解我的钓鱼生涯摘要。

**功能模块二：AI渔获日志 (AI-Powered Catch Logging) - [核心]**

* **用户故事 2.1 (开始记录):**
    * **作为** 一名钓鱼者，**我希望** 在APP的首页能看到一个显眼的“记录渔获”按钮，**以便** 我能随时随地快速开始记录。
* **用户故事 2.2 (上传/拍摄照片):**
    * **作为** 一名用户，**我希望** 能选择“立即拍照”或“从相册上传”我的渔获照片，**以便** 将渔获的影像作为核心记录。
* **用户故事 2.3 (AI处理与反馈):**
    * **作为** 一名用户，**我希望** 在上传照片后，系统能自动进行处理，并清晰地展示AI识别出的鱼种和提取出的鱼体（抠图），**以便** 我能直观地看到AI的工作结果。
* **用户故事 2.4 (确认/修正识别结果):**
    * **作为** 一名用户，**我希望** 系统能提供一个首选的识别结果和几个备选项，我可以轻松点击“确认”或选择正确的鱼种进行修正，**以便** 保证日志的准确性。
* **用户故事 2.5 (数据自动填充):**
    * **作为** 一名用户，**我希望** 在我确认鱼种后，系统能自动记录当前的日期、时间、GPS位置，并关联当时的天气信息，**以便** 减少我的手动输入，提升记录效率。
* **用户故事 2.6 (手动补充信息):**
    * **作为** 一名用户，**我希望** 能有选项让我手动输入鱼的长度、重量、使用的钓组/饵料以及一段文字备注，**以便** 我能记录更丰富的钓鱼细节。
* **用户故事 2.7 (保存与完成):**
    * **作为** 一名用户，**我希望** 点击保存后，系统能快速生成一条图文并茂的完整日志，并给予我积极的反馈（如“记录成功！”），**以便** 我能获得完成记录的满足感。

**功能模块三：“鱼种图鉴” (The "Fishdex") - [游戏化核心]**

* **用户故事 3.1 (访问图鉴):**
    * **作为** 一名用户，**我希望** 能在主菜单中轻松找到并进入我的“鱼种图鉴”，**以便** 查看我的收集进度。
* **用户故事 3.2 (查看图鉴状态):**
    * **作为** 一名用户，**我希望** 在图鉴中能清晰地看到哪些鱼种是我已经解锁的（彩色点亮），哪些是还未解锁的（灰色剪影），**以便** 激发我的收集欲望。
* **用户故事 3.3 (查看已解锁鱼种):**
    * **作为** 一名用户，**我希望** 点击一个已点亮的鱼种图标，就能看到我所有钓获该鱼种的历史记录列表，**以便** 我回顾和分析特定鱼种的垂钓经历。
* **用户故事 3.4 (解锁新鱼种):**
    * **作为** 一名用户，**我希望** 当我第一次成功记录一个新鱼种时，能看到一个令人兴奋的“新鱼种解锁！”动画或提示，**以便** 给我强烈的成就感和惊喜感。

**功能模块四：地区排行榜 (Regional Leaderboard)**

* **用户故事 4.1 (访问排行榜):**
    * **作为** 一名用户，**我希望** 能在APP内访问排行榜，**以便** 了解我在本地区钓友中的表现。
* **用户故事 4.2 (自动区域匹配):**
    * **作为** 一名用户，**我希望** 系统能根据我的渔获记录位置，自动将我归入正确的地区榜单（如“广东省榜”），**以便** 我能和身边的钓友进行比较。
* **用户故事 4.3 (查看排名):**
    * **作为** 一名用户，**我希望** 能在排行榜上看到基于一个简单维度的排名（MVP阶段定为 **“月度最重渔获”**），并能看到上榜用户的昵称、鱼种和重量，**以便** 了解竞争情况并激励我钓上更大的鱼。

**3. 技术栈与关键需求 (Tech Stack & Key Requirements)**

* **3.1 移动端 (Frontend):**
    * 使用 **React Native** 进行跨平台开发，以降低MVP阶段的开发成本和时间。
* **3.2 后端 (Backend):**
    * 使用 **Go** 语言搭配 **Gin** 框架，搭配gorm等库，TDD测试，优雅的项目结构，实现高并发、低延迟的API服务。
* **3.3 数据库 (Database):**
    * 使用 **PostgreSQL** (配合PostGIS处理地理位置数据)。
* **3.4 AI与第三方服务 (AI & 3rd-Party Services):**
    * **鱼种识别:** 自行部署基于 **Fishial.ai** 的开源模型。
    * **鱼体抠图:** 调用 **Clipdrop.co** 的背景移除API。
    * **地图服务:** 在日志详情页显示位置时，可使用 **高德地图SDK/Mapbox**。
    * **天气服务:** 调用免费天气API，如 **和风天气** 或 **OpenWeatherMap**。
* **3.5 非功能性需求 (Non-Functional Requirements):**
    * **性能:** 图片上传后的AI处理（识别+抠图）过程，应在5-8秒内向用户返回结果。
    * **易用性:** 核心的“记录渔获”流程，应能在5步以内、60秒内完成。
    * **数据隐私:** 用户的GPS位置数据必须加密存储，未经用户本人许可，绝不能在任何公共界面（如排行榜、社交动态）中泄露精确钓点。
    * **稳定性:** APP核心功能在主流机型上的闪退率应低于0.5%。

**4. MVP成功度量指标 (MVP Success Metrics)**

* **4.1 用户激活率 (Activation Rate):**
    * **指标:** 新用户在注册后7天内，成功完成至少一次“AI渔获记录”的比例。
    * **目标:** > 40%。
* **4.2 用户留存率 (Retention Rate):**
    * **指标:** 次日留存率、7日留存率。
    * **目标:** 次日 > 30%，7日 > 15%。
* **4.3 核心功能参与度 (Feature Engagement):**
    * **指标:** 活跃用户的平均每周记录渔获次数。
    * **目标:** > 1.5次/周/活跃用户。
* **4.4 AI模型表现:**
    * **指标:** AI识别结果被用户直接“确认”的比例（即用户无需手动修正）。
    * **目标:** > 75%。