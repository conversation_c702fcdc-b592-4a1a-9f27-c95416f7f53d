import React, { useState, useEffect } from 'react'
import { 
  View, 
  Text, 
  StyleSheet, 
  Modal, 
  Pressable, 
  Image, 
  ScrollView,
  Dimensions
} from 'react-native'
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withTiming,
  runOnJS
} from 'react-native-reanimated'
import { Ionicons } from '@expo/vector-icons'
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights } from '../../constants/colors'

interface PhotoModalProps {
  isOpen: boolean
  photos: string[]
  currentIndex: number
  onClose: () => void
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window')
const AnimatedPressable = Animated.createAnimatedComponent(Pressable)

export function PhotoModal({ isOpen, photos, currentIndex, onClose }: PhotoModalProps) {
  const [activeIndex, setActiveIndex] = useState(currentIndex)
  const opacity = useSharedValue(0)
  const scale = useSharedValue(0.8)

  useEffect(() => {
    if (isOpen) {
      opacity.value = withTiming(1, { duration: 300 })
      scale.value = withSpring(1, { damping: 20, stiffness: 300 })
    } else {
      opacity.value = withTiming(0, { duration: 200 })
      scale.value = withTiming(0.8, { duration: 200 })
    }
  }, [isOpen])

  useEffect(() => {
    setActiveIndex(currentIndex)
  }, [currentIndex])

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }]
  }))

  const handleClose = () => {
    opacity.value = withTiming(0, { duration: 200 })
    scale.value = withTiming(0.8, { duration: 200 }, () => {
      runOnJS(onClose)()
    })
  }

  const handlePrevious = () => {
    if (activeIndex > 0) {
      setActiveIndex(activeIndex - 1)
    }
  }

  const handleNext = () => {
    if (activeIndex < photos.length - 1) {
      setActiveIndex(activeIndex + 1)
    }
  }

  const handleThumbnailPress = (index: number) => {
    setActiveIndex(index)
  }

  if (!isOpen || photos.length === 0) {
    return null
  }

  return (
    <Modal
      visible={isOpen}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <Animated.View style={[styles.container, modalAnimatedStyle]}>
          {/* 关闭按钮 */}
          <AnimatedPressable style={styles.closeButton} onPress={handleClose}>
            <Ionicons name="close" size={24} color={Colors.cardBackground} />
          </AnimatedPressable>

          {/* 主图片 */}
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: photos[activeIndex] }} 
              style={styles.mainImage}
              resizeMode="contain"
            />
            
            {/* 导航按钮 */}
            {photos.length > 1 && (
              <>
                {activeIndex > 0 && (
                  <AnimatedPressable 
                    style={[styles.navButton, styles.prevButton]} 
                    onPress={handlePrevious}
                  >
                    <Ionicons name="chevron-back" size={24} color={Colors.cardBackground} />
                  </AnimatedPressable>
                )}
                
                {activeIndex < photos.length - 1 && (
                  <AnimatedPressable 
                    style={[styles.navButton, styles.nextButton]} 
                    onPress={handleNext}
                  >
                    <Ionicons name="chevron-forward" size={24} color={Colors.cardBackground} />
                  </AnimatedPressable>
                )}
              </>
            )}
          </View>

          {/* 图片计数 */}
          {photos.length > 1 && (
            <Text style={styles.counter}>
              {activeIndex + 1} / {photos.length}
            </Text>
          )}

          {/* 缩略图列表 */}
          {photos.length > 1 && (
            <ScrollView 
              horizontal 
              style={styles.thumbnailContainer}
              contentContainerStyle={styles.thumbnailContent}
              showsHorizontalScrollIndicator={false}
            >
              {photos.map((photo, index) => (
                <Pressable
                  key={index}
                  style={[
                    styles.thumbnail,
                    index === activeIndex && styles.activeThumbnail
                  ]}
                  onPress={() => handleThumbnailPress(index)}
                >
                  <Image 
                    source={{ uri: photo }} 
                    style={styles.thumbnailImage}
                    resizeMode="cover"
                  />
                </Pressable>
              ))}
            </ScrollView>
          )}
        </Animated.View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  container: {
    width: screenWidth * 0.9,
    maxHeight: screenHeight * 0.8,
    backgroundColor: Colors.primaryText,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
  },
  
  closeButton: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.oceanBlueAlpha(0.7),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  
  imageContainer: {
    position: 'relative',
    height: screenHeight * 0.5,
    backgroundColor: Colors.primaryText,
  },
  
  mainImage: {
    width: '100%',
    height: '100%',
  },
  
  navButton: {
    position: 'absolute',
    top: '50%',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.oceanBlueAlpha(0.7),
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ translateY: -20 }],
  },
  
  prevButton: {
    left: Spacing.md,
  },
  
  nextButton: {
    right: Spacing.md,
  },
  
  counter: {
    textAlign: 'center',
    color: Colors.cardBackground,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    paddingVertical: Spacing.sm,
    backgroundColor: Colors.primaryText,
  },
  
  thumbnailContainer: {
    backgroundColor: Colors.primaryText,
    maxHeight: 80,
  },
  
  thumbnailContent: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    gap: Spacing.sm,
  },
  
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  
  activeThumbnail: {
    borderColor: Colors.sunriseGold,
  },
  
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
})
