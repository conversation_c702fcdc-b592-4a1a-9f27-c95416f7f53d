'use client'

import { motion } from 'framer-motion'
import { Check, Share2, Plus, Home, Book, Trophy, Calendar, MapPin, Cloud, Droplets, Save, Eye, Bookmark } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import { AnimatedButton } from '../common/AnimatedButton'
import type { PageType } from '../AppWrapper'

interface RecordSuccessPageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  recordData: any
  uploadedPhotos: Array<{url: string, type: string}>
  appState: any
}

export function RecordSuccessPage({ 
  navigateToPage, 
  showFishDetail, 
  openPhotoModal, 
  recordData, 
  uploadedPhotos 
}: RecordSuccessPageProps) {
  const confettiColors = ['#FFC759', '#00A79D', '#4F8EFF', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57']
  
  // 生成固定的随机值避免重新渲染时动画重置
  const generateFireworkParticles = () => {
    return Array.from({ length: 20 }).map((_, index) => {
      const angle = (index * 18) * (Math.PI / 180)
      const distance = 80 + (index % 3) * 20
      const endX = Math.cos(angle) * distance
      const endY = Math.sin(angle) * distance
      return { angle, distance, endX, endY, color: confettiColors[index % confettiColors.length] }
    })
  }
  
  const fireworkParticles = generateFireworkParticles()

  const shareRecord = () => {
    if (navigator.share) {
      navigator.share({
        title: '我的渔获日志',
        text: '今天钓到了一条漂亮的鲫鱼！',
        url: window.location.href
      })
    } else {
      alert('分享功能：已复制链接到剪贴板')
    }
  }

  const recommendedActions = [
    {
      icon: Book,
      title: '查看鱼种图鉴',
      description: '探索更多鱼种，完成收集',
      action: () => navigateToPage('fishdex'),
      color: 'bg-aqua-teal'
    },
    {
      icon: Trophy,
      title: '查看排行榜',
      description: '看看您在地区的排名',
      action: () => navigateToPage('leaderboard'),
      color: 'bg-sunrise-gold'
    }
  ]

  const fishDetails = {
    name: recordData?.fishName || '鲫鱼',
    scientificName: recordData?.scientificName || 'Crucian Carp',
    weight: recordData?.weight || 0.8,
    length: 25,
    image: uploadedPhotos?.[0]?.url || 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format'
  }

  const environmentInfo = {
    location: '东湖公园钓点',
    weather: '多云转晴 15°C',
    wind: '微风2级',
    waterTemp: '水温约12°C'
  }

  const tackleInfo = {
    rig: '3号主线 + 2号子线 + 4号钩',
    bait: '玉米粒、面包虫',
    notes: '今天天气不错，鱼情活跃，在水草边上钓获了这条漂亮的鲫鱼。使用玉米粒作饵效果很好，建议其他钓友也可以尝试这个钓点。'
  }

  return (
    <div className="pb-24">
      <TopNavigation
        title="记录完成"
        showBack
        onBack={() => navigateToPage('homepage')}
        rightIcon="share"
        onRightClick={shareRecord}
      />

      {/* 成功反馈动画 */}
      <motion.div 
        className="px-6 py-6"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="bg-white p-6 rounded-xl shadow-sm text-center relative overflow-hidden">
          {/* 烟花彩带动画 */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {/* 中心爆炸烟花 */}
            {fireworkParticles.map((particle, index) => (
              <motion.div
                key={`firework-${index}`}
                className="absolute w-1.5 h-1.5 rounded-full"
                style={{ 
                  backgroundColor: particle.color,
                  left: '50%',
                  top: '30%',
                  transformOrigin: 'center'
                }}
                animate={{
                  x: [0, particle.endX, particle.endX],
                  y: [0, particle.endY, particle.endY + 50],
                  scale: [0, 1, 0.3],
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: 1.5,
                  delay: 0.3 + index * 0.05,
                  ease: "easeOut"
                }}
              />
            ))}
            

          </div>

          <motion.div
            className="relative z-10"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 300 }}
          >
            <div className="w-24 h-24 mx-auto bg-gradient-sunrise rounded-full flex items-center justify-center mb-4">
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity, repeatDelay: 1 }}
              >
                <Check className="text-white" size={32} />
              </motion.div>
            </div>
          </motion.div>

          <motion.h2 
            className="text-2xl font-bold text-primary-text mb-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            🎉 记录成功！
          </motion.h2>
          <motion.p 
            className="text-lg text-secondary-text mb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            您的渔获已成功添加到日志
          </motion.p>
          <motion.div 
            className="flex items-center justify-center space-x-2 text-sm text-aqua-teal"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <Trophy size={16} />
            <span>获得经验值 +50</span>
            <span>•</span>
            <span>连续记录 +1</span>
          </motion.div>
        </div>
      </motion.div>

      {/* 生成的完整日志 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-4 border-b border-divider">
            <h3 className="text-lg font-bold text-primary-text mb-2">您的渔获日志</h3>
            <div className="text-sm text-secondary-text">
              <Calendar className="inline mr-1" size={14} />
              2024年1月15日 14:30
            </div>
          </div>

          <div className="p-4">
            {/* 原始照片展示 */}
            {uploadedPhotos && uploadedPhotos.length > 0 && (
              <motion.div 
                className="mb-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 }}
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-medium text-primary-text">📸 原始照片</h4>
                  <span className="text-xs text-secondary-text">{uploadedPhotos.length}张照片</span>
                </div>
                
                <div className="photo-gallery">
                  {/* 主要照片 */}
                  <motion.div 
                    className="main-photo mb-3 relative cursor-pointer"
                    whileHover={{ scale: 1.02 }}
                    onClick={() => openPhotoModal(uploadedPhotos.map(p => p.url), 0)}
                  >
                    <Image
                      src={uploadedPhotos[0].url}
                      alt="主要照片"
                      width={400}
                      height={300}
                      className="w-full h-48 rounded-xl object-cover border-2 border-aqua-teal"
                    />
                    <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      主要照片
                    </div>
                    <div className="absolute top-2 right-2 bg-white bg-opacity-20 text-white p-2 rounded-full">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </motion.div>
                  
                  {/* 缩略图列表 */}
                  <div className="grid grid-cols-4 gap-2">
                    {uploadedPhotos.map((photo, index) => (
                      <motion.div
                        key={index}
                        className="thumbnail-item relative cursor-pointer"
                        initial={{ opacity: 0, scale: 0.5 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 1.0 + index * 0.1 }}
                        whileHover={{ scale: 1.05 }}
                        onClick={() => openPhotoModal(uploadedPhotos.map(p => p.url), index)}
                      >
                        <Image
                          src={photo.url}
                          alt={`照片${index + 1}`}
                          width={80}
                          height={80}
                          className={`w-full aspect-square rounded-lg object-cover border-2 ${
                            index === 0 ? 'border-aqua-teal' : 'border-divider'
                          }`}
                        />
                        {index === 0 && (
                          <div className="absolute top-1 right-1 w-3 h-3 bg-aqua-teal rounded-full"></div>
                        )}
                      </motion.div>
                    ))}
                    
                    {uploadedPhotos.length < 4 && (
                      <motion.div
                        className="w-full aspect-square rounded-lg border-2 border-dashed border-divider bg-light-slate flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-colors"
                        onClick={() => navigateToPage('record')}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Plus className="text-secondary-text" size={20} />
                      </motion.div>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {/* 鱼种信息卡片 */}
            <motion.div 
              className="flex items-start space-x-4 mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1 }}
            >
              <div className="relative">
                <Image
                  src={fishDetails.image}
                  alt={fishDetails.name}
                  width={96}
                  height={96}
                  className="w-24 h-24 rounded-xl object-cover border-2 border-aqua-teal"
                />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </div>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className="text-xl font-bold text-primary-text">{fishDetails.name}</h4>
                  {recordData?.isNewSpecies && (
                    <span className="text-xs bg-aqua-teal text-white px-2 py-1 rounded-full">新解锁</span>
                  )}
                </div>
                <div className="text-sm text-secondary-text mb-2">{fishDetails.scientificName}</div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4 text-aqua-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-3m-3 3l-3-3" />
                    </svg>
                    <span>{fishDetails.weight} kg</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4 text-aqua-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707L16.414 6.414A1 1 0 0015.707 6H7a2 2 0 00-2 2v11a2 2 0 002 2z" />
                    </svg>
                    <span>{fishDetails.length} cm</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* 钓鱼详情 */}
            <motion.div 
              className="space-y-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
            >
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-light-slate p-3 rounded-lg">
                  <div className="text-xs text-secondary-text mb-1">钓组配置</div>
                  <div className="text-sm font-medium text-primary-text">{tackleInfo.rig}</div>
                </div>
                <div className="bg-light-slate p-3 rounded-lg">
                  <div className="text-xs text-secondary-text mb-1">使用饵料</div>
                  <div className="text-sm font-medium text-primary-text">{tackleInfo.bait}</div>
                </div>
              </div>

              {/* 环境信息 */}
              <div className="bg-gradient-to-r from-light-slate to-white p-4 rounded-lg border border-divider">
                <div className="text-sm font-medium text-primary-text mb-3">📍 钓鱼环境</div>
                <div className="grid grid-cols-2 gap-3 text-xs text-secondary-text">
                  <div className="flex items-center space-x-2">
                    <MapPin className="text-aqua-teal" size={14} />
                    <span>{environmentInfo.location}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Cloud className="text-aqua-teal" size={14} />
                    <span>{environmentInfo.weather}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-3.5 h-3.5 text-aqua-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10m0 0l-3-3m3 3l-3 3M9 12h2m0 0h2" />
                    </svg>
                    <span>{environmentInfo.wind}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Droplets className="text-aqua-teal" size={14} />
                    <span>{environmentInfo.waterTemp}</span>
                  </div>
                </div>
              </div>

              {/* 钓鱼心得 */}
              <div className="bg-white border border-divider p-4 rounded-lg">
                <div className="text-sm font-medium text-primary-text mb-2">💭 钓鱼心得</div>
                <div className="text-sm text-secondary-text leading-relaxed">
                  "{tackleInfo.notes}"
                </div>
              </div>

              {/* AI分析摘要 */}
              <div className="bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 p-4 rounded-lg border border-aqua-teal">
                <div className="flex items-center space-x-2 mb-2">
                  <svg className="w-4 h-4 text-aqua-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm font-medium text-primary-text">AI分析摘要</span>
                </div>
                <div className="text-xs text-secondary-text leading-relaxed">
                  本次渔获的鲫鱼体型适中，在当前季节和水温条件下属于正常表现。建议继续在相似环境下使用相同钓组和饵料配置，成功率较高。
                </div>
              </div>
            </motion.div>
          </div>

          {/* 底部操作区 */}
          <div className="p-4 bg-light-slate border-t border-divider">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4 text-sm text-secondary-text">
                <div className="flex items-center space-x-1">
                  <Eye size={14} />
                  <span>仅自己可见</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Bookmark size={14} />
                  <span>已保存</span>
                </div>
              </div>
              <div className="text-xs text-secondary-text">
                日志编号: #2024011501
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="grid grid-cols-2 gap-3">
              <AnimatedButton
                onClick={() => navigateToPage('record')}
                variant="secondary"
                icon={<Plus size={20} />}
              >
                继续记录
              </AnimatedButton>
              <AnimatedButton
                onClick={shareRecord}
                variant="primary"
                icon={<Share2 size={20} />}
              >
                分享日志
              </AnimatedButton>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 推荐下一步 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.3 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <h3 className="text-lg font-bold text-primary-text mb-4">🎯 推荐下一步</h3>
          <div className="space-y-3">
            {recommendedActions.map((action, index) => {
              const Icon = action.icon
              return (
                <motion.div
                  key={action.title}
                  className="flex items-center space-x-3 p-3 bg-light-slate rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.4 + index * 0.1 }}
                  whileHover={{ x: 4 }}
                  onClick={action.action}
                >
                  <div className={`w-10 h-10 ${action.color} bg-opacity-20 rounded-lg flex items-center justify-center`}>
                    <Icon className={action.color.replace('bg-', 'text-')} size={20} />
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-primary-text">{action.title}</div>
                    <div className="text-xs text-secondary-text">{action.description}</div>
                  </div>
                  <svg className="w-4 h-4 text-secondary-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </motion.div>
              )
            })}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
