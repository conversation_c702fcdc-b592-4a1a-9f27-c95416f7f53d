'use client'

import { motion } from 'framer-motion'
import { Download, Fish, Calendar, TrendingUp, MapPin, Award, Star, Plus } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import type { PageType } from '../AppWrapper'

interface CareerPageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  appState: any
}

export function CareerPage({ navigateToPage }: CareerPageProps) {
  const careerStats = {
    level: 15,
    experience: 75,
    title: '钓鱼大师',
    fishingAge: '2年',
    totalCatch: 128,
    speciesCount: 32,
    fishingDays: 85,
    spotsCount: 25
  }

  const monthlyStats = {
    catchCount: 15,
    fishingDays: 8,
    successRate: 75
  }

  const weeklyTrend = [
    { week: '1周', count: 3 },
    { week: '2周', count: 6 },
    { week: '3周', count: 8 },
    { week: '4周', count: 4 }
  ]

  const timelineData = [
    {
      month: '2024年1月',
      recordCount: 15,
      days: [
        {
          date: '1月15日',
          label: '今天',
          records: [
            {
              id: 1,
              fish: '鲫鱼',
              image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=50&h=50&fit=crop&auto=format',
              location: '东湖公园钓点',
              time: '14:30',
              weight: '0.8kg',
              length: '25cm',
              weather: '多云 15°C',
              points: '+50',
              isNew: true
            },
            {
              id: 2,
              fish: '草鱼',
              image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=50&h=50&fit=crop&auto=format',
              location: '西湖钓点',
              time: '09:15',
              weight: '1.2kg',
              length: '35cm',
              weather: '晴 18°C',
              points: '+35',
              isNew: false
            }
          ]
        },
        {
          date: '1月14日',
          label: '',
          records: [
            {
              id: 3,
              fish: '鲤鱼',
              image: 'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=50&h=50&fit=crop&auto=format',
              location: '北河钓场',
              time: '16:20',
              weight: '2.8kg',
              length: '45cm',
              weather: '阴 12°C',
              points: '+80',
              isRecord: true
            }
          ],
          hasMore: 2
        },
        {
          date: '1月12日',
          label: '',
          records: [
            {
              id: 4,
              fish: '鲫鱼',
              image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=50&h=50&fit=crop&auto=format',
              location: '南湖公园',
              time: '08:45',
              weight: '0.6kg',
              length: '22cm',
              weather: '小雨 10°C',
              points: '+25',
              isNew: false
            }
          ]
        }
      ]
    }
  ]

  const viewTimelineRecord = (recordId: number) => {
    alert(`查看时间线记录详情 #${recordId}`)
  }

  const expandTimelineDay = (dayIndex: number) => {
    alert(`展开第${dayIndex}天的更多记录`)
  }

  const exportCareerData = () => {
    alert('生涯数据导出功能：将生成PDF报告')
  }

  const maxWeeklyCount = Math.max(...weeklyTrend.map(w => w.count))

  return (
    <div className="pb-24">
      <TopNavigation
        title="我的生涯"
        showBack
        onBack={() => navigateToPage('profile')}
        rightIcon="download"
        onRightClick={exportCareerData}
      />

      {/* 生涯概览 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="bg-white p-6 rounded-xl shadow-sm">
          <div className="text-center mb-6">
            <motion.div
              className="w-20 h-20 mx-auto career-badge rounded-full flex items-center justify-center mb-3"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
            >
              <Fish className="text-white text-2xl" size={32} />
            </motion.div>
            <motion.h2 
              className="text-xl font-bold text-primary-text mb-1"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {careerStats.title}
            </motion.h2>
            <motion.p 
              className="text-sm text-secondary-text"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              钓龄 {careerStats.fishingAge} • 等级 {careerStats.level}
            </motion.p>
          </div>

          {/* 生涯统计 */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            {[
              { value: careerStats.totalCatch, label: '总钓获', color: 'text-aqua-teal' },
              { value: careerStats.speciesCount, label: '鱼种数', color: 'text-sunrise-gold' },
              { value: careerStats.fishingDays, label: '钓鱼天数', color: 'text-primary-text' },
              { value: careerStats.spotsCount, label: '钓点数', color: 'text-aqua-teal' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center p-3 bg-light-slate rounded-lg"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 + index * 0.1 }}
              >
                <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
                <div className="text-xs text-secondary-text">{stat.label}</div>
              </motion.div>
            ))}
          </div>

          {/* 生涯进度 */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-secondary-text">下一等级进度</span>
              <span className="text-sm font-medium text-primary-text">{careerStats.experience}%</span>
            </div>
            <div className="w-full bg-light-slate rounded-full h-3">
              <motion.div 
                className="bg-aqua-teal h-3 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${careerStats.experience}%` }}
                transition={{ duration: 1, delay: 1.0 }}
              />
            </div>
            <div className="text-xs text-secondary-text mt-1">
              还需 12 次钓获升级到 {careerStats.level + 1} 级
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* 月度回顾 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <h3 className="text-lg font-bold text-primary-text mb-4">📊 本月回顾</h3>
          <div className="grid grid-cols-3 gap-3 mb-4">
            {[
              { value: monthlyStats.catchCount, label: '本月钓获', color: 'text-aqua-teal' },
              { value: monthlyStats.fishingDays, label: '出钓天数', color: 'text-sunrise-gold' },
              { value: `${monthlyStats.successRate}%`, label: '成功率', color: 'text-primary-text' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 + index * 0.1 }}
              >
                <div className={`text-xl font-bold ${stat.color}`}>{stat.value}</div>
                <div className="text-xs text-secondary-text">{stat.label}</div>
              </motion.div>
            ))}
          </div>
          
          {/* 月度趋势图 */}
          <motion.div 
            className="relative h-16 bg-light-slate rounded-lg p-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
          >
            <div className="flex items-end justify-between h-full">
              {weeklyTrend.map((data, index) => {
                const height = (data.count / maxWeeklyCount) * 100
                const isMax = data.count === maxWeeklyCount
                return (
                  <motion.div
                    key={index}
                    className="flex flex-col items-center"
                    initial={{ height: 0 }}
                    animate={{ height: "auto" }}
                    transition={{ delay: 1.1 + index * 0.1 }}
                  >
                    <motion.div
                      className={`w-4 rounded-t ${isMax ? 'bg-sunrise-gold' : 'bg-aqua-teal'}`}
                      style={{ height: `${height}%` }}
                      initial={{ height: 0 }}
                      animate={{ height: `${height}%` }}
                      transition={{ delay: 1.1 + index * 0.1, duration: 0.5 }}
                    />
                    <span className="text-xs text-secondary-text mt-1">{data.week}</span>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* 时间线记录 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-4 border-b border-divider">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-primary-text">🕒 时间线</h3>
              <div className="flex items-center space-x-2">
                <button className="text-xs bg-aqua-teal text-white px-3 py-1 rounded-full">全部</button>
                <button className="text-xs text-secondary-text px-3 py-1 rounded-full border border-divider">本月</button>
              </div>
            </div>
          </div>

          {/* 时间线内容 */}
          <div className="p-4">
            {timelineData.map((month, monthIndex) => (
              <motion.div 
                key={month.month}
                className="timeline-month mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 + monthIndex * 0.1 }}
              >
                <div className="flex items-center mb-4">
                  <div className="w-3 h-3 bg-sunrise-gold rounded-full mr-3"></div>
                  <h4 className="text-lg font-bold text-primary-text">{month.month}</h4>
                  <div className="flex-1 h-px bg-divider mx-3"></div>
                  <span className="text-sm text-secondary-text">{month.recordCount}条记录</span>
                </div>

                {/* 时间线记录项 */}
                <div className="ml-6 space-y-4">
                  {month.days.map((day, dayIndex) => (
                    <motion.div 
                      key={dayIndex}
                      className="timeline-day"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 1.0 + dayIndex * 0.1 }}
                    >
                      <div className="flex items-center mb-2">
                        <div className="w-2 h-2 bg-aqua-teal rounded-full mr-3 -ml-4"></div>
                        <span className="text-sm font-medium text-primary-text">
                          {day.date} {day.label}
                        </span>
                        <div className="flex-1"></div>
                        <span className="text-xs text-secondary-text">{day.records.length}条记录</span>
                      </div>
                      
                      {/* 记录卡片 */}
                      <div className="ml-2 space-y-3">
                        {day.records.map((record, recordIndex) => (
                          <motion.div
                            key={record.id}
                            className="timeline-record bg-light-slate p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 1.1 + recordIndex * 0.05 }}
                            whileHover={{ x: 4 }}
                            onClick={() => viewTimelineRecord(record.id)}
                          >
                            <div className="flex items-start space-x-3">
                              <Image
                                src={record.image}
                                alt={record.fish}
                                width={40}
                                height={40}
                                className="rounded-lg object-cover"
                              />
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="text-sm font-medium text-primary-text">{record.fish}</span>
                                  {record.isNew && (
                                    <span className="text-xs bg-aqua-teal text-white px-2 py-1 rounded">新解锁</span>
                                  )}
                                  {record.isRecord && (
                                    <span className="text-xs bg-sunrise-gold text-white px-2 py-1 rounded">最大</span>
                                  )}
                                </div>
                                <div className="text-xs text-secondary-text mb-1">{record.location} • {record.time}</div>
                                <div className="flex items-center space-x-3 text-xs text-secondary-text">
                                  <span>{record.weight}</span>
                                  <span>{record.length}</span>
                                  <span>{record.weather}</span>
                                </div>
                              </div>
                              <div className="text-xs text-sunrise-gold">{record.points}</div>
                            </div>
                          </motion.div>
                        ))}

                        {/* 展开更多 */}
                        {day.hasMore && (
                          <motion.div 
                            className="text-center py-2"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 1.3 }}
                          >
                            <motion.button 
                              className="text-xs text-aqua-teal flex items-center justify-center space-x-1"
                              onClick={() => expandTimelineDay(dayIndex)}
                              whileHover={{ scale: 1.05 }}
                            >
                              <span>展开其余 {day.hasMore} 条记录</span>
                              <Plus size={12} />
                            </motion.button>
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}

            {/* 加载更多 */}
            <motion.div 
              className="text-center py-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.4 }}
            >
              <motion.button 
                className="text-aqua-teal text-sm font-medium flex items-center justify-center space-x-1"
                whileHover={{ scale: 1.05 }}
              >
                <span>加载更多记录</span>
                <TrendingUp size={14} />
              </motion.button>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* 成就徽章 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
      >
        <div className="bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 p-4 rounded-xl border border-aqua-teal/20">
          <div className="flex items-center space-x-2 mb-3">
            <Award className="text-aqua-teal" size={20} />
            <span className="text-sm font-medium text-primary-text">本月成就</span>
          </div>
          <div className="grid grid-cols-3 gap-3">
            {[
              { icon: Fish, label: '鱼种收集者', achieved: true },
              { icon: Calendar, label: '连续记录', achieved: true },
              { icon: Star, label: '完美一周', achieved: false }
            ].map((achievement, index) => {
              const Icon = achievement.icon
              return (
                <motion.div
                  key={achievement.label}
                  className="text-center"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.1 + index * 0.1 }}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-1 ${
                    achievement.achieved 
                      ? 'bg-gradient-to-r from-sunrise-gold to-yellow-400' 
                      : 'bg-gray-200'
                  }`}>
                    <Icon 
                      className={achievement.achieved ? 'text-white' : 'text-gray-400'} 
                      size={14} 
                    />
                  </div>
                  <div className={`text-xs ${
                    achievement.achieved ? 'text-secondary-text' : 'text-gray-400'
                  }`}>
                    {achievement.label}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
