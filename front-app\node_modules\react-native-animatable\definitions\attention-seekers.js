export const bounce = {
  0: {
    translateY: 0,
  },
  0.2: {
    translateY: 0,
  },
  0.4: {
    translateY: -30,
  },
  0.43: {
    translateY: -30,
  },
  0.53: {
    translateY: 0,
  },
  0.7: {
    translateY: -15,
  },
  0.8: {
    translateY: 0,
  },
  0.9: {
    translateY: -4,
  },
  1: {
    translateY: 0,
  },
};

export const flash = {
  0: {
    opacity: 1,
  },
  0.25: {
    opacity: 0,
  },
  0.5: {
    opacity: 1,
  },
  0.75: {
    opacity: 0,
  },
  1: {
    opacity: 1,
  },
};

export const jello = {
  0: {
    skewX: '0deg',
    skewY: '0deg',
  },
  0.111: {
    skewX: '0deg',
    skewY: '0deg',
  },
  0.222: {
    skewX: '-12.5deg',
    skewY: '-12.5deg',
  },
  0.333: {
    skewX: '6.25deg',
    skewY: '6.25deg',
  },
  0.444: {
    skewX: '-3.125deg',
    skewY: '-3.125deg',
  },
  0.555: {
    skewX: '1.5625deg',
    skewY: '1.5625deg',
  },
  0.666: {
    skewX: '-0.78125deg',
    skewY: '-0.78125deg',
  },
  0.777: {
    skewX: '0.390625deg',
    skewY: '0.390625deg',
  },
  0.888: {
    skewX: '-0.1953125deg',
    skewY: '-0.1953125deg',
  },
  1: {
    skewX: '0deg',
    skewY: '0deg',
  },
};

export const pulse = {
  0: {
    scale: 1,
  },
  0.5: {
    scale: 1.05,
  },
  1: {
    scale: 1,
  },
};

export const rotate = {
  0: {
    rotate: '0deg',
  },
  0.25: {
    rotate: '90deg',
  },
  0.5: {
    rotate: '180deg',
  },
  0.75: {
    rotate: '270deg',
  },
  1: {
    rotate: '360deg',
  },
};

export const shake = {
  0: {
    translateX: 0,
  },
  0.1: {
    translateX: -10,
  },
  0.2: {
    translateX: 10,
  },
  0.3: {
    translateX: -10,
  },
  0.4: {
    translateX: 10,
  },
  0.5: {
    translateX: -10,
  },
  0.6: {
    translateX: 10,
  },
  0.7: {
    translateX: -10,
  },
  0.8: {
    translateX: 10,
  },
  0.9: {
    translateX: -10,
  },
  1: {
    translateX: 0,
  },
};

export const swing = {
  0: {
    rotate: '0deg',
  },
  0.2: {
    rotate: '15deg',
  },
  0.4: {
    rotate: '-10deg',
  },
  0.6: {
    rotate: '5deg',
  },
  0.8: {
    rotate: '-5deg',
  },
  1: {
    rotate: '0deg',
  },
};

export const rubberBand = {
  0: {
    scaleX: 1,
    scaleY: 1,
  },
  0.3: {
    scaleX: 1.25,
    scaleY: 0.75,
  },
  0.4: {
    scaleX: 0.75,
    scaleY: 1.25,
  },
  0.5: {
    scaleX: 1.15,
    scaleY: 0.85,
  },
  0.65: {
    scaleX: 0.95,
    scaleY: 1.05,
  },
  0.75: {
    scaleX: 1.05,
    scaleY: 0.95,
  },
  1: {
    scaleX: 1,
    scaleY: 1,
  },
};

export const tada = {
  0: {
    scale: 1,
    rotate: '0deg',
  },
  0.1: {
    scale: 0.9,
    rotate: '-3deg',
  },
  0.2: {
    scale: 0.9,
    rotate: '-3deg',
  },
  0.3: {
    scale: 1.1,
    rotate: '-3deg',
  },
  0.4: {
    rotate: '3deg',
  },
  0.5: {
    rotate: '-3deg',
  },
  0.6: {
    rotate: '3deg',
  },
  0.7: {
    rotate: '-3deg',
  },
  0.8: {
    rotate: '3deg',
  },
  0.9: {
    scale: 1.1,
    rotate: '3deg',
  },
  1: {
    scale: 1,
    rotate: '0deg',
  },
};

export const wobble = {
  0: {
    translateX: 0,
    rotate: '0deg',
  },
  0.15: {
    translateX: -25,
    rotate: '-5deg',
  },
  0.3: {
    translateX: 20,
    rotate: '3deg',
  },
  0.45: {
    translateX: -15,
    rotate: '-3deg',
  },
  0.6: {
    translateX: 10,
    rotate: '2deg',
  },
  0.75: {
    translateX: -5,
    rotate: '-1deg',
  },
  1: {
    translateX: 0,
    rotate: '0deg',
  },
};
