package expo.modules.haptics.arguments

data class HapticsVibrationType(
  val timings: <PERSON><PERSON><PERSON><PERSON>,
  val amplitudes: Int<PERSON><PERSON>y,
  val oldSDKPattern: LongArray
) {
  override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
    if (this === other) return true
    if (javaClass != other?.javaClass) return false

    other as HapticsVibrationType

    if (!timings.contentEquals(other.timings)) return false
    if (!amplitudes.contentEquals(other.amplitudes)) return false
    if (!oldSDKPattern.contentEquals(other.oldSDKPattern)) return false

    return true
  }

  override fun hashCode(): Int {
    var result = timings.contentHashCode()
    result = 31 * result + amplitudes.contentHashCode()
    result = 31 * result + oldSDKPattern.contentHashCode()
    return result
  }
}
