import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { TopNavigation } from '../layout/TopNavigation'
import { globalStyles } from '../../styles'
import { Colors, Spacing, FontSizes, FontWeights } from '../../constants/colors'
import type { NavigationProps } from '../../types'

interface RecordSuccessPageProps extends NavigationProps {
  recordData?: any
  uploadedPhotos: Array<{url: string, type: string}>
}

export function RecordSuccessPage({ 
  navigateToPage, 
  recordData, 
  uploadedPhotos 
}: RecordSuccessPageProps) {
  const handleBack = () => {
    navigateToPage('homepage')
  }

  return (
    <View style={globalStyles.container}>
      <TopNavigation 
        title="记录成功" 
        showBack 
        onBack={handleBack}
      />
      <View style={styles.content}>
        <Text style={styles.placeholder}>记录成功页开发中...</Text>
        <Text style={styles.description}>
          这里将展示记录成功的庆祝动画和详细信息
        </Text>
        {recordData && (
          <Text style={styles.description}>
            记录数据: {JSON.stringify(recordData)}
          </Text>
        )}
        <Text style={styles.description}>
          上传照片数量: {uploadedPhotos.length}
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
  },
  
  placeholder: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.primaryText,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  
  description: {
    fontSize: FontSizes.md,
    color: Colors.secondaryText,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: Spacing.sm,
  },
})
