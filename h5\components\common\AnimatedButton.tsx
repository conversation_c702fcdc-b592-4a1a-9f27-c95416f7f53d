'use client'

import { motion } from 'framer-motion'
import { ReactNode } from 'react'

interface AnimatedButtonProps {
  children: ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'success' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  className?: string
  icon?: ReactNode
}

export function AnimatedButton({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = '',
  icon
}: AnimatedButtonProps) {
  const baseClasses = "rounded-lg font-medium flex items-center justify-center space-x-2 transition-all"
  
  const variantClasses = {
    primary: "record-button text-ocean-blue",
    secondary: "bg-aqua-teal text-white",
    success: "bg-green-500 text-white",
    outline: "border-2 border-aqua-teal text-aqua-teal bg-transparent hover:bg-aqua-teal hover:text-white"
  }

  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  }

  const disabledClasses = disabled 
    ? "bg-gray-300 text-gray-500 cursor-not-allowed" 
    : ""

  return (
    <motion.button
      onClick={disabled ? undefined : onClick}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`}
      whileHover={disabled ? {} : { scale: 1.05 }}
      whileTap={disabled ? {} : { scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      disabled={disabled}
    >
      {icon && <span>{icon}</span>}
      <span>{children}</span>
    </motion.button>
  )
}
