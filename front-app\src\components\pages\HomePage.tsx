import React from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions,
  StyleSheet
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withDelay,
  withTiming,
  interpolate
} from 'react-native-reanimated';
import { Camera, Bell } from 'lucide-react-native';
import { AnimatedButton } from '../common/AnimatedButton';
import { colors, spacing, borderRadius, fontSize, fontWeight, shadows, gradients, globalStyles } from '../../constants/styles';
import type { PageType, UserStats, FishRecord, LeaderboardItem } from '../../types';

interface HomePageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  appState: any;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedView = Animated.createAnimatedComponent(View);
const { width } = Dimensions.get('window');

export function HomePage({ navigateToPage, showFishDetail, openPhotoModal }: HomePageProps) {
  const insets = useSafeAreaInsets();
  
  const headerOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-20);
  const statsOpacity = useSharedValue(0);
  const statsTranslateY = useSharedValue(20);
  const buttonOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(0.8);

  const userStats: UserStats[] = [
    { value: '128', label: '总渔获', color: colors.primaryText },
    { value: '32', label: '鱼种数', color: colors.aquaTeal },
    { value: '15', label: '本月记录', color: colors.sunriseGold }
  ];

  const recentRecords: FishRecord[] = [
    {
      id: 1,
      fishName: '鲫鱼',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop&auto=format',
      weight: '0.8kg',
      length: '25cm',
      time: '今天 14:30',
      location: '东湖钓点',
      isNew: true
    },
    {
      id: 2,
      fishName: '草鱼',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=60&h=60&fit=crop&auto=format',
      weight: '1.2kg',
      length: '35cm',
      time: '昨天 09:15',
      location: '西湖钓点',
      isNew: false
    }
  ];

  const leaderboard: LeaderboardItem[] = [
    {
      rank: 1,
      name: '钓鱼大师',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '草鱼',
      weight: '2.8kg'
    },
    {
      rank: 2,
      name: '江边老钓',
      avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '鲤鱼',
      weight: '2.1kg'
    }
  ];

  React.useEffect(() => {
    // 页面进入动画
    headerOpacity.value = withDelay(100, withTiming(1, { duration: 600 }));
    headerTranslateY.value = withDelay(100, withSpring(0));
    
    statsOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
    statsTranslateY.value = withDelay(200, withSpring(0));
    
    buttonOpacity.value = withDelay(500, withTiming(1, { duration: 600 }));
    buttonScale.value = withDelay(500, withSpring(1));
  }, []);

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const statsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: statsOpacity.value,
    transform: [{ translateY: statsTranslateY.value }],
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: buttonOpacity.value,
    transform: [{ scale: buttonScale.value }],
  }));

  return (
    <ScrollView 
      style={[styles.scrollView, { paddingTop: insets.top }]}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: insets.bottom + 100 }}
    >
      {/* 顶部导航 */}
      <AnimatedView style={headerAnimatedStyle}>
        <LinearGradient
          colors={gradients.ocean}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View style={styles.userInfo}>
              <TouchableOpacity>
                <Image
                  source={{
                    uri: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format'
                  }}
                  style={styles.avatar}
                />
              </TouchableOpacity>
              <View>
                <Text style={styles.userName}>渔友小明</Text>
                <Text style={styles.userExperience}>钓龄 2年</Text>
              </View>
            </View>
            <TouchableOpacity>
              <Bell size={20} color={colors.white} />
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </AnimatedView>

      {/* 快速统计 */}
      <AnimatedView style={[statsAnimatedStyle, styles.statsContainer]}>
        <View style={styles.statsRow}>
          {userStats.map((stat, index) => (
            <View
              key={stat.label}
              style={[styles.statCard, { marginHorizontal: spacing.xs }]}
            >
              <Text 
                style={[styles.statValue, { color: stat.color }]}
              >
                {stat.value}
              </Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>
      </AnimatedView>

      {/* 记录渔获按钮 */}
      <AnimatedView style={[buttonAnimatedStyle, styles.buttonContainer]}>
        <AnimatedButton
          onPress={() => navigateToPage('record')}
          variant="primary"
          size="lg"
          style={styles.recordButton}
          icon={<Camera size={24} color={colors.oceanBlue} />}
        >
          记录渔获
        </AnimatedButton>
      </AnimatedView>

      {/* 本周排行榜 */}
      <View style={styles.sectionContainer}>
        <View style={[globalStyles.card, styles.leaderboardCard]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>本周排行榜</Text>
            <TouchableOpacity onPress={() => navigateToPage('leaderboard')}>
              <Text style={styles.viewAllText}>查看全部</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.leaderboardList}>
            {leaderboard.map((item, index) => (
              <View key={item.rank} style={styles.leaderboardItem}>
                <View 
                  style={[
                    styles.rankBadge,
                    { backgroundColor: item.rank === 1 ? colors.sunriseGold : colors.secondaryText }
                  ]}
                >
                  <Text style={styles.rankText}>{item.rank}</Text>
                </View>
                <Image
                  source={{ uri: item.avatar }}
                  style={styles.leaderboardAvatar}
                />
                <View style={styles.leaderboardInfo}>
                  <Text style={styles.leaderboardName}>{item.name}</Text>
                  <Text style={styles.leaderboardFish}>{item.fish} {item.weight}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </View>

      {/* 最近记录 */}
      <View style={styles.sectionContainer}>
        <View style={[globalStyles.card, styles.recordsCard]}>
          <Text style={[styles.sectionTitle, { marginBottom: spacing.md }]}>最近记录</Text>
          <View>
            {recentRecords.map((record, index) => (
              <TouchableOpacity
                key={record.id}
                style={styles.recordItem}
                onPress={() => showFishDetail('crucian')}
              >
                <Image
                  source={{ uri: record.image }}
                  style={styles.recordImage}
                />
                <View style={styles.recordInfo}>
                  <View style={styles.recordHeader}>
                    <Text style={styles.recordName}>{record.fishName}</Text>
                    {record.isNew && (
                      <View style={styles.newBadge}>
                        <Text style={styles.newBadgeText}>新解锁</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.recordDetails}>重量: {record.weight} | 长度: {record.length}</Text>
                  <Text style={styles.recordMeta}>{record.time} | {record.location}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: colors.lightSlate,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.full,
    borderWidth: 2,
    borderColor: colors.sunriseGold,
  },
  userName: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.bold,
    color: colors.white,
  },
  userExperience: {
    fontSize: fontSize.sm,
    color: colors.white,
    opacity: 0.8,
  },
  statsContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: colors.white,
    padding: spacing.md,
    borderRadius: borderRadius.xl,
    flex: 1,
    alignItems: 'center',
    ...shadows.small,
  },
  statValue: {
    fontSize: fontSize.xxl,
    fontWeight: fontWeight.bold,
  },
  statLabel: {
    fontSize: fontSize.sm,
    color: colors.secondaryText,
  },
  buttonContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  recordButton: {
    width: '100%',
  },
  sectionContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.bold,
    color: colors.primaryText,
  },
  viewAllText: {
    color: colors.aquaTeal,
    fontSize: fontSize.sm,
  },
  leaderboardCard: {
    // Uses globalStyles.card
  },
  leaderboardList: {
    gap: spacing.md,
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  rankBadge: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rankText: {
    color: colors.white,
    fontWeight: fontWeight.bold,
    fontSize: fontSize.sm,
  },
  leaderboardAvatar: {
    width: 32,
    height: 32,
    borderRadius: borderRadius.full,
  },
  leaderboardInfo: {
    flex: 1,
  },
  leaderboardName: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    color: colors.primaryText,
  },
  leaderboardFish: {
    fontSize: fontSize.xs,
    color: colors.secondaryText,
  },
  recordsCard: {
    // Uses globalStyles.card
  },
  recordItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
    paddingVertical: spacing.sm,
  },
  recordImage: {
    width: 60,
    height: 60,
    borderRadius: borderRadius.md,
  },
  recordInfo: {
    flex: 1,
  },
  recordHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  recordName: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    color: colors.primaryText,
  },
  newBadge: {
    backgroundColor: colors.aquaTeal,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
  },
  newBadgeText: {
    fontSize: fontSize.xs,
    color: colors.white,
  },
  recordDetails: {
    fontSize: fontSize.xs,
    color: colors.secondaryText,
  },
  recordMeta: {
    fontSize: fontSize.xs,
    color: colors.secondaryText,
  },
});