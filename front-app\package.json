{"name": "front-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "expo": "~53.0.22", "expo-blur": "^14.1.5", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "lucide-react-native": "^0.541.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "babel-preset-expo": "~13.0.0", "typescript": "~5.8.3"}, "private": true}