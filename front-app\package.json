{"name": "front-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "expo": "~53.0.22", "expo-blur": "^14.1.5", "expo-camera": "^16.1.11", "expo-constants": "^17.1.7", "expo-device": "^7.1.4", "expo-font": "^13.3.2", "expo-haptics": "^14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "^17.1.7", "expo-status-bar": "~2.2.3", "lucide-react-native": "^0.541.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.28.0", "react-native-modal": "^14.0.0-rc.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "babel-preset-expo": "~13.0.0", "typescript": "~5.8.3"}, "private": true}