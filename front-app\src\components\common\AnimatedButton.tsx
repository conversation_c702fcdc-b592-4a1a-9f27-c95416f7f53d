import React, { ReactNode } from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  withSpring
} from 'react-native-reanimated';
import { colors, shadows, globalStyles, spacing, borderRadius, fontSize } from '../../constants/styles';

interface AnimatedButtonProps {
  children: ReactNode;
  onPress?: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  icon?: ReactNode;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export function AnimatedButton({
  children,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = '',
  icon
}: AnimatedButtonProps) {
  const scale = useSharedValue(1);
  
  const sizeStyles = {
    sm: { 
      paddingHorizontal: spacing.md, 
      paddingVertical: spacing.sm, 
      minHeight: 36 
    },
    md: { 
      paddingHorizontal: spacing.lg, 
      paddingVertical: spacing.md, 
      minHeight: 48 
    },
    lg: { 
      paddingHorizontal: spacing.xl, 
      paddingVertical: spacing.lg, 
      minHeight: 56 
    }
  };

  const textSizeStyles = {
    sm: { fontSize: fontSize.sm },
    md: { fontSize: fontSize.base },
    lg: { fontSize: fontSize.lg }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const renderButton = () => {
    const baseStyle = [
      globalStyles.buttonBase,
      { borderRadius: borderRadius.lg },
      sizeStyles[size]
    ];
    
    if (variant === 'primary') {
      return (
        <LinearGradient
          colors={[colors.sunriseGold, colors.sunriseGoldDark]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[baseStyle, shadows.recordButton]}
        >
          <View style={[globalStyles.flexRow, { alignItems: 'center', gap: spacing.sm }]}>
            {icon && <View>{icon}</View>}
            <Text style={[
              { 
                color: colors.oceanBlue, 
                fontWeight: '600' 
              },
              textSizeStyles[size]
            ]}>
              {children}
            </Text>
          </View>
        </LinearGradient>
      );
    }

    if (variant === 'secondary') {
      return (
        <View style={[baseStyle, { backgroundColor: colors.aquaTeal }]}>
          <View style={[globalStyles.flexRow, { alignItems: 'center', gap: spacing.sm }]}>
            {icon && <View>{icon}</View>}
            <Text style={[
              { 
                color: colors.white, 
                fontWeight: '600' 
              },
              textSizeStyles[size]
            ]}>
              {children}
            </Text>
          </View>
        </View>
      );
    }

    if (variant === 'outline') {
      return (
        <View style={[
          baseStyle, 
          { 
            backgroundColor: 'transparent',
            borderWidth: 2,
            borderColor: colors.aquaTeal
          }
        ]}>
          <View style={[globalStyles.flexRow, { alignItems: 'center', gap: spacing.sm }]}>
            {icon && <View>{icon}</View>}
            <Text style={[
              { 
                color: colors.aquaTeal, 
                fontWeight: '600' 
              },
              textSizeStyles[size]
            ]}>
              {children}
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={[baseStyle, { backgroundColor: colors.success }]}>
        <View style={[globalStyles.flexRow, { alignItems: 'center', gap: spacing.sm }]}>
          {icon && <View>{icon}</View>}
          <Text style={[
            { 
              color: colors.white, 
              fontWeight: '600' 
            },
            textSizeStyles[size]
          ]}>
            {children}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <AnimatedTouchableOpacity
      onPress={disabled ? undefined : onPress}
      onPressIn={disabled ? undefined : handlePressIn}
      onPressOut={disabled ? undefined : handlePressOut}
      style={animatedStyle}
      disabled={disabled}
      activeOpacity={0.8}
    >
      {renderButton()}
    </AnimatedTouchableOpacity>
  );
}