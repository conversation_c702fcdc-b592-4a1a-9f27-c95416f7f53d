'use client'

import { motion } from 'framer-motion'
import { Setting<PERSON>, Clock, MapPin, Share2, Crown, ChevronRight, Trophy, Fish, Calendar, Target } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import type { PageType } from '../AppWrapper'

interface ProfilePageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  appState: any
}

export function ProfilePage({ navigateToPage }: ProfilePageProps) {
  const userInfo = {
    name: '渔友小明',
    id: '1001',
    fishingAge: '2年',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face&auto=format',
    stats: {
      totalCatch: 128,
      speciesCount: 32,
      monthlyRank: 8
    }
  }

  const achievements = [
    {
      id: 'first-record',
      name: '初次记录',
      icon: Trophy,
      color: 'from-sunrise-gold to-yellow-400',
      unlocked: true
    },
    {
      id: 'species-collector',
      name: '鱼种收集者',
      icon: Fish,
      color: 'from-aqua-teal to-blue-400',
      unlocked: true
    },
    {
      id: 'daily-streak',
      name: '连续记录',
      icon: Calendar,
      color: 'from-purple-400 to-purple-600',
      unlocked: true
    },
    {
      id: 'monthly-champion',
      name: '月度冠军',
      icon: Crown,
      color: 'from-gray-200 to-gray-400',
      unlocked: false
    }
  ]

  const menuItems = [
    {
      id: 'career',
      icon: Clock,
      label: '我的生涯',
      description: '查看钓鱼生涯数据',
      page: 'career' as PageType
    },
    {
      id: 'fishing-spots',
      icon: MapPin,
      label: '钓点管理',
      description: '管理常用钓点',
      page: 'fishing-spots' as PageType
    },
    {
      id: 'share',
      icon: Share2,
      label: '分享记录',
      description: '分享到社交平台',
      action: () => alert('分享功能')
    },
    {
      id: 'premium',
      icon: Crown,
      label: '升级专业版',
      description: '解锁更多功能',
      badge: 'Pro',
      action: () => alert('升级专业版')
    },
    {
      id: 'settings',
      icon: Settings,
      label: '设置',
      description: '应用设置和偏好',
      action: () => alert('设置页面')
    }
  ]

  return (
    <div className="pb-24">
      <TopNavigation
        title="个人档案"
        rightIcon="settings"
        onRightClick={() => alert('设置')}
      />

      {/* 用户信息 */}
      <motion.div 
        className="px-6 py-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="bg-white p-6 rounded-xl shadow-sm text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
          >
            <Image
              src={userInfo.avatar}
              alt="头像"
              width={80}
              height={80}
              className="rounded-full mx-auto mb-4 border-4 border-sunrise-gold"
            />
          </motion.div>
          
          <motion.h2 
            className="text-xl font-bold text-primary-text mb-1"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {userInfo.name}
          </motion.h2>
          <motion.p 
            className="text-sm text-secondary-text mb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            钓龄 {userInfo.fishingAge} | ID: {userInfo.id}
          </motion.p>
          
          <div className="grid grid-cols-3 gap-4">
            {[
              { value: userInfo.stats.totalCatch, label: '总渔获', color: 'text-aqua-teal' },
              { value: userInfo.stats.speciesCount, label: '鱼种数', color: 'text-sunrise-gold' },
              { value: userInfo.stats.monthlyRank, label: '本月排名', color: 'text-primary-text' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 + index * 0.1 }}
              >
                <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
                <div className="text-xs text-secondary-text">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* 成就展示 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <h3 className="text-lg font-bold text-primary-text mb-4">成就徽章</h3>
          <div className="grid grid-cols-4 gap-3">
            {achievements.map((achievement, index) => {
              const Icon = achievement.icon
              return (
                <motion.div
                  key={achievement.id}
                  className="text-center"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  whileHover={{ scale: 1.1 }}
                >
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 ${
                    achievement.unlocked 
                      ? `bg-gradient-to-r ${achievement.color}` 
                      : 'bg-gray-200'
                  }`}>
                    <Icon 
                      className={achievement.unlocked ? 'text-white' : 'text-gray-400'} 
                      size={20} 
                    />
                  </div>
                  <div className={`text-xs ${
                    achievement.unlocked ? 'text-secondary-text' : 'text-gray-400'
                  }`}>
                    {achievement.name}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </motion.div>

      {/* 功能菜单 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          {menuItems.map((item, index) => {
            const Icon = item.icon
            return (
              <motion.div
                key={item.id}
                className="p-4 border-b border-divider last:border-b-0 flex items-center justify-between cursor-pointer hover:bg-light-slate transition-colors"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 + index * 0.05 }}
                whileHover={{ x: 4 }}
                onClick={() => {
                  if (item.page) {
                    navigateToPage(item.page)
                  } else if (item.action) {
                    item.action()
                  }
                }}
              >
                <div className="flex items-center space-x-3">
                  <Icon className="text-aqua-teal" size={20} />
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="text-primary-text font-medium">{item.label}</span>
                      {item.badge && (
                        <span className="text-xs bg-sunrise-gold text-white px-2 py-1 rounded">
                          {item.badge}
                        </span>
                      )}
                    </div>
                    {item.description && (
                      <div className="text-xs text-secondary-text">{item.description}</div>
                    )}
                  </div>
                </div>
                <ChevronRight className="text-secondary-text" size={16} />
              </motion.div>
            )
          })}
        </div>
      </motion.div>

      {/* 等级进度 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
      >
        <div className="bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 p-4 rounded-xl border border-aqua-teal/20">
          <div className="flex items-center space-x-2 mb-3">
            <Target className="text-aqua-teal" size={20} />
            <span className="text-sm font-medium text-primary-text">钓鱼等级</span>
            <span className="text-lg font-bold text-aqua-teal">Lv.15</span>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-secondary-text">升级进度</span>
              <span className="text-primary-text font-medium">75%</span>
            </div>
            <div className="w-full bg-white rounded-full h-2">
              <motion.div 
                className="bg-aqua-teal h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: "75%" }}
                transition={{ duration: 1, delay: 1.2 }}
              />
            </div>
            <div className="text-xs text-secondary-text">
              还需 12 次钓获升级到 16 级
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
