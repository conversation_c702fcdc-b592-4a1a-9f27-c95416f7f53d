import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert, Share } from 'react-native';
import { Check, Share2, Plus, Home, Book, Trophy, Calendar, MapPin, Cloud, Droplets, Save, Eye, Bookmark } from 'lucide-react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  withSpring,
  withRepeat
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { TopNavigation } from '../layout/TopNavigation';
import { AnimatedButton } from '../common/AnimatedButton';
import type { PageType } from '../AppWrapper';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface RecordSuccessPageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  recordData: any;
  uploadedPhotos: Array<{url: string, type: string}>;
  appState: any;
}

export function RecordSuccessPage({ 
  navigateToPage, 
  showFishDetail, 
  openPhotoModal, 
  recordData, 
  uploadedPhotos 
}: RecordSuccessPageProps) {
  const successOpacity = useSharedValue(0);
  const journalOpacity = useSharedValue(0);
  const recommendOpacity = useSharedValue(0);
  const checkScale = useSharedValue(0);
  const confettiOpacity = useSharedValue(0);

  React.useEffect(() => {
    successOpacity.value = withDelay(100, withTiming(1, { duration: 600 }));
    checkScale.value = withDelay(300, withSpring(1));
    confettiOpacity.value = withDelay(300, withTiming(1, { duration: 1500 }));
    journalOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
    recommendOpacity.value = withDelay(1300, withTiming(1, { duration: 600 }));
  }, []);

  const successStyle = useAnimatedStyle(() => ({
    opacity: successOpacity.value,
    transform: [{ translateY: withTiming(successOpacity.value === 1 ? 0 : 30) }],
  }));

  const checkStyle = useAnimatedStyle(() => ({
    transform: [{ scale: checkScale.value }],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ 
      scale: withRepeat(
        withTiming(1.2, { duration: 1000 }),
        -1,
        true
      )
    }],
  }));

  const journalStyle = useAnimatedStyle(() => ({
    opacity: journalOpacity.value,
    transform: [{ translateY: withTiming(journalOpacity.value === 1 ? 0 : 20) }],
  }));

  const recommendStyle = useAnimatedStyle(() => ({
    opacity: recommendOpacity.value,
    transform: [{ translateY: withTiming(recommendOpacity.value === 1 ? 0 : 20) }],
  }));

  const shareRecord = () => {
    Share.share({
      message: '今天钓到了一条漂亮的鲫鱼！🎣',
      title: '我的渔获日志',
    }).catch(() => {
      Alert.alert('分享失败', '请稍后再试');
    });
  };

  const recommendedActions = [
    {
      icon: Book,
      title: '查看鱼种图鉴',
      description: '探索更多鱼种，完成收集',
      action: () => navigateToPage('fishdex'),
      color: '#00A79D'
    },
    {
      icon: Trophy,
      title: '查看排行榜',
      description: '看看您在地区的排名',
      action: () => navigateToPage('leaderboard'),
      color: '#FFC759'
    }
  ];

  const fishDetails = {
    name: recordData?.fishName || '鲫鱼',
    scientificName: recordData?.scientificName || 'Crucian Carp',
    weight: recordData?.weight || 0.8,
    length: 25,
    image: uploadedPhotos?.[0]?.url || 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format'
  };

  const environmentInfo = {
    location: '东湖公园钓点',
    weather: '多云转晴 15°C',
    wind: '微风2级',
    waterTemp: '水温约12°C'
  };

  const tackleInfo = {
    rig: '3号主线 + 2号子线 + 4号钩',
    bait: '玉米粒、面包虫',
    notes: '今天天气不错，鱼情活跃，在水草边上钓获了这条漂亮的鲫鱼。使用玉米粒作饵效果很好，建议其他钓友也可以尝试这个钓点。'
  };

  return (
    <ScrollView 
      className="flex-1 bg-page-bg" 
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    >
      <View>
        <TopNavigation
          title="记录完成"
          showBack
          onBack={() => navigateToPage('homepage')}
          rightIcon="share"
          onRightClick={shareRecord}
        />

        {/* 成功反馈动画 */}
        <AnimatedView style={successStyle} className="px-6 py-6">
          <View className="bg-white p-6 rounded-xl shadow-sm items-center relative overflow-hidden">
            <AnimatedView style={checkStyle}>
              <LinearGradient
                colors={['#FFC759', '#F59E0B']}
                className="w-24 h-24 rounded-full items-center justify-center mb-4"
              >
                <AnimatedView style={pulseStyle}>
                  <Check color="white" size={32} />
                </AnimatedView>
              </LinearGradient>
            </AnimatedView>

            <Text className="text-2xl font-bold text-primary-text mb-2">
              🎉 记录成功！
            </Text>
            <Text className="text-lg text-secondary-text mb-4">
              您的渔获已成功添加到日志
            </Text>
            <View className="flex-row items-center gap-x-2">
              <Trophy color="#00A79D" size={16} />
              <Text className="text-sm text-aqua-teal">获得经验值 +50</Text>
              <Text className="text-sm text-aqua-teal">•</Text>
              <Text className="text-sm text-aqua-teal">连续记录 +1</Text>
            </View>
          </View>
        </AnimatedView>

        {/* 生成的完整日志 */}
        <AnimatedView style={journalStyle} className="px-6 py-4">
          <View className="bg-white rounded-xl shadow-sm overflow-hidden">
            <View className="p-4 border-b border-gray-100">
              <Text className="text-lg font-bold text-primary-text mb-2">您的渔获日志</Text>
              <View className="flex-row items-center">
                <Calendar color="#6B7280" size={14} />
                <Text className="text-sm text-secondary-text ml-1">2024年1月15日 14:30</Text>
              </View>
            </View>

            <View className="p-4">
              {/* 原始照片展示 */}
              {uploadedPhotos && uploadedPhotos.length > 0 && (
                <View className="mb-6">
                  <View className="flex-row items-center justify-between mb-3">
                    <Text className="text-sm font-medium text-primary-text">📸 原始照片</Text>
                    <Text className="text-xs text-secondary-text">{uploadedPhotos.length}张照片</Text>
                  </View>
                  
                  <TouchableOpacity
                    className="mb-3 relative"
                    onPress={() => openPhotoModal(uploadedPhotos.map(p => p.url), 0)}
                  >
                    <Image
                      source={{ uri: uploadedPhotos[0].url }}
                      className="w-full h-48 rounded-xl border-2 border-aqua-teal"
                      resizeMode="cover"
                    />
                    <View className="absolute top-2 left-2 bg-black/50 px-2 py-1 rounded">
                      <Text className="text-white text-xs">主要照片</Text>
                    </View>
                  </TouchableOpacity>
                  
                  <View className="flex-row gap-x-2">
                    {uploadedPhotos.slice(0, 4).map((photo, index) => (
                      <TouchableOpacity
                        key={index}
                        className={`flex-1 aspect-square relative ${index === 0 ? 'border-2 border-aqua-teal' : 'border-2 border-gray-200'} rounded-lg overflow-hidden`}
                        onPress={() => openPhotoModal(uploadedPhotos.map(p => p.url), index)}
                      >
                        <Image
                          source={{ uri: photo.url }}
                          className="w-full h-full"
                          resizeMode="cover"
                        />
                        {index === 0 && (
                          <View className="absolute top-1 right-1 w-3 h-3 bg-aqua-teal rounded-full" />
                        )}
                      </TouchableOpacity>
                    ))}
                    
                    {uploadedPhotos.length < 4 && (
                      <TouchableOpacity
                        className="flex-1 aspect-square border-2 border-dashed border-gray-300 bg-light-slate rounded-lg items-center justify-center"
                        onPress={() => navigateToPage('record')}
                      >
                        <Plus color="#6B7280" size={20} />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              )}

              {/* 鱼种信息卡片 */}
              <View className="flex-row items-start gap-x-4 mb-6">
                <View className="relative">
                  <View className="w-24 h-24 rounded-xl border-2 border-aqua-teal overflow-hidden">
                    <Image
                      source={{ uri: fishDetails.image }}
                      className="w-full h-full"
                      resizeMode="cover"
                    />
                  </View>
                  <View className="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full items-center justify-center">
                    <Text className="text-white text-xs">★</Text>
                  </View>
                </View>
                <View className="flex-1">
                  <View className="flex-row items-center gap-x-2 mb-1">
                    <Text className="text-xl font-bold text-primary-text">{fishDetails.name}</Text>
                    {recordData?.isNewSpecies && (
                      <View className="bg-aqua-teal px-2 py-1 rounded-full">
                        <Text className="text-xs text-white">新解锁</Text>
                      </View>
                    )}
                  </View>
                  <Text className="text-sm text-secondary-text mb-2">{fishDetails.scientificName}</Text>
                  <View className="flex-row gap-x-4">
                    <Text className="text-sm text-primary-text">⚖️ {fishDetails.weight} kg</Text>
                    <Text className="text-sm text-primary-text">📏 {fishDetails.length} cm</Text>
                  </View>
                </View>
              </View>

              {/* 钓鱼详情 */}
              <View className="gap-y-4">
                <View className="flex-row gap-x-4">
                  <View className="flex-1 bg-light-slate p-3 rounded-lg">
                    <Text className="text-xs text-secondary-text mb-1">钓组配置</Text>
                    <Text className="text-sm font-medium text-primary-text">{tackleInfo.rig}</Text>
                  </View>
                  <View className="flex-1 bg-light-slate p-3 rounded-lg">
                    <Text className="text-xs text-secondary-text mb-1">使用饵料</Text>
                    <Text className="text-sm font-medium text-primary-text">{tackleInfo.bait}</Text>
                  </View>
                </View>

                {/* 环境信息 */}
                <LinearGradient
                  colors={['#F0F2F5', '#FFFFFF']}
                  className="p-4 rounded-lg border border-gray-200"
                >
                  <Text className="text-sm font-medium text-primary-text mb-3">📍 钓鱼环境</Text>
                  <View className="gap-y-2">
                    <View className="flex-row items-center gap-x-2">
                      <MapPin color="#00A79D" size={14} />
                      <Text className="text-xs text-secondary-text">{environmentInfo.location}</Text>
                    </View>
                    <View className="flex-row items-center gap-x-2">
                      <Cloud color="#00A79D" size={14} />
                      <Text className="text-xs text-secondary-text">{environmentInfo.weather}</Text>
                    </View>
                    <View className="flex-row justify-between">
                      <Text className="text-xs text-secondary-text">{environmentInfo.wind}</Text>
                      <Text className="text-xs text-secondary-text">{environmentInfo.waterTemp}</Text>
                    </View>
                  </View>
                </LinearGradient>

                {/* 钓鱼心得 */}
                <View className="bg-white border border-gray-200 p-4 rounded-lg">
                  <Text className="text-sm font-medium text-primary-text mb-2">💭 钓鱼心得</Text>
                  <Text className="text-sm text-secondary-text leading-5">
                    "{tackleInfo.notes}"
                  </Text>
                </View>

                {/* AI分析摘要 */}
                <LinearGradient
                  colors={['rgba(0,167,157,0.1)', 'rgba(255,199,89,0.1)']}
                  className="p-4 rounded-lg border border-aqua-teal"
                >
                  <View className="flex-row items-center gap-x-2 mb-2">
                    <Text className="text-sm font-medium text-primary-text">🤖 AI分析摘要</Text>
                  </View>
                  <Text className="text-xs text-secondary-text leading-5">
                    本次渔获的鲫鱼体型适中，在当前季节和水温条件下属于正常表现。建议继续在相似环境下使用相同钓组和饵料配置，成功率较高。
                  </Text>
                </LinearGradient>
              </View>
            </View>

            {/* 底部操作区 */}
            <View className="p-4 bg-light-slate border-t border-gray-100">
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-row items-center gap-x-4">
                  <View className="flex-row items-center gap-x-1">
                    <Eye color="#6B7280" size={14} />
                    <Text className="text-sm text-secondary-text">仅自己可见</Text>
                  </View>
                  <View className="flex-row items-center gap-x-1">
                    <Bookmark color="#6B7280" size={14} />
                    <Text className="text-sm text-secondary-text">已保存</Text>
                  </View>
                </View>
                <Text className="text-xs text-secondary-text">
                  日志编号: #2024011501
                </Text>
              </View>

              {/* 操作按钮 */}
              <View className="flex-row gap-x-3">
                <View className="flex-1">
                  <AnimatedButton
                    onPress={() => navigateToPage('record')}
                    variant="secondary"
                    icon={<Plus color="#6B7280" size={20} />}
                  >
                    继续记录
                  </AnimatedButton>
                </View>
                <View className="flex-1">
                  <AnimatedButton
                    onPress={shareRecord}
                    variant="primary"
                    icon={<Share2 color="white" size={20} />}
                  >
                    分享日志
                  </AnimatedButton>
                </View>
              </View>
            </View>
          </View>
        </AnimatedView>

        {/* 推荐下一步 */}
        <AnimatedView style={recommendStyle} className="px-6 py-4">
          <View className="bg-white p-4 rounded-xl shadow-sm">
            <Text className="text-lg font-bold text-primary-text mb-4">🎯 推荐下一步</Text>
            <View className="gap-y-3">
              {recommendedActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <TouchableOpacity
                    key={action.title}
                    className="flex-row items-center gap-x-3 p-3 bg-light-slate rounded-lg"
                    onPress={action.action}
                  >
                    <View 
                      className="w-10 h-10 rounded-lg items-center justify-center"
                      style={{ backgroundColor: `${action.color}20` }}
                    >
                      <Icon color={action.color} size={20} />
                    </View>
                    <View className="flex-1">
                      <Text className="text-sm font-medium text-primary-text">{action.title}</Text>
                      <Text className="text-xs text-secondary-text">{action.description}</Text>
                    </View>
                    <Text className="text-secondary-text">›</Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </AnimatedView>
      </View>
    </ScrollView>
  );
}