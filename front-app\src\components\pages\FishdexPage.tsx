import React from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions,
  StyleSheet
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withDelay,
  interpolate
} from 'react-native-reanimated';
import { Lock, Star } from 'lucide-react-native';
import { TopNavigation } from '../layout/TopNavigation';
import { colors, spacing, borderRadius, fontSize, fontWeight, shadows, globalStyles } from '../../constants/styles';
import type { PageType, FishSpecies } from '../../types';

interface FishdexPageProps {
  navigateToPage: (page: PageType) => void;
  showFishDetail: (fishId: string) => void;
  openPhotoModal: (photos: string[], index?: number) => void;
  appState: any;
}

export function FishdexPage({ navigateToPage, showFishDetail, openPhotoModal, appState }: FishdexPageProps) {
  const mockFishData: FishSpecies[] = [
    {
      id: 'crucian',
      name: '鲫鱼',
      scientificName: 'Carassius auratus',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=120&fit=crop&auto=format',
      isUnlocked: true
    },
    {
      id: 'carp',
      name: '鲤鱼',
      scientificName: 'Cyprinus carpio',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=120&h=120&fit=crop&auto=format',
      isUnlocked: true
    },
    {
      id: 'bass',
      name: '鲈鱼',
      scientificName: 'Lateolabrax japonicus',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=120&h=120&fit=crop&auto=format',
      isUnlocked: false
    },
    {
      id: 'mandarin',
      name: '鳜鱼',
      scientificName: 'Siniperca chuatsi',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=120&h=120&fit=crop&auto=format',
      isUnlocked: false
    }
  ];

  const unlockedCount = mockFishData.filter(fish => fish.isUnlocked).length;
  const totalCount = mockFishData.length;

  return (
    <View style={globalStyles.container}>
      <TopNavigation
        title="鱼种图鉴"
        showBack
        onBack={() => navigateToPage('home')}
      />
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* 解锁进度 */}
        <View style={styles.progressContainer}>
          <View style={[globalStyles.card, styles.progressCard]}>
            <Text style={styles.progressTitle}>收集进度</Text>
            <View style={styles.progressRow}>
              <Text style={styles.progressCount}>{unlockedCount}</Text>
              <Text style={styles.progressTotal}>/ {totalCount}</Text>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { width: `${(unlockedCount / totalCount) * 100}%` }
                  ]}
                />
              </View>
            </View>
          </View>
        </View>

        {/* 鱼种网格 */}
        <View style={styles.fishGridContainer}>
          <View style={styles.fishGrid}>
            {mockFishData.map((fish, index) => (
              <TouchableOpacity
                key={fish.id}
                style={[
                  styles.fishCard,
                  fish.isUnlocked ? styles.fishCardUnlocked : styles.fishCardLocked
                ]}
                onPress={() => fish.isUnlocked && showFishDetail(fish.id)}
                disabled={!fish.isUnlocked}
              >
                <View style={styles.fishImageContainer}>
                  {fish.isUnlocked ? (
                    <Image
                      source={{ uri: fish.image }}
                      style={styles.fishImage}
                    />
                  ) : (
                    <View style={styles.lockedImageContainer}>
                      <Lock size={24} color={colors.secondaryText} />
                    </View>
                  )}
                  
                  {fish.isUnlocked && (
                    <View style={styles.starBadge}>
                      <Star size={16} color={colors.sunriseGold} fill={colors.sunriseGold} />
                    </View>
                  )}
                </View>
                
                <View style={styles.fishInfo}>
                  <Text style={[
                    styles.fishName,
                    { color: fish.isUnlocked ? colors.primaryText : colors.secondaryText }
                  ]}>
                    {fish.isUnlocked ? fish.name : '???'}
                  </Text>
                  <Text style={styles.fishScientificName}>
                    {fish.isUnlocked ? fish.scientificName : '未解锁'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  progressContainer: {
    paddingVertical: spacing.md,
  },
  progressCard: {
    // Uses globalStyles.card
  },
  progressTitle: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.bold,
    color: colors.primaryText,
    marginBottom: spacing.sm,
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  progressCount: {
    fontSize: fontSize.xxl,
    fontWeight: fontWeight.bold,
    color: colors.aquaTeal,
  },
  progressTotal: {
    color: colors.secondaryText,
  },
  progressBar: {
    flex: 1,
    backgroundColor: colors.divider,
    height: 8,
    borderRadius: borderRadius.full,
    marginLeft: spacing.md,
  },
  progressFill: {
    backgroundColor: colors.aquaTeal,
    height: 8,
    borderRadius: borderRadius.full,
  },
  fishGridContainer: {
    paddingBottom: 96,
  },
  fishGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  fishCard: {
    width: '48%',
    marginBottom: spacing.md,
    borderRadius: borderRadius.xl,
    overflow: 'hidden',
  },
  fishCardUnlocked: {
    backgroundColor: colors.white,
    ...shadows.small,
  },
  fishCardLocked: {
    backgroundColor: colors.divider,
  },
  fishImageContainer: {
    aspectRatio: 1,
    padding: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  fishImage: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.full,
    ...shadows.fishHover,
  },
  lockedImageContainer: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.full,
    backgroundColor: colors.divider,
    alignItems: 'center',
    justifyContent: 'center',
  },
  starBadge: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
  },
  fishInfo: {
    padding: spacing.md,
    backgroundColor: colors.white,
  },
  fishName: {
    fontWeight: fontWeight.medium,
    textAlign: 'center',
  },
  fishScientificName: {
    fontSize: fontSize.xs,
    color: colors.secondaryText,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
});