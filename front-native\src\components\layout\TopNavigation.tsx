import React from 'react'
import { View, Text, StyleSheet, Pressable } from 'react-native'
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring
} from 'react-native-reanimated'
import { Ionicons } from '@expo/vector-icons'
import { Colors, Spacing, FontSizes, FontWeights } from '../../constants/colors'

interface TopNavigationProps {
  title: string
  showBack?: boolean
  onBack?: () => void
  rightElement?: React.ReactNode
  backgroundColor?: string
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable)

export function TopNavigation({ 
  title, 
  showBack = false, 
  onBack,
  rightElement,
  backgroundColor = Colors.cardBackground
}: TopNavigationProps) {
  const backScale = useSharedValue(1)

  const backAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: backScale.value }]
  }))

  const handleBackPressIn = () => {
    backScale.value = withSpring(0.9, { damping: 15, stiffness: 300 })
  }

  const handleBackPressOut = () => {
    backScale.value = withSpring(1, { damping: 15, stiffness: 300 })
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <View style={styles.content}>
        {/* 左侧 - 返回按钮或占位 */}
        <View style={styles.leftSection}>
          {showBack && onBack ? (
            <AnimatedPressable
              style={[backAnimatedStyle, styles.backButton]}
              onPressIn={handleBackPressIn}
              onPressOut={handleBackPressOut}
              onPress={onBack}
            >
              <Ionicons 
                name="chevron-back" 
                size={24} 
                color={Colors.primaryText} 
              />
            </AnimatedPressable>
          ) : (
            <View style={styles.placeholder} />
          )}
        </View>

        {/* 中间 - 标题 */}
        <View style={styles.centerSection}>
          <Text style={styles.title} numberOfLines={1}>
            {title}
          </Text>
        </View>

        {/* 右侧 - 自定义元素或占位 */}
        <View style={styles.rightSection}>
          {rightElement || <View style={styles.placeholder} />}
        </View>
      </View>
    </View>
  )
}

// 预定义的右侧元素组件
export function NotificationButton({ onPress }: { onPress: () => void }) {
  const scale = useSharedValue(1)

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }]
  }))

  const handlePressIn = () => {
    scale.value = withSpring(0.9, { damping: 15, stiffness: 300 })
  }

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 })
  }

  return (
    <AnimatedPressable
      style={[animatedStyle, styles.iconButton]}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}
    >
      <Ionicons 
        name="notifications-outline" 
        size={24} 
        color={Colors.primaryText} 
      />
    </AnimatedPressable>
  )
}

export function SettingsButton({ onPress }: { onPress: () => void }) {
  const scale = useSharedValue(1)

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }]
  }))

  const handlePressIn = () => {
    scale.value = withSpring(0.9, { damping: 15, stiffness: 300 })
  }

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 })
  }

  return (
    <AnimatedPressable
      style={[animatedStyle, styles.iconButton]}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}
    >
      <Ionicons 
        name="settings-outline" 
        size={24} 
        color={Colors.primaryText} 
      />
    </AnimatedPressable>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: Spacing.md,
    paddingBottom: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.divider,
  },
  
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    height: 44,
  },
  
  leftSection: {
    width: 44,
    alignItems: 'flex-start',
  },
  
  centerSection: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: Spacing.sm,
  },
  
  rightSection: {
    width: 44,
    alignItems: 'flex-end',
  },
  
  title: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.primaryText,
  },
  
  backButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
  },
  
  iconButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
  },
  
  placeholder: {
    width: 44,
    height: 44,
  },
})
