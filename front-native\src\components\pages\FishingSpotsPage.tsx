import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { TopNavigation } from '../layout/TopNavigation'
import { globalStyles } from '../../styles'
import { Colors, Spacing, FontSizes, FontWeights } from '../../constants/colors'
import type { NavigationProps } from '../../types'

export function FishingSpotsPage({ navigateToPage }: NavigationProps) {
  const handleBack = () => {
    navigateToPage('profile')
  }

  return (
    <View style={globalStyles.container}>
      <TopNavigation 
        title="钓点推荐" 
        showBack 
        onBack={handleBack}
      />
      <View style={styles.content}>
        <Text style={styles.placeholder}>钓点推荐开发中...</Text>
        <Text style={styles.description}>
          这里将展示各个钓点的信息、距离、鱼类分布等
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
  },
  
  placeholder: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.primaryText,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  
  description: {
    fontSize: FontSizes.md,
    color: Colors.secondaryText,
    textAlign: 'center',
    lineHeight: 24,
  },
})
