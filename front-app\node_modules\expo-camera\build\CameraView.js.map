{"version": 3, "file": "CameraView.js", "sourceRoot": "", "sources": ["../src/CameraView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAA0B,MAAM,mBAAmB,CAAC;AAC1F,OAAO,EAAY,SAAS,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAcvD,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,aAAa,MAAM,qBAAqB,CAAC;AAEhD,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAEpE,MAAM,eAAe,GAAG,GAAG,CAAC;AAE5B,MAAM,wBAAwB,GAC5B,EAAE,CAAC;AAEL,IAAI,8BAA8B,GAAG,KAAK,CAAC;AAC3C,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAE3B,SAAS,oBAAoB,CAAC,OAA8B;IAC1D,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;QAClC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CACV,8FAA8F,CAC/F,CAAC;IACJ,CAAC;IAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;QAC3B,MAAM,EAAE,GAAG,kBAAkB,EAAE,CAAC;QAChC,wBAAwB,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;QACtD,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC;QAChB,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAkC,EAAE;IAClE,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC5C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CACV,8FAA8F,CAC/F,CAAC;IACJ,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,eAAe,CAAC,EACvB,WAAW,GAGZ;IACC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;IACjC,MAAM,QAAQ,GAAG,wBAAwB,CAAC,EAAE,CAAC,CAAC;IAC9C,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,OAAO,wBAAwB,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC;AACH,CAAC;AAED,MAAM,CAAC,OAAO,OAAO,UAAW,SAAQ,SAA0B;IAChE;;OAEG;IACH,MAAM,CAAC,+BAA+B,GAAY,aAAa,CAAC,+BAA+B,CAAC;IAChG;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB;QAC3B,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;YACpC,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,aAAa,CAAC,gBAAgB,EAAE,CAAC;IAC1C,CAAC;IAED,cAAc;IACd;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,4BAA4B;QACvC,IAAI,CAAC,aAAa,CAAC,4BAA4B,EAAE,CAAC;YAChD,MAAM,IAAI,mBAAmB,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,aAAa,CAAC,4BAA4B,EAAE,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,6BAA6B;QACjC,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,wBAAwB,EAAE,CAAC,IAAI,EAAE,CAAC;IAC3E,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,uBAAuB;QAC3B,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC,IAAI,EAAE,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,oBAAoB;QAIlB,OAAO;YACL,+BAA+B,EAAE,aAAa,CAAC,+BAA+B;YAC9E,6BAA6B,EAAE,aAAa,CAAC,6BAA6B;SAC3E,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;IACjD,CAAC;IAED,2EAA2E;IAC3E,MAAM,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAE3C,MAAM,CAAC,YAAY,GAAoB;QACrC,IAAI,EAAE,CAAC;QACP,MAAM,EAAE,MAAM;QACd,WAAW,EAAE,KAAK;QAClB,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,KAAK;KACb,CAAC;IAEF,aAAa,CAAiB;IAC9B,UAAU,GAAG,SAAS,EAAiB,CAAC;IACxC,WAAW,GAAoC,EAAE,CAAC;IAClD,gBAAgB,GAAkC,EAAE,CAAC;IA0BrD,KAAK,CAAC,gBAAgB,CAAC,OAA8B;QACnD,MAAM,cAAc,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAErD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,OAAO,EAAE,UAAU,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAyB;QAClD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,UAAU,CAAC,+BAA+B,EAAE,CAAC;YACxE,MAAM,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc;QACzB,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,UAAU,CAAC,+BAA+B,EAAE,CAAC;YACxE,MAAM,aAAa,CAAC,cAAc,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAyC;QACrE,OAAO,aAAa,CAAC,WAAW,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,WAAW,CAAC,OAAgC;QAChD,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,oBAAoB;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACH,aAAa;QACX,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC;IAC3C,CAAC;IAED,cAAc,GAAG,GAAG,EAAE;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC;IAEF,yBAAyB,GAAG,CAAC,EAAE,WAAW,EAAoC,EAAE,EAAE;QAChF,IAAI,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC;YACxC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QACnD,CAAC;IACH,CAAC,CAAC;IAEF,aAAa,GAAG,CAAC,EAAE,WAAW,EAAwC,EAAE,EAAE;QACxE,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,CAAC;IAEF,+BAA+B,GAAG,CAAC,EACjC,WAAW,GAGZ,EAAE,EAAE;QACH,IAAI,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,CAAC;YAC9C,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAC;QACzD,CAAC;IACH,CAAC,CAAC;IAEF,iBAAiB,GACf,CAAC,QAAmB,EAAE,EAAE,CACxB,CAAC,EAAE,WAAW,EAAwB,EAAE,EAAE;QACxC,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;QAC7B,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtD,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,eAAe,EAC9E,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,WAAW,CAAC,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC;IACH,CAAC,CAAC;IAEJ,aAAa,GAAG,CAAC,GAAuB,EAAE,EAAE;QAC1C,IAAI,GAAG,EAAE,CAAC;YACR,iDAAiD;YACjD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;gBAC1B,IAAI,CAAC,aAAa,GAAG,GAAU,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,MAAM;QACJ,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAClD,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;QAEd,mBAAmB;QACnB,IAAI,WAAW,CAAC,QAAQ,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAC5D,OAAO,CAAC,IAAI,CACV,oMAAoM,CACrM,CAAC;YACF,8BAA8B,GAAG,IAAI,CAAC;QACxC,CAAC;QAED,OAAO,CACL,CAAC,UAAU,CACT,IAAI,WAAW,CAAC,CAChB,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CACrB,aAAa,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CACnC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CACjC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CACnC,wBAAwB,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CACzD,cAAc,CAAC,CAAC,eAAe,CAAC,CAChC,8BAA8B,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,EACrE,CACH,CAAC;IACJ,CAAC", "sourcesContent": ["import { Platform, UnavailabilityError, type EventSubscription } from 'expo-modules-core';\nimport { type Ref, Component, createRef } from 'react';\n\nimport {\n  CameraCapturedPicture,\n  CameraOrientation,\n  CameraPictureOptions,\n  CameraViewProps,\n  CameraRecordingOptions,\n  CameraViewRef,\n  ScanningOptions,\n  ScanningResult,\n  VideoCodec,\n  AvailableLenses,\n} from './Camera.types';\nimport ExpoCamera from './ExpoCamera';\nimport CameraManager from './ExpoCameraManager';\nimport { PictureRef } from './PictureRef';\nimport { ConversionTables, ensureNativeProps } from './utils/props';\n\nconst EventThrottleMs = 500;\n\nconst _PICTURE_SAVED_CALLBACKS: Record<number, CameraPictureOptions['onPictureSaved'] | undefined> =\n  {};\n\nlet loggedRenderingChildrenWarning = false;\nlet _GLOBAL_PICTURE_ID = 1;\n\nfunction ensurePictureOptions(options?: CameraPictureOptions): CameraPictureOptions {\n  if (!options || typeof options !== 'object') {\n    return {};\n  }\n\n  if (options.quality === undefined) {\n    options.quality = 1;\n  }\n\n  if (options.mirror) {\n    console.warn(\n      'The `mirror` option is deprecated. Please use the `mirror` prop on the `CameraView` instead.'\n    );\n  }\n\n  if (options.onPictureSaved) {\n    const id = _GLOBAL_PICTURE_ID++;\n    _PICTURE_SAVED_CALLBACKS[id] = options.onPictureSaved;\n    options.id = id;\n    options.fastMode = true;\n  }\n\n  return options;\n}\n\nfunction ensureRecordingOptions(options: CameraRecordingOptions = {}): CameraRecordingOptions {\n  if (!options || typeof options !== 'object') {\n    return {};\n  }\n\n  if (options.mirror) {\n    console.warn(\n      'The `mirror` option is deprecated. Please use the `mirror` prop on the `CameraView` instead.'\n    );\n  }\n\n  return options;\n}\n\nfunction _onPictureSaved({\n  nativeEvent,\n}: {\n  nativeEvent: { data: CameraCapturedPicture; id: number };\n}) {\n  const { id, data } = nativeEvent;\n  const callback = _PICTURE_SAVED_CALLBACKS[id];\n  if (callback) {\n    callback(data);\n    delete _PICTURE_SAVED_CALLBACKS[id];\n  }\n}\n\nexport default class CameraView extends Component<CameraViewProps> {\n  /**\n   * Property that determines if the current device has the ability to use `DataScannerViewController` (iOS 16+).\n   */\n  static isModernBarcodeScannerAvailable: boolean = CameraManager.isModernBarcodeScannerAvailable;\n  /**\n   * Check whether the current device has a camera. This is useful for web and simulators cases.\n   * This isn't influenced by the Permissions API (all platforms), or HTTP usage (in the browser).\n   * You will still need to check if the native permission has been accepted.\n   * @platform web\n   */\n  static async isAvailableAsync(): Promise<boolean> {\n    if (!CameraManager.isAvailableAsync) {\n      throw new UnavailabilityError('expo-camera', 'isAvailableAsync');\n    }\n\n    return CameraManager.isAvailableAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Queries the device for the available video codecs that can be used in video recording.\n   * @return A promise that resolves to a list of strings that represents available codecs.\n   * @platform ios\n   */\n  static async getAvailableVideoCodecsAsync(): Promise<VideoCodec[]> {\n    if (!CameraManager.getAvailableVideoCodecsAsync) {\n      throw new UnavailabilityError('Camera', 'getAvailableVideoCodecsAsync');\n    }\n\n    return CameraManager.getAvailableVideoCodecsAsync();\n  }\n\n  /**\n   * Get picture sizes that are supported by the device.\n   * @return Returns a Promise that resolves to an array of strings representing picture sizes that can be passed to `pictureSize` prop.\n   * The list varies across Android devices but is the same for every iOS.\n   */\n  async getAvailablePictureSizesAsync(): Promise<string[]> {\n    return (await this._cameraRef.current?.getAvailablePictureSizes()) ?? [];\n  }\n\n  /**\n   * Returns the available lenses for the currently selected camera.\n   *\n   * @return Returns a Promise that resolves to an array of strings representing the lens type that can be passed to `selectedLens` prop.\n   * @platform ios\n   */\n  async getAvailableLensesAsync(): Promise<string[]> {\n    return (await this._cameraRef.current?.getAvailableLenses()) ?? [];\n  }\n\n  /**\n   * Returns an object with the supported features of the camera on the current device.\n   */\n  getSupportedFeatures(): {\n    isModernBarcodeScannerAvailable: boolean;\n    toggleRecordingAsyncAvailable: boolean;\n  } {\n    return {\n      isModernBarcodeScannerAvailable: CameraManager.isModernBarcodeScannerAvailable,\n      toggleRecordingAsyncAvailable: CameraManager.toggleRecordingAsyncAvailable,\n    };\n  }\n\n  /**\n   * Resumes the camera preview.\n   */\n  async resumePreview(): Promise<void> {\n    return this._cameraRef.current?.resumePreview();\n  }\n\n  /**\n   * Pauses the camera preview. It is not recommended to use `takePictureAsync` when preview is paused.\n   */\n  async pausePreview(): Promise<void> {\n    return this._cameraRef.current?.pausePreview();\n  }\n\n  // Values under keys from this object will be transformed to native options\n  static ConversionTables = ConversionTables;\n\n  static defaultProps: CameraViewProps = {\n    zoom: 0,\n    facing: 'back',\n    enableTorch: false,\n    mode: 'picture',\n    flash: 'off',\n  };\n\n  _cameraHandle?: number | null;\n  _cameraRef = createRef<CameraViewRef>();\n  _lastEvents: { [eventName: string]: string } = {};\n  _lastEventsTimes: { [eventName: string]: Date } = {};\n\n  // @needsAudit\n  /**\n   * Takes a picture and saves it to app's cache directory. Photos are rotated to match device's orientation\n   * (if `options.skipProcessing` flag is not enabled) and scaled to match the preview.\n   * > **Note**: Make sure to wait for the [`onCameraReady`](#oncameraready) callback before calling this method.\n   * @param options An object in form of `CameraPictureOptions` type.\n   * @return Returns a Promise that resolves to `CameraCapturedPicture` object, where `uri` is a URI to the local image file on Android,\n   * iOS, and a base64 string on web (usable as the source for an `Image` element). The `width` and `height` properties specify\n   * the dimensions of the image.\n   *\n   * `base64` is included if the `base64` option was truthy, and is a string containing the JPEG data\n   * of the image in Base64. Prepend it with `'data:image/jpg;base64,'` to get a data URI, which you can use as the source\n   * for an `Image` element for example.\n   *\n   * `exif` is included if the `exif` option was truthy, and is an object containing EXIF\n   * data for the image. The names of its properties are EXIF tags and their values are the values for those tags.\n   *\n   * > On native platforms, the local image URI is temporary. Use [`FileSystem.copyAsync`](filesystem/#filesystemcopyasyncoptions)\n   * > to make a permanent copy of the image.\n   *\n   * > **Note:** Avoid calling this method while the preview is paused. On Android, this will throw an error. On iOS, this will take a picture of the last frame that is currently on screen.\n   */\n  async takePictureAsync(options: CameraPictureOptions & { pictureRef: true }): Promise<PictureRef>;\n  async takePictureAsync(options?: CameraPictureOptions): Promise<CameraCapturedPicture>;\n  async takePictureAsync(options?: CameraPictureOptions) {\n    const pictureOptions = ensurePictureOptions(options);\n\n    if (Platform.OS === 'ios' && options?.pictureRef) {\n      return this._cameraRef.current?.takePictureRef?.(options);\n    }\n    return this._cameraRef.current?.takePicture(pictureOptions);\n  }\n\n  /**\n   * On Android, we will use the [Google code scanner](https://developers.google.com/ml-kit/vision/barcode-scanning/code-scanner).\n   * On iOS, presents a modal view controller that uses the [`DataScannerViewController`](https://developer.apple.com/documentation/visionkit/scanning_data_with_the_camera) available on iOS 16+.\n   * @platform android\n   * @platform ios\n   */\n  static async launchScanner(options?: ScanningOptions): Promise<void> {\n    if (!options) {\n      options = { barcodeTypes: [] };\n    }\n    if (Platform.OS !== 'web' && CameraView.isModernBarcodeScannerAvailable) {\n      await CameraManager.launchScanner(options);\n    }\n  }\n\n  /**\n   * Dismiss the scanner presented by `launchScanner`.\n   * > **info** On Android, the scanner is dismissed automatically when a barcode is scanned.\n   * @platform ios\n   */\n  static async dismissScanner(): Promise<void> {\n    if (Platform.OS !== 'web' && CameraView.isModernBarcodeScannerAvailable) {\n      await CameraManager.dismissScanner();\n    }\n  }\n\n  /**\n   * Invokes the `listener` function when a bar code has been successfully scanned. The callback is provided with\n   * an object of the `ScanningResult` shape, where the `type` refers to the bar code type that was scanned and the `data` is the information encoded in the bar code\n   * (in this case of QR codes, this is often a URL). See [`BarcodeType`](#barcodetype) for supported values.\n   * @param listener Invoked with the [ScanningResult](#scanningresult) when a bar code has been successfully scanned.\n   *\n   * @platform ios\n   * @platform android\n   */\n  static onModernBarcodeScanned(listener: (event: ScanningResult) => void): EventSubscription {\n    return CameraManager.addListener('onModernBarcodeScanned', listener);\n  }\n\n  /**\n   * Starts recording a video that will be saved to cache directory. Videos are rotated to match device's orientation.\n   * Flipping camera during a recording results in stopping it.\n   * @param options A map of `CameraRecordingOptions` type.\n   * @return Returns a Promise that resolves to an object containing video file `uri` property and a `codec` property on iOS.\n   * The Promise is returned if `stopRecording` was invoked, one of `maxDuration` and `maxFileSize` is reached or camera preview is stopped.\n   * @platform android\n   * @platform ios\n   */\n  async recordAsync(options?: CameraRecordingOptions) {\n    const recordingOptions = ensureRecordingOptions(options);\n    return this._cameraRef.current?.record(recordingOptions);\n  }\n\n  /**\n   * Pauses or resumes the video recording. Only has an effect if there is an active recording. On `iOS`, this method only supported on `iOS` 18.\n   *\n   * @example\n   * ```ts\n   * const { toggleRecordingAsyncAvailable } = getSupportedFeatures()\n   *\n   * return (\n   *  {toggleRecordingAsyncAvailable && (\n   *    <Button title=\"Toggle Recording\" onPress={toggleRecordingAsync} />\n   *  )}\n   * )\n   * ```\n   */\n  async toggleRecordingAsync() {\n    return this._cameraRef.current?.toggleRecording();\n  }\n\n  /**\n   * Stops recording if any is in progress.\n   * @platform android\n   * @platform ios\n   */\n  stopRecording() {\n    this._cameraRef.current?.stopRecording();\n  }\n\n  _onCameraReady = () => {\n    if (this.props.onCameraReady) {\n      this.props.onCameraReady();\n    }\n  };\n\n  _onAvailableLensesChanged = ({ nativeEvent }: { nativeEvent: AvailableLenses }) => {\n    if (this.props.onAvailableLensesChanged) {\n      this.props.onAvailableLensesChanged(nativeEvent);\n    }\n  };\n\n  _onMountError = ({ nativeEvent }: { nativeEvent: { message: string } }) => {\n    if (this.props.onMountError) {\n      this.props.onMountError(nativeEvent);\n    }\n  };\n\n  _onResponsiveOrientationChanged = ({\n    nativeEvent,\n  }: {\n    nativeEvent: { orientation: CameraOrientation };\n  }) => {\n    if (this.props.onResponsiveOrientationChanged) {\n      this.props.onResponsiveOrientationChanged(nativeEvent);\n    }\n  };\n\n  _onObjectDetected =\n    (callback?: Function) =>\n    ({ nativeEvent }: { nativeEvent: any }) => {\n      const { type } = nativeEvent;\n      if (\n        this._lastEvents[type] &&\n        this._lastEventsTimes[type] &&\n        JSON.stringify(nativeEvent) === this._lastEvents[type] &&\n        new Date().getTime() - this._lastEventsTimes[type].getTime() < EventThrottleMs\n      ) {\n        return;\n      }\n\n      if (callback) {\n        callback(nativeEvent);\n        this._lastEventsTimes[type] = new Date();\n        this._lastEvents[type] = JSON.stringify(nativeEvent);\n      }\n    };\n\n  _setReference = (ref: Ref<CameraViewRef>) => {\n    if (ref) {\n      // TODO(Bacon): Unify these - perhaps with hooks?\n      if (Platform.OS === 'web') {\n        this._cameraHandle = ref as any;\n      }\n    }\n  };\n\n  render() {\n    const nativeProps = ensureNativeProps(this.props);\n    const onBarcodeScanned = this.props.onBarcodeScanned\n      ? this._onObjectDetected(this.props.onBarcodeScanned)\n      : undefined;\n\n    // @ts-expect-error\n    if (nativeProps.children && !loggedRenderingChildrenWarning) {\n      console.warn(\n        'The <CameraView> component does not support children. This may lead to inconsistent behaviour or crashes. If you want to render content on top of the Camera, consider using absolute positioning.'\n      );\n      loggedRenderingChildrenWarning = true;\n    }\n\n    return (\n      <ExpoCamera\n        {...nativeProps}\n        ref={this._cameraRef}\n        onCameraReady={this._onCameraReady}\n        onMountError={this._onMountError}\n        onBarcodeScanned={onBarcodeScanned}\n        onAvailableLensesChanged={this._onAvailableLensesChanged}\n        onPictureSaved={_onPictureSaved}\n        onResponsiveOrientationChanged={this._onResponsiveOrientationChanged}\n      />\n    );\n  }\n}\n"]}