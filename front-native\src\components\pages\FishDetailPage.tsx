import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { TopNavigation } from '../layout/TopNavigation'
import { globalStyles } from '../../styles'
import { Colors, Spacing, FontSizes, FontWeights } from '../../constants/colors'
import type { NavigationProps } from '../../types'

interface FishDetailPageProps extends NavigationProps {
  fishId: string
}

export function FishDetailPage({ navigateToPage, fishId }: FishDetailPageProps) {
  const handleBack = () => {
    navigateToPage('fishdex')
  }

  return (
    <View style={globalStyles.container}>
      <TopNavigation 
        title="鱼类详情" 
        showBack 
        onBack={handleBack}
      />
      <View style={styles.content}>
        <Text style={styles.placeholder}>鱼类详情页开发中...</Text>
        <Text style={styles.description}>
          鱼类ID: {fishId}
        </Text>
        <Text style={styles.description}>
          这里将展示鱼类的详细信息、栖息地、钓鱼技巧等
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
  },
  
  placeholder: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.primaryText,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  
  description: {
    fontSize: FontSizes.md,
    color: Colors.secondaryText,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: Spacing.sm,
  },
})
