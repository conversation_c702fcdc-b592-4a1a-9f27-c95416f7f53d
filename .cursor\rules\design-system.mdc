---
globs: *.html,*.css,*.js,*.tsx,*.jsx
description: 设计系统和UI规范指南
---

# 设计系统规范

## 配色方案
严格遵循 [配色方案.md](mdc:配色方案.md) 中定义的颜色系统：

### 主色系
- **静谧深蓝**: `#1A2E40` - 导航栏、标签栏背景
- **高级灰**: `#F0F2F5` - 页面主背景色

### 点缀色
- **拂晓金**: `#FFC759` - CTA按钮、选中状态、徽章
- **水波青**: `#00A79D` - 次要按钮、链接、状态提示

### 中性色
- **主要文本黑**: `#121212` - 标题、正文
- **次要文本灰**: `#6B7280` - 辅助文字、未激活状态
- **卡片背景**: `#FFFFFF` - 信息卡片、弹窗背景
- **分割线**: `#E5E7EB` - 列表分割线、边框

## 设计原则
1. **静谧深海与拂晓微光** - 核心设计理念
2. **现代扁平化设计** - 简洁、直观的视觉语言
3. **游戏化体验** - 通过色彩和动画营造成就感
4. **移动优先** - 针对手机屏幕优化的布局

## 组件规范
- 卡片圆角: 12px-16px
- 按钮圆角: 8px-12px  
- 间距系统: 4px的倍数 (4, 8, 12, 16, 24px)
- 字体大小: text-xs(12px), text-sm(14px), text-base(16px), text-lg(18px)