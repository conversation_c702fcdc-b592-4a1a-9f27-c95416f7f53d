import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withTiming,
  Easing
} from 'react-native-reanimated';
import { ArrowLeft, Bell, Share2, Settings, Download, Plus } from 'lucide-react-native';
import { colors, gradients, spacing, fontSize, fontWeight } from '../../constants/styles';

interface TopNavigationProps {
  title: string;
  showBack?: boolean;
  onBack?: () => void;
  rightIcon?: 'bell' | 'share' | 'settings' | 'download' | 'plus';
  onRightClick?: () => void;
  stepInfo?: string;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export function TopNavigation({ 
  title, 
  showBack, 
  onBack, 
  rightIcon, 
  onRightClick, 
  stepInfo
}: TopNavigationProps) {
  const insets = useSafeAreaInsets();
  const translateY = useSharedValue(-50);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    translateY.value = withSpring(0);
    opacity.value = withTiming(1, {
      duration: 300,
      easing: Easing.out(Easing.exp),
    });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
  }));

  const getRightIcon = () => {
    switch (rightIcon) {
      case 'bell': return <Bell size={20} color="white" />;
      case 'share': return <Share2 size={20} color="white" />;
      case 'settings': return <Settings size={20} color="white" />;
      case 'download': return <Download size={20} color="white" />;
      case 'plus': return <Plus size={20} color="white" />;
      default: return null;
    }
  };

  return (
    <Animated.View style={animatedStyle}>
      <LinearGradient
        colors={gradients.ocean}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ 
          paddingTop: insets.top,
          paddingHorizontal: spacing.lg,
          paddingBottom: spacing.md
        }}
      >
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: spacing.md
          }}>
            {showBack && (
              <AnimatedTouchableOpacity
                onPress={onBack}
                style={{ padding: 4 }}
              >
                <ArrowLeft size={20} color="white" />
              </AnimatedTouchableOpacity>
            )}
            <Text style={{
              fontSize: fontSize.lg,
              fontWeight: fontWeight.bold,
              color: colors.white
            }}>
              {title}
            </Text>
          </View>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: spacing.md
          }}>
            {stepInfo && (
              <Text style={{
                fontSize: fontSize.sm,
                color: colors.white
              }}>
                {stepInfo}
              </Text>
            )}
            {rightIcon && (
              <AnimatedTouchableOpacity
                onPress={onRightClick}
                style={{ padding: 4 }}
              >
                {getRightIcon()}
              </AnimatedTouchableOpacity>
            )}
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );
}