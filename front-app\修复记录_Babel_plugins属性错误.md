## 背景

在运行 Expo（front-app 子项目）时出现构建失败，终端日志显示：

```
iOS Bundling failed index.ts
ERROR  index.ts: [BABEL] ... .plugins is not a valid Plugin property
```

环境（关键信息）：
- Expo: 53.x
- React: 19.x
- React Native: 0.79.x
- NativeWind: 4.x
- react-native-reanimated: 3.x

---

## 根因分析

- `nativewind/babel` 在 v4 中是一个 Babel「预设（preset）」而不是「插件（plugin）」。
- 将 `nativewind/babel` 误放进 `plugins` 数组时，Babel 会把其返回的对象（内部含有 `plugins` 字段）当成单个「插件配置」解析，从而触发报错：`.plugins is not a valid Plugin property`。

验证依据（包内导向）：
- `nativewind/babel` -> 实际导出来自 `react-native-css-interop/babel`，该预设返回一个包含 `plugins` 数组的对象。

---

## 修复方案

1) 将 `nativewind/babel` 从 `plugins` 移至 `presets`。
2) 确保 `react-native-reanimated/plugin` 保持在 `plugins` 数组的最后（官方要求）。
3) 清理缓存并重启打包器。

示例：

错误（问题配置）：
```javascript
module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'nativewind/babel',
      'react-native-reanimated/plugin'
    ],
  };
};
```

正确（修复后配置）：
```javascript
module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo', 'nativewind/babel'],
    plugins: ['react-native-reanimated/plugin'],
  };
};
```

---

## 相关文件
- `front-app/babel.config.js`
- `front-app/metro.config.js`（已启用 `withNativeWind`）
- `front-app/tailwind.config.js`（已使用 `nativewind/preset`）

---

## 验证步骤
1) 清理缓存并重启 Expo：
```bash
npx expo start -c
```
2) 观察 Metro 日志不再出现 “.plugins is not a valid Plugin property”。
3) 打开页面，确认使用 `className` 的样式（NativeWind）和动画（Reanimated）正常。

---

## 常见变体与排查
- 仍报错：
  - 删除 `node_modules` 与锁文件后重装依赖，再次 `expo start -c`。
  - 确认 `react-native-reanimated/plugin` 在 `plugins` 的最后。
  - 确认 `metro.config.js` 已使用 `withNativeWind(config, { input: './global.css' })`。
  - 确认 `tailwind.config.js` 内含 `presets: [require('nativewind/preset')]` 且 `content` 路径覆盖实际源码。

---

## 版本与兼容性建议
- 固定（或成组升级）以下版本以减少不兼容：
  - Expo 53.x / React 19.x / RN 0.79.x
  - NativeWind 4.x
  - react-native-reanimated 3.x（并保持其 Babel 插件在最后）

---

## 参考
- NativeWind 快速开始（Expo）：`https://www.nativewind.dev/quick-starts/expo`
- Reanimated Babel 插件顺序：`https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/installation/`
- 相关讨论（Babel 插件属性报错）：`https://stackoverflow.com/questions/77996575/babel-plugins-is-not-a-valid-plugin-property`


