'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { X, ChevronLeft, ChevronRight } from 'lucide-react'
import { useState, useEffect } from 'react'
import Image from 'next/image'

interface PhotoModalProps {
  isOpen: boolean
  photos: string[]
  currentIndex: number
  onClose: () => void
}

export function PhotoModal({ isOpen, photos, currentIndex, onClose }: PhotoModalProps) {
  const [activeIndex, setActiveIndex] = useState(currentIndex)

  useEffect(() => {
    setActiveIndex(currentIndex)
  }, [currentIndex])

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }

    return () => {
      document.body.style.overflow = 'auto'
    }
  }, [isOpen])

  const navigatePhoto = (direction: number) => {
    const newIndex = activeIndex + direction
    if (newIndex < 0) {
      setActiveIndex(photos.length - 1)
    } else if (newIndex >= photos.length) {
      setActiveIndex(0)
    } else {
      setActiveIndex(newIndex)
    }
  }

  const goToPhoto = (index: number) => {
    setActiveIndex(index)
  }

  if (!isOpen || photos.length === 0) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          style={{ backdropFilter: 'blur(4px)' }}
        >
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {/* 关闭按钮 */}
            <motion.button
              onClick={onClose}
              className="absolute top-4 right-4 w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all z-10"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <X size={20} />
            </motion.button>

            {/* 导航按钮 */}
            {photos.length > 1 && (
              <>
                <motion.button
                  onClick={() => navigatePhoto(-1)}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <ChevronLeft size={24} />
                </motion.button>

                <motion.button
                  onClick={() => navigatePhoto(1)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <ChevronRight size={24} />
                </motion.button>
              </>
            )}

            {/* 主要照片显示 */}
            <motion.div
              className="text-center"
              key={activeIndex}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative max-w-full max-h-[70vh]">
                <Image
                  src={photos[activeIndex]}
                  alt={`照片 ${activeIndex + 1}`}
                  width={800}
                  height={600}
                  className="max-w-full max-h-full object-contain rounded-lg"
                  priority
                />
              </div>
              <div className="mt-4 text-white">
                <div className="text-sm opacity-80">
                  照片 {activeIndex + 1} / {photos.length}
                </div>
              </div>
            </motion.div>

            {/* 底部缩略图导航 */}
            {photos.length > 1 && (
              <motion.div
                className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <div className="flex space-x-2">
                  {photos.map((photo, index) => (
                    <motion.button
                      key={index}
                      onClick={() => goToPhoto(index)}
                      className={`modal-thumbnail object-cover ${
                        index === activeIndex ? 'active' : ''
                      }`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Image
                        src={photo}
                        alt={`缩略图 ${index + 1}`}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover rounded-md"
                      />
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
