---
globs: *.html,*.tsx,*.jsx
description: 移动端UI模式和组件规范
---

# 移动端UI模式规范

## 布局结构
基于 [fishing_app_prototype.html](mdc:fishing_app_prototype.html) 的设计模式

### 容器规范
```html
<div class="max-w-md mx-auto bg-white shadow-2xl min-h-screen relative">
```
- 最大宽度: max-w-md (448px)
- 居中对齐: mx-auto
- 全屏高度: min-h-screen

### 导航模式
**顶部导航**：
- 渐变背景: `gradient-bg` (ocean-blue渐变)
- 三栏布局: 返回按钮 | 标题 | 功能按钮
- 高度: py-4 (16px上下间距)

**底部标签栏**：
- 固定定位: `fixed bottom-0`
- 5个标签: 首页、图鉴、记录(中央圆形)、排行、我的
- 活跃状态: sunrise-gold色彩

### 卡片组件
**信息卡片**：
```css
.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}
```

**交互卡片**：
- 悬停缩放: `hover:scale-105`
- 过渡动画: `transition-all`
- 圆角统一: rounded-xl (12px)

## 响应式网格
**统计卡片**: `grid-cols-3` - 三等分布局
**照片网格**: `grid-cols-2` - 二等分布局  
**鱼种图鉴**: `grid-cols-auto-fit` - 自适应网格

## 按钮系统
**主要按钮**: 
- 拂晓金背景 + 白色文字
- 阴影效果: `shadow-lg`
- 悬停动画: `hover:scale-105`

**次要按钮**:
- 水波青背景 + 白色文字
- 较小的视觉权重

**禁用状态**:
- 灰色背景: `bg-gray-300`
- 灰色文字: `text-gray-500`
- 禁用指针: `cursor-not-allowed`

## 输入组件
**表单输入框**:
```css
focus:border-aqua-teal focus:outline-none
border border-divider rounded-lg p-3
```

**文本区域**:
- 禁用调整大小: `resize-none`
- 统一样式继承表单输入框

## 动画系统
**页面切换**: `fadeInUp` 0.5s ease-out
**加载状态**: `spin` 1s linear infinite  
**进度条**: `transition-all duration-500`
**按钮交互**: `transition-all` 默认时长

## 间距系统
- 页面边距: px-6 (24px)
- 组件间距: py-4 (16px) 
- 元素间距: space-x-3, space-y-3 (12px)
- 内容边距: p-4 (16px)