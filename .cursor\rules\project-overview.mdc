---
alwaysApply: true
---

# 钓鱼生涯记录APP - 项目概览

## 项目描述
这是一个以AI技术为驱动、以游戏化收集为核心乐趣的钓鱼日志应用。项目当前包含完整的UI高保真原型，展示了所有核心功能的用户界面设计。

## 核心文档
- 产品需求：[开发需求文档.md](mdc:开发需求文档.md)
- 设计规范：[配色方案.md](mdc:配色方案.md)
- UI原型：[fishing_app_prototype.html](mdc:fishing_app_prototype.html)

## 核心功能模块
1. **用户账户与个人档案** - 注册登录、个人信息管理
2. **AI渔获日志** - 核心功能，AI识别鱼种并自动记录
3. **鱼种图鉴** - 游戏化收集系统，解锁新鱼种
4. **地区排行榜** - 基于地理位置的竞争系统

## 技术栈
- 前端：React Native/Flutter（跨平台）
- 后端：Go + Gin框架
- 数据库：PostgreSQL + PostGIS
- AI服务：Fishial.ai鱼种识别 + Clipdrop背景移除