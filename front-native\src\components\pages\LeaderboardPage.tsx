import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { TopNavigation } from '../layout/TopNavigation'
import { globalStyles } from '../../styles'
import { Colors, Spacing, FontSizes, FontWeights } from '../../constants/colors'
import type { NavigationProps } from '../../types'

export function LeaderboardPage({ navigateToPage }: NavigationProps) {
  return (
    <View style={globalStyles.container}>
      <TopNavigation title="排行榜" />
      <View style={styles.content}>
        <Text style={styles.placeholder}>排行榜开发中...</Text>
        <Text style={styles.description}>
          这里将展示各种排行榜数据，包括重量排行、数量排行等
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
  },
  
  placeholder: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.primaryText,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  
  description: {
    fontSize: FontSizes.md,
    color: Colors.secondaryText,
    textAlign: 'center',
    lineHeight: 24,
  },
})
