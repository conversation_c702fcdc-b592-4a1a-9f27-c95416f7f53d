@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-color: #F0F2F5;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow-x: hidden;
  }
}

@layer components {
  /* 钓鱼APP特定样式 */
  .gradient-bg {
    @apply bg-gradient-ocean;
  }
  
  .stats-card {
    @apply bg-gradient-card shadow-card;
  }
  
  .record-button {
    @apply bg-gradient-sunrise shadow-record-button;
  }
  
  .fish-unlocked {
    @apply bg-gradient-card border-2 border-aqua-teal transition-all hover:scale-105 hover:shadow-fish-hover;
  }
  
  .fish-locked {
    @apply bg-gradient-locked border-2 border-divider;
  }
  
  .nav-active {
    @apply text-sunrise-gold;
  }
  
  .nav-inactive {
    @apply text-secondary-text;
  }
  
  .scan-line {
    @apply absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-aqua-teal to-transparent animate-scan-move;
  }
  
  .radar-pulse {
    @apply w-24 h-24 border-2 border-sunrise-gold rounded-full opacity-70 animate-radar-pulse;
  }
  
  .loading-spinner {
    @apply w-5 h-5 border-2 border-divider border-l-aqua-teal rounded-full animate-spin;
  }
  
  .unlock-animation {
    @apply animate-unlock-scale;
  }
  
  .step-content {
    @apply animate-fade-in-up;
  }
  
  .confetti {
    @apply absolute w-2 h-2 bg-sunrise-gold rounded-full animate-confetti-fall;
  }
  
  .career-badge {
    @apply bg-gradient-badge animate-pulse-glow;
  }
  
  .pulse-ring {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-5 h-5 border-2 border-aqua-teal rounded-full opacity-60 animate-pulse-ring;
  }
  
  .timeline-record {
    @apply transition-all border-l-4 border-transparent hover:border-l-aqua-teal hover:translate-x-1;
  }
  
  .fishing-spot-item {
    @apply transition-all hover:translate-x-1 hover:shadow-card;
  }
  
  .photo-item {
    @apply relative aspect-square rounded-lg overflow-hidden border-2 border-divider;
  }
  
  .photo-item img {
    @apply w-full h-full object-cover;
  }
  
  .photo-item .remove-btn {
    @apply absolute -top-2 -right-2 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-white text-xs cursor-pointer;
  }
  
  .modal-thumbnail {
    @apply w-12 h-12 rounded-md cursor-pointer transition-all border-2 border-transparent hover:scale-110;
  }
  
  .modal-thumbnail.active {
    @apply border-sunrise-gold shadow-lg;
  }
  
  /* 布局辅助类 */
  .container-mobile {
    @apply max-w-md mx-auto bg-white shadow-2xl min-h-screen relative;
  }
  
  .page-content {
    @apply transition-all duration-300;
  }
  
  .fish-grid {
    @apply grid grid-cols-4 gap-4;
  }
  
  .fish-card {
    @apply aspect-square rounded-xl flex items-center justify-center relative transition-all cursor-pointer;
  }
}
