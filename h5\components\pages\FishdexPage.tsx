'use client'

import { motion } from 'framer-motion'
import { Book, Star, Lock } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import type { PageType } from '../AppWrapper'

interface FishdexPageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  appState: any
}

export function FishdexPage({ navigateToPage, showFishDetail }: FishdexPageProps) {
  const fishData = [
    {
      id: 'crucian',
      name: '鲫鱼',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=80&h=80&fit=crop&auto=format',
      unlocked: true,
      isNew: true
    },
    {
      id: 'grass-carp',
      name: '草鱼',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=80&h=80&fit=crop&auto=format',
      unlocked: true,
      isNew: false
    },
    {
      id: 'carp',
      name: '鲤鱼',
      image: 'https://images.unsplash.com/photo-1571752726703-5e7d1f6a986d?w=80&h=80&fit=crop&auto=format',
      unlocked: true,
      isNew: false
    },
    ...Array.from({ length: 15 }, (_, i) => ({
      id: `locked-${i}`,
      name: '未知',
      image: '',
      unlocked: false,
      isNew: false
    }))
  ]

  const categories = [
    { id: 'all', name: '全部', active: true },
    { id: 'freshwater', name: '淡水鱼', active: false },
    { id: 'saltwater', name: '海水鱼', active: false },
    { id: 'unlocked', name: '已解锁', active: false }
  ]

  const collectionProgress = {
    unlocked: 3,
    total: 18,
    percentage: 16.7
  }

  return (
    <div className="pb-24">
      <TopNavigation
        title="鱼种图鉴"
        rightIcon="share"
        onRightClick={() => alert('分享图鉴')}
      />

      {/* 收集进度 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-secondary-text">收集进度</span>
            <div className="flex items-center space-x-2">
              <span className="text-sunrise-gold font-bold">{collectionProgress.unlocked}</span>
              <span className="text-secondary-text">/</span>
              <span className="text-secondary-text">{collectionProgress.total}</span>
            </div>
          </div>
          <div className="w-full bg-light-slate rounded-full h-3 mb-2">
            <motion.div 
              className="bg-aqua-teal h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${collectionProgress.percentage}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
          <div className="text-right">
            <span className="text-sm font-medium text-primary-text">
              {collectionProgress.percentage.toFixed(1)}%
            </span>
          </div>
        </div>
      </motion.div>

      {/* 分类筛选 */}
      <motion.div 
        className="px-6 py-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="flex space-x-2 overflow-x-auto">
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-all ${
                category.active 
                  ? 'bg-aqua-teal text-white' 
                  : 'bg-white text-secondary-text border border-divider hover:border-aqua-teal'
              }`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.05 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category.name}
            </motion.button>
          ))}
        </div>
      </motion.div>

      {/* 鱼种网格 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="fish-grid">
          {fishData.map((fish, index) => (
            <motion.div
              key={fish.id}
              className={`fish-card ${
                fish.unlocked ? 'fish-unlocked cursor-pointer' : 'fish-locked'
              }`}
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ 
                delay: 0.5 + index * 0.05,
                type: "spring",
                stiffness: 300,
                damping: 20
              }}
              whileHover={fish.unlocked ? { scale: 1.05 } : {}}
              whileTap={fish.unlocked ? { scale: 0.95 } : {}}
              onClick={() => fish.unlocked && showFishDetail(fish.id)}
            >
              {fish.unlocked ? (
                <>
                  <Image
                    src={fish.image}
                    alt={fish.name}
                    width={64}
                    height={64}
                    className="rounded-lg object-cover"
                  />
                  {fish.isNew && (
                    <motion.div 
                      className="absolute -top-2 -right-2 w-6 h-6 bg-sunrise-gold rounded-full flex items-center justify-center"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ 
                        delay: 0.8 + index * 0.05,
                        type: "spring",
                        stiffness: 400
                      }}
                    >
                      <Star className="text-white" size={12} />
                    </motion.div>
                  )}
                  <div className="absolute bottom-1 left-1 right-1 bg-black bg-opacity-70 text-white text-xs text-center py-1 rounded">
                    {fish.name}
                  </div>
                </>
              ) : (
                <motion.div 
                  className="w-16 h-16 bg-secondary-text bg-opacity-20 rounded-lg flex items-center justify-center"
                  whileHover={{ scale: 1.1 }}
                >
                  <Lock className="text-secondary-text" size={24} />
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* 图鉴说明 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="bg-gradient-to-r from-aqua-teal/10 to-sunrise-gold/10 p-4 rounded-xl border border-aqua-teal/20">
          <div className="flex items-center space-x-2 mb-2">
            <Book className="text-aqua-teal" size={20} />
            <span className="text-sm font-medium text-primary-text">收集提示</span>
          </div>
          <div className="text-xs text-secondary-text leading-relaxed">
            通过AI识别记录新的鱼种来解锁图鉴。每解锁一个新鱼种都会获得经验值和成就奖励！
            继续钓鱼探索，发现更多神秘鱼种。
          </div>
        </div>
      </motion.div>
    </div>
  )
}
