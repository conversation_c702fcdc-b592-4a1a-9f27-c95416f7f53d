{"version": 3, "file": "Device.d.ts", "sourceRoot": "", "sources": ["../src/Device.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAG5C,OAAO,EAAE,UAAU,EAAE,CAAC;AAEtB;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE,OAAiD,CAAC;AAEzE;;;;;;;;;GASG;AACH,eAAO,MAAM,KAAK,EAAE,MAAM,GAAG,IAA2C,CAAC;AAEzE;;;;;;;;;GASG;AACH,eAAO,MAAM,YAAY,EAAE,MAAM,GAAG,IAAkD,CAAC;AAEvF;;;;;;;;;GASG;AACH,eAAO,MAAM,OAAO,KAAiD,CAAC;AAEtE;;;;;;;;GAQG;AACH,eAAO,MAAM,SAAS,EAAE,MAAM,GAAG,IAA+C,CAAC;AAEjF;;;;;;;;;GASG;AACH,eAAO,MAAM,UAAU,EAAE,MAAM,GAAG,IAAwD,CAAC;AAE3F;;;;;;;;;GASG;AACH,eAAO,MAAM,WAAW,EAAE,MAAM,GAAG,IAAyD,CAAC;AAE7F;;;;;;;;;;;GAWG;AACH,eAAO,MAAM,UAAU,EAAE,UAAU,GAAG,IAAgD,CAAC;AAEvF;;GAEG;AACH,eAAO,MAAM,eAAe,EAAE,MAAM,GAAG,IAAqD,CAAC;AAE7F;;;;;;;;;GASG;AACH,eAAO,MAAM,WAAW,EAAE,MAAM,GAAG,IAAiD,CAAC;AAErF;;;;;;;;GAQG;AACH,eAAO,MAAM,yBAAyB,EAAE,MAAM,EAAE,GAAG,IAE3C,CAAC;AAET;;;;;;;GAOG;AACH,eAAO,MAAM,MAAM,EAAE,MAAM,GAAG,IAA4C,CAAC;AAE3E;;;;;;;GAOG;AACH,eAAO,MAAM,SAAS,EAAE,MAAM,GAAG,IAA+C,CAAC;AAEjF;;;;;;;;;GASG;AACH,eAAO,MAAM,SAAS,EAAE,MAAM,GAAG,IAA+C,CAAC;AAEjF;;;;;;;;GAQG;AACH,eAAO,MAAM,iBAAiB,EAAE,MAAM,GAAG,IAAuD,CAAC;AAEjG;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,kBAAkB,EAAE,MAAM,GAAG,IAElC,CAAC;AAET;;;;;;;;;;GAUG;AACH,eAAO,MAAM,gBAAgB,EAAE,MAAM,GAAG,IAEhC,CAAC;AAET;;;;;;;;;;;GAWG;AACH,eAAO,MAAM,UAAU,EAAE,MAAM,GAAG,IAAgD,CAAC;AAEnF;;;;;;;;;;;;;GAaG;AACH,wBAAsB,kBAAkB,IAAI,OAAO,CAAC,UAAU,CAAC,CAK9D;AAED;;;;;;;;;;GAUG;AACH,wBAAsB,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,CAKtD;AAED;;;;;;;;;GASG;AACH,wBAAsB,iBAAiB,IAAI,OAAO,CAAC,MAAM,CAAC,CASzD;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAsB,yBAAyB,IAAI,OAAO,CAAC,OAAO,CAAC,CAKlE;AAED;;;;;;;;;;;GAWG;AACH,wBAAsB,yBAAyB,IAAI,OAAO,CAAC,OAAO,CAAC,CAKlE;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAsB,wBAAwB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,CAKlE;AAED;;;;;;;;;;;;GAYG;AACH,wBAAsB,uBAAuB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAK/E"}