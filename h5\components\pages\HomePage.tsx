'use client'

import { motion } from 'framer-motion'
import { <PERSON>, Bell } from 'lucide-react'
import Image from 'next/image'
import { TopNavigation } from '../layout/TopNavigation'
import { AnimatedButton } from '../common/AnimatedButton'
import type { PageType } from '../AppWrapper'

interface HomePageProps {
  navigateToPage: (page: PageType) => void
  showFishDetail: (fishId: string) => void
  openPhotoModal: (photos: string[], index?: number) => void
  appState: any
}

export function HomePage({ navigateToPage, showFishDetail, openPhotoModal }: HomePageProps) {
  const userStats = [
    { value: '128', label: '总渔获', color: 'text-primary-text' },
    { value: '32', label: '鱼种数', color: 'text-aqua-teal' },
    { value: '15', label: '本月记录', color: 'text-sunrise-gold' }
  ]

  const recentRecords = [
    {
      id: 1,
      fishName: '鲫鱼',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=60&h=60&fit=crop&auto=format',
      weight: '0.8kg',
      length: '25cm',
      time: '今天 14:30',
      location: '东湖钓点',
      isNew: true
    },
    {
      id: 2,
      fishName: '草鱼',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=60&h=60&fit=crop&auto=format',
      weight: '1.2kg',
      length: '35cm',
      time: '昨天 09:15',
      location: '西湖钓点',
      isNew: false
    }
  ]

  const leaderboard = [
    {
      rank: 1,
      name: '钓鱼大师',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '草鱼',
      weight: '2.8kg'
    },
    {
      rank: 2,
      name: '江边老钓',
      avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=32&h=32&fit=crop&crop=face&auto=format',
      fish: '鲤鱼',
      weight: '2.1kg'
    }
  ]

  return (
    <div className="pb-24">
      {/* 顶部导航 */}
      <div className="gradient-bg px-6 py-4 text-white">
        <motion.div 
          className="flex items-center justify-between"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center space-x-3">
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Image
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format"
                alt="头像"
                width={40}
                height={40}
                className="rounded-full border-2 border-sunrise-gold"
              />
            </motion.div>
            <div>
              <h1 className="text-lg font-bold">渔友小明</h1>
              <p className="text-sm opacity-80">钓龄 2年</p>
            </div>
          </div>
          <motion.button 
            className="text-xl"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Bell size={20} />
          </motion.button>
        </motion.div>
      </div>

      {/* 快速统计 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="grid grid-cols-3 gap-3">
          {userStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="stats-card p-4 rounded-xl text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              whileHover={{ scale: 1.05 }}
            >
              <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
              <div className="text-sm text-secondary-text">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* 记录渔获按钮 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <AnimatedButton
          onClick={() => navigateToPage('record')}
          className="w-full py-4 text-lg"
          icon={<Camera size={24} />}
        >
          记录渔获
        </AnimatedButton>
      </motion.div>

      {/* 本周排行榜 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-primary-text">本周排行榜</h3>
            <motion.button 
              onClick={() => navigateToPage('leaderboard')}
              className="text-aqua-teal text-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              查看全部
            </motion.button>
          </div>
          <div className="space-y-3">
            {leaderboard.map((item, index) => (
              <motion.div
                key={item.rank}
                className="flex items-center space-x-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
              >
                <div className={`w-8 h-8 ${item.rank === 1 ? 'bg-sunrise-gold' : 'bg-secondary-text'} rounded-full flex items-center justify-center text-white font-bold text-sm`}>
                  {item.rank}
                </div>
                <Image
                  src={item.avatar}
                  alt="用户"
                  width={32}
                  height={32}
                  className="rounded-full"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-primary-text">{item.name}</div>
                  <div className="text-xs text-secondary-text">{item.fish} {item.weight}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* 最近记录 */}
      <motion.div 
        className="px-6 py-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="bg-white p-4 rounded-xl shadow-sm">
          <h3 className="text-lg font-bold text-primary-text mb-4">最近记录</h3>
          <div className="space-y-4">
            {recentRecords.map((record, index) => (
              <motion.div
                key={record.id}
                className="flex items-start space-x-3 cursor-pointer hover:bg-light-slate rounded-lg p-2 -m-2 transition-colors"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 + index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                onClick={() => showFishDetail('crucian')}
              >
                <Image
                  src={record.image}
                  alt={record.fishName}
                  width={60}
                  height={60}
                  className="rounded-lg object-cover"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-primary-text">{record.fishName}</span>
                    {record.isNew && (
                      <span className="text-xs bg-aqua-teal text-white px-2 py-1 rounded">新解锁</span>
                    )}
                  </div>
                  <div className="text-xs text-secondary-text">重量: {record.weight} | 长度: {record.length}</div>
                  <div className="text-xs text-secondary-text">{record.time} | {record.location}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
