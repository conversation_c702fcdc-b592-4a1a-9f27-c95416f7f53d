import React from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  Image
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { <PERSON>, Bell } from 'lucide-react-native';
import type { PageType } from '../../types';

interface SimpleHomePageProps {
  navigateToPage: (page: PageType) => void;
}

export function SimpleHomePage({ navigateToPage }: SimpleHomePageProps) {
  const insets = useSafeAreaInsets();

  return (
    <ScrollView 
      className="flex-1 bg-light-slate"
      showsVerticalScrollIndicator={false}
      style={{ paddingTop: insets.top }}
    >
      {/* 顶部导航 */}
      <LinearGradient
        colors={['#1A2E40', '#203347']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className="px-6 py-4"
      >
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center space-x-3">
            <TouchableOpacity>
              <Image
                source={{
                  uri: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format'
                }}
                className="w-10 h-10 rounded-full border-2 border-sunrise-gold"
              />
            </TouchableOpacity>
            <View>
              <Text className="text-lg font-bold text-white">渔友小明</Text>
              <Text className="text-sm text-white opacity-80">钓龄 2年</Text>
            </View>
          </View>
          <TouchableOpacity>
            <Bell size={20} color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* 快速统计 */}
      <View className="px-6 py-4">
        <View className="flex-row justify-between">
          <View className="bg-white p-4 rounded-xl flex-1 mx-1 shadow-sm items-center">
            <Text className="text-2xl font-bold text-primary-text">128</Text>
            <Text className="text-sm text-secondary-text">总渔获</Text>
          </View>
          <View className="bg-white p-4 rounded-xl flex-1 mx-1 shadow-sm items-center">
            <Text className="text-2xl font-bold text-aqua-teal">32</Text>
            <Text className="text-sm text-secondary-text">鱼种数</Text>
          </View>
          <View className="bg-white p-4 rounded-xl flex-1 mx-1 shadow-sm items-center">
            <Text className="text-2xl font-bold text-sunrise-gold">15</Text>
            <Text className="text-sm text-secondary-text">本月记录</Text>
          </View>
        </View>
      </View>

      {/* 记录渔获按钮 */}
      <View className="px-6 py-4">
        <TouchableOpacity
          onPress={() => navigateToPage('record')}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#FFC759', '#FFB840']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            className="py-4 px-6 rounded-lg"
          >
            <View className="flex-row items-center justify-center gap-x-2">
              <Camera size={24} color="#1A2E40" />
              <Text className="text-lg font-medium text-ocean-blue">记录渔获</Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {/* 快捷导航 */}
      <View className="px-6 py-4 pb-24">
        <View className="bg-white rounded-xl p-4">
          <Text className="text-lg font-bold text-primary-text mb-4">功能导航</Text>
          <View className="flex-row flex-wrap justify-between">
            <TouchableOpacity
              className="w-[48%] bg-light-slate rounded-lg p-4 mb-3 items-center"
              onPress={() => navigateToPage('fishdex')}
            >
              <Text className="font-medium text-primary-text">鱼种图鉴</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="w-[48%] bg-light-slate rounded-lg p-4 mb-3 items-center"
              onPress={() => navigateToPage('leaderboard')}
            >
              <Text className="font-medium text-primary-text">排行榜</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="w-[48%] bg-light-slate rounded-lg p-4 mb-3 items-center"
              onPress={() => navigateToPage('profile')}
            >
              <Text className="font-medium text-primary-text">个人档案</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="w-[48%] bg-light-slate rounded-lg p-4 mb-3 items-center"
              onPress={() => navigateToPage('career')}
            >
              <Text className="font-medium text-primary-text">钓鱼生涯</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}