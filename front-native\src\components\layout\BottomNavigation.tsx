import React from 'react'
import { View, Text, StyleSheet, Pressable } from 'react-native'
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  runOnJS
} from 'react-native-reanimated'
import { Ionicons } from '@expo/vector-icons'
import { OceanGradient, SunriseGradient } from '../common/GradientBackground'
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../../constants/colors'
import type { PageType } from '../../types'

interface BottomNavigationProps {
  currentPage: PageType
  onNavigate: (page: PageType) => void
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable)

export function BottomNavigation({ currentPage, onNavigate }: BottomNavigationProps) {
  const getNavId = (page: PageType): string => {
    if (page === 'homepage') return 'home'
    if (page === 'fish-detail') return 'fishdex'
    if (page === 'career' || page === 'fishing-spots') return 'profile'
    if (page === 'success') return currentPage
    return page
  }

  const activeNav = getNavId(currentPage)

  const navItems = [
    { 
      id: 'home', 
      icon: 'home' as const, 
      label: '首页', 
      page: 'homepage' as PageType 
    },
    { 
      id: 'fishdex', 
      icon: 'book' as const, 
      label: '图鉴', 
      page: 'fishdex' as PageType 
    },
    { 
      id: 'record', 
      icon: 'add' as const, 
      label: '记录', 
      page: 'record' as PageType, 
      isCenter: true 
    },
    { 
      id: 'leaderboard', 
      icon: 'trophy' as const, 
      label: '排行', 
      page: 'leaderboard' as PageType 
    },
    { 
      id: 'profile', 
      icon: 'person' as const, 
      label: '我的', 
      page: 'profile' as PageType 
    }
  ]

  const NavButton = ({ item }: { item: typeof navItems[0] }) => {
    const scale = useSharedValue(1)
    const isActive = activeNav === item.id

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scale.value }]
    }))

    const handlePressIn = () => {
      scale.value = withSpring(0.9, { damping: 15, stiffness: 300 })
    }

    const handlePressOut = () => {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 })
    }

    const handlePress = () => {
      runOnJS(onNavigate)(item.page)
    }

    if (item.isCenter) {
      return (
        <AnimatedPressable
          style={[animatedStyle, styles.centerButton]}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          onPress={handlePress}
        >
          <SunriseGradient style={styles.centerButtonGradient}>
            <Ionicons 
              name={item.icon} 
              size={28} 
              color={Colors.cardBackground} 
            />
          </SunriseGradient>
        </AnimatedPressable>
      )
    }

    return (
      <AnimatedPressable
        style={[animatedStyle, styles.navButton]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
      >
        <Ionicons 
          name={item.icon} 
          size={24} 
          color={isActive ? Colors.sunriseGold : Colors.secondaryText} 
        />
        <Text style={[
          styles.navLabel,
          { color: isActive ? Colors.sunriseGold : Colors.secondaryText }
        ]}>
          {item.label}
        </Text>
      </AnimatedPressable>
    )
  }

  return (
    <OceanGradient style={styles.container}>
      <View style={styles.navigation}>
        {navItems.map((item) => (
          <NavButton key={item.id} item={item} />
        ))}
      </View>
    </OceanGradient>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingBottom: Spacing.sm,
  },
  
  navigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  
  navButton: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
    paddingVertical: Spacing.xs,
  },
  
  navLabel: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    marginTop: Spacing.xs / 2,
  },
  
  centerButton: {
    position: 'relative',
    marginTop: -20, // 让中心按钮突出
  },
  
  centerButtonGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.recordButton,
  },
})
