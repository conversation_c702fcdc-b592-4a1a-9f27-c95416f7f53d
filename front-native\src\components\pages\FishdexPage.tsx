import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { TopNavigation } from '../layout/TopNavigation'
import { globalStyles } from '../../styles'
import { Colors, Spacing, FontSizes, FontWeights } from '../../constants/colors'
import type { NavigationProps } from '../../types'

export function FishdexPage({ navigateToPage }: NavigationProps) {
  return (
    <View style={globalStyles.container}>
      <TopNavigation title="鱼类图鉴" />
      <View style={styles.content}>
        <Text style={styles.placeholder}>鱼类图鉴开发中...</Text>
        <Text style={styles.description}>
          这里将展示各种鱼类的信息、解锁状态和详细介绍
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
  },
  
  placeholder: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    color: Colors.primaryText,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  
  description: {
    fontSize: FontSizes.md,
    color: Colors.secondaryText,
    textAlign: 'center',
    lineHeight: 24,
  },
})
