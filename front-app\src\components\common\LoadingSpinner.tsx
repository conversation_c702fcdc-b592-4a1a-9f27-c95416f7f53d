import React from 'react';
import { View } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming,
  Easing
} from 'react-native-reanimated';
import { colors, borderRadius } from '../../constants/styles';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export function LoadingSpinner({ size = 'md', color = colors.aquaTeal }: LoadingSpinnerProps) {
  const rotation = useSharedValue(0);
  
  const sizeStyles = {
    sm: { width: 16, height: 16 },
    md: { width: 24, height: 24 }, 
    lg: { width: 32, height: 32 }
  };

  React.useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 1000,
        easing: Easing.linear,
      }),
      -1
    );
  }, [rotation]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  return (
    <Animated.View
      style={[
        sizeStyles[size],
        {
          borderRadius: borderRadius.full,
          borderWidth: 2,
          borderColor: colors.divider,
          borderTopColor: color,
        },
        animatedStyle
      ]}
    />
  );
}